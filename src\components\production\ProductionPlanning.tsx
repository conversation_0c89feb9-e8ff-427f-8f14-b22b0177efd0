
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Calendar, ArrowUpDown } from "lucide-react";

export const ProductionPlanning = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium">Üretim Planları</h3>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Takvim Görünümü
          </Button>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Yeni Plan Oluştur
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Üretim Planı Listesi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative overflow-x-auto rounded-md border">
            <table className="w-full text-sm">
              <thead className="bg-muted text-muted-foreground">
                <tr>
                  <th className="px-4 py-3 text-left">Plan No</th>
                  <th className="px-4 py-3 text-left">Ürün</th>
                  <th className="px-4 py-3 text-left">Miktar</th>
                  <th className="px-4 py-3 text-left">Başlangıç Tarihi</th>
                  <th className="px-4 py-3 text-left">Teslim Tarihi</th>
                  <th className="px-4 py-3 text-left">Durum</th>
                  <th className="px-4 py-3 text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">PRD-2024-028</td>
                  <td className="px-4 py-3">Masa Lambası X-2000</td>
                  <td className="px-4 py-3">250 adet</td>
                  <td className="px-4 py-3">15.06.2024</td>
                  <td className="px-4 py-3">25.06.2024</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                      Planlandı
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Düzenle</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">PRD-2024-027</td>
                  <td className="px-4 py-3">Metal Ayaklı Sehpa</td>
                  <td className="px-4 py-3">100 adet</td>
                  <td className="px-4 py-3">12.06.2024</td>
                  <td className="px-4 py-3">22.06.2024</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      Üretimde
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Düzenle</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">PRD-2024-026</td>
                  <td className="px-4 py-3">Dekoratif Vazo Set</td>
                  <td className="px-4 py-3">500 adet</td>
                  <td className="px-4 py-3">10.06.2024</td>
                  <td className="px-4 py-3">20.06.2024</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                      Malzeme Bekliyor
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Düzenle</Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div className="flex justify-end mt-4">
            <Button variant="outline" size="sm">
              <ArrowUpDown className="mr-2 h-3 w-3" />
              Sırala ve Filtrele
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

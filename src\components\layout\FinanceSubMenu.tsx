
import { useLocation, useNavigate } from "react-router-dom";
import { 
  FileText, Receipt, Wallet, BarChart3, Users, 
  Building, Package, Calculator, DollarSign, BanknoteIcon
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from "@/components/ui/collapsible";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

interface FinanceSubMenuProps {
  openFinanceMenu: boolean;
  setOpenFinanceMenu: (open: boolean) => void;
  iconColor: string;
}

interface FinanceMenuItem {
  id: string;
  title: string;
  path: string;
  icon: React.ReactNode;
  color: string;
}

export const FinanceSubMenu = ({ 
  openFinanceMenu, 
  setOpenFinanceMenu, 
  iconColor 
}: FinanceSubMenuProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Check if the current path starts with /finans to handle all finance subpages
  const isFinancePath = location.pathname.startsWith('/finans');

  // Define finance menu items - simplified version with direct paths
  const financeMenuItems: FinanceMenuItem[] = [
    {
      id: "cari",
      title: "Cari Hesap Yönetimi",
      path: "/finans/cari",
      icon: <Users className="h-4 w-4 text-blue-500" />,
      color: "text-blue-500",
    },
    {
      id: "faturalar",
      title: "Fatura Yönetimi",
      path: "/finans/faturalar",
      icon: <FileText className="h-4 w-4 text-purple-500" />,
      color: "text-purple-500",
    },
    {
      id: "nakit",
      title: "Nakit Yönetimi",
      path: "/finans/nakit",
      icon: <Wallet className="h-4 w-4 text-green-500" />,
      color: "text-green-500",
    },
    {
      id: "banka",
      title: "Banka İşlemleri",
      path: "/finans/banka-kasa",
      icon: <Building className="h-4 w-4 text-cyan-500" />,
      color: "text-cyan-500",
    },
    {
      id: "alacak-borc",
      title: "Alacak ve Borç Takibi",
      path: "/finans/alacak-borc",
      icon: <DollarSign className="h-4 w-4 text-pink-500" />,
      color: "text-pink-500",
    },
    {
      id: "cekSenet",
      title: "Çek ve Senet Takibi",
      path: "/finans/cek-senet",
      icon: <Receipt className="h-4 w-4 text-amber-500" />,
      color: "text-amber-500",
    },
    {
      id: "stok",
      title: "Stok Yönetimi",
      path: "/finans/stok",
      icon: <Package className="h-4 w-4 text-orange-500" />,
      color: "text-orange-500",
    },
    {
      id: "muhasebe",
      title: "Muhasebe İşlemleri",
      path: "/finans/muhasebe",
      icon: <Calculator className="h-4 w-4 text-red-500" />,
      color: "text-red-500",
    },
    {
      id: "raporlar",
      title: "Raporlama",
      path: "/finans/raporlar",
      icon: <BarChart3 className="h-4 w-4 text-indigo-500" />,
      color: "text-indigo-500",
    },
  ];

  // Function to handle navigation without closing the menu
  const handleMenuItemClick = (path: string) => {
    navigate(path);
    // We're NOT calling setOpenFinanceMenu(false) anymore
    // This way the menu stays open after clicking
  };

  return (
    <div>
      <Button
        variant={isFinancePath ? "secondary" : "ghost"}
        className={`w-full justify-between gap-2 mb-1 ${isFinancePath ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : ''}`}
        onClick={() => setOpenFinanceMenu(!openFinanceMenu)}
      >
        <div className="flex items-center gap-2">
          <BanknoteIcon className={`h-4 w-4 ${iconColor}`} />
          Finans
        </div>
        {openFinanceMenu ? 
          <ChevronDown className="h-4 w-4" /> : 
          <ChevronRight className="h-4 w-4" />
        }
      </Button>
      
      {openFinanceMenu && (
        <div className="pl-3 pr-1 py-1 space-y-1">
          {financeMenuItems.map((item) => {
            const isActive = location.pathname.includes(item.path);
            return (
              <Button
                key={item.id}
                variant="ghost"
                size="sm"
                className={`w-full justify-start gap-2 ${isActive ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-200' : ''}`}
                onClick={() => handleMenuItemClick(item.path)}
              >
                {item.icon}
                <span className="text-sm">{item.title}</span>
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );
};

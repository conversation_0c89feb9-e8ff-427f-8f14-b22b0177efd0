
import { Button } from "@/components/ui/button";
import { PlusCircle, Calendar, Settings, FileSpreadsheet } from "lucide-react";
import { useState } from "react";
import { useMaintenanceStore } from "@/stores/maintenanceStore";
import { useToast } from "@/hooks/use-toast";
import { CreateMaintenanceDialog } from "./CreateMaintenanceDialog";
import { CreateScheduleDialog } from "./CreateScheduleDialog";

export const MaintenanceHeader = () => {
  const [isCreateMaintenanceOpen, setIsCreateMaintenanceOpen] = useState(false);
  const [isCreateScheduleOpen, setIsCreateScheduleOpen] = useState(false);
  const { maintenanceItems, maintenanceSchedules, generateDemoData } = useMaintenanceStore();
  const { toast } = useToast();

  // Generate demo data if none exists
  const handleGenerateDemoData = () => {
    if (maintenanceItems.length === 0 && maintenanceSchedules.length === 0) {
      generateDemoData();
      toast({
        title: "Demo Verileri Oluşturuldu",
        description: "Bakım ve onarım için örnek veriler sisteme yüklendi.",
      });
    }
  };

  // Check for demo data
  if (maintenanceItems.length === 0 && maintenanceSchedules.length === 0) {
    handleGenerateDemoData();
  }

  const handleExportData = () => {
    toast({
      title: "Veriler Dışa Aktarıldı",
      description: "Bakım verileri başarıyla dışa aktarıldı.",
    });
  };

  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Bakım & Onarım</h2>
        <p className="text-muted-foreground">
          Bakım takvimlerini yönetin ve maliyetleri takip edin
        </p>
      </div>
      <div className="flex flex-wrap gap-2">
        <Button 
          variant="outline" 
          onClick={handleExportData}
          className="flex items-center gap-2"
        >
          <FileSpreadsheet className="h-4 w-4" />
          Dışa Aktar
        </Button>
        <Button 
          onClick={() => setIsCreateScheduleOpen(true)}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Calendar className="h-4 w-4" />
          Bakım Takvimi Oluştur
        </Button>
        <Button onClick={() => setIsCreateMaintenanceOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Bakım Kaydı
        </Button>
      </div>
      
      <CreateMaintenanceDialog 
        open={isCreateMaintenanceOpen} 
        onOpenChange={setIsCreateMaintenanceOpen}
      />
      
      <CreateScheduleDialog
        open={isCreateScheduleOpen}
        onOpenChange={setIsCreateScheduleOpen}
      />
    </div>
  );
};

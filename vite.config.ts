import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { componentTagger } from 'lovable-tagger'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 8080,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    // Her build için uniquie cache-busting hash oluştur
    rollupOptions: {
      output: {
        manualChunks: undefined,
        entryFileNames: 'assets/[name]-[hash]-nocache.js',
        chunkFileNames: 'assets/[name]-[hash]-nocache.js',
        assetFileNames: 'assets/[name]-[hash]-nocache.[ext]'
      }
    },
    // Source map ekle ki daha kolay debug edebilelim
    sourcemap: true
  }
}));

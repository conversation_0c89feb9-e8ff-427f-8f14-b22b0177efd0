
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { FileDown, Printer, ClipboardList } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";

type PurchaseInvoice = {
  id: string;
  invoiceNumber: string;
  supplier: string;
  issueDate: string;
  dueDate: string;
  amount: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  status: string;
  paymentStatus: string;
};

export const getPurchaseInvoiceColumns = (
  onCreatePaymentOrder: (invoice: any) => void,
  onCreatePaymentInstruction: (invoice: any) => void
): ColumnDef<PurchaseInvoice, any>[] => [
  {
    header: "Fatura No",
    accessorKey: "invoiceNumber",
    enableSorting: true,
    // enableFiltering kaldırıldı
  },
  {
    header: "Tedarikçi",
    accessorKey: "supplier",
    enableSorting: true,
    // enableFiltering kaldırıldı
  },
  {
    header: "Fatura Tarihi",
    accessorKey: "issueDate",
    enableSorting: true,
    // enableFiltering kaldırıldı
  },
  {
    header: "Vade Tarihi",
    accessorKey: "dueDate",
    enableSorting: true,
    // enableFiltering kaldırıldı
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    enableSorting: true,
    // enableFiltering kaldırıldı
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span>{currency} {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "KDV",
    accessorKey: "taxAmount",
    enableSorting: true,
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span>{currency} {row.original.taxAmount.toLocaleString()}</span>;
    }
  },
  {
    header: "Toplam",
    accessorKey: "totalAmount",
    enableSorting: true,
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span className="font-medium">{currency} {row.original.totalAmount.toLocaleString()}</span>;
    }
  },
  {
    header: "Fatura Durumu",
    accessorKey: "status",
    enableSorting: true,
    // enableFiltering kaldırıldı
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant = "default";
      
      if (status === "received") {
        badgeVariant = "success";
      } else if (status === "pending") {
        badgeVariant = "warning";
      }
      
      return <Badge variant={badgeVariant as any}>{status === "received" ? "Alındı" : "Bekliyor"}</Badge>;
    }
  },
  {
    header: "Ödeme Durumu",
    accessorKey: "paymentStatus",
    enableSorting: true,
    // enableFiltering kaldırıldı
    cell: ({ row }) => {
      const status = row.original.paymentStatus;
      let badgeVariant = "default";
      
      if (status === "paid") {
        badgeVariant = "success";
      } else if (status === "pending") {
        badgeVariant = "warning";
      }
      
      return <Badge variant={badgeVariant as any}>{status === "paid" ? "Ödendi" : "Bekliyor"}</Badge>;
    }
  },
  {
    header: "İşlemler",
    id: "actions",
    cell: ({ row }) => {
      const isPending = row.original.paymentStatus === "pending";
      
      return (
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon">
            <FileDown className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <Printer className="h-4 w-4" />
          </Button>
          {isPending && (
            <>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs"
                onClick={() => onCreatePaymentOrder(row.original)}
              >
                Ödeme Emri
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs"
                onClick={() => onCreatePaymentInstruction(row.original)}
              >
                Talimat
              </Button>
            </>
          )}
        </div>
      );
    }
  }
];

export const purchaseInvoicesData = [
  { id: "1", invoiceNumber: "A2024-001", supplier: "ABC Tedarikçi Ltd.", issueDate: "2024-07-05", dueDate: "2024-08-05", amount: 12500.00, taxAmount: 2250.00, totalAmount: 14750.00, currency: "TRY", status: "received", paymentStatus: "paid" },
  { id: "2", invoiceNumber: "A2024-002", supplier: "XYZ Dağıtım A.Ş.", issueDate: "2024-07-08", dueDate: "2024-08-08", amount: 8750.50, taxAmount: 1575.09, totalAmount: 10325.59, currency: "TRY", status: "received", paymentStatus: "pending" },
  { id: "3", invoiceNumber: "A2024-003", supplier: "Teknoloji Partner Ltd.", issueDate: "2024-07-10", dueDate: "2024-08-10", amount: 22800.00, taxAmount: 4104.00, totalAmount: 26904.00, currency: "TRY", status: "received", paymentStatus: "pending" },
  { id: "4", invoiceNumber: "A2024-004", supplier: "Global Satış A.Ş.", issueDate: "2024-07-12", dueDate: "2024-08-12", amount: 5200.00, taxAmount: 936.00, totalAmount: 6136.00, currency: "USD", status: "pending", paymentStatus: "pending" }
];

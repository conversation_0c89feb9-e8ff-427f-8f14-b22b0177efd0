
import { Button } from "@/components/ui/button";
import { UserPlus } from "lucide-react";

interface UserListHeaderProps {
  onAddUser: () => void;
}

export const UserListHeader = ({ onAddUser }: UserListHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <h3 className="text-lg font-medium">Kullanıcı Listesi</h3>
      <Button className="flex items-center gap-2" onClick={onAddUser}>
        <UserPlus className="h-4 w-4" />
        <PERSON><PERSON><PERSON><PERSON><PERSON>
      </Button>
    </div>
  );
};

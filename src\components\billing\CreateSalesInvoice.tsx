import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, addDays } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  CalendarIcon, Plus, Trash2, Search, Save, FileText, 
  Printer, Download, ArrowRight, Eye, CreditCard, Calculator,
  Building2, UserPlus, PackagePlus, BadgePercent
} from "lucide-react";
import { CustomerSelector } from "./CustomerSelector";
import { ProductSelector } from "./ProductSelector";
import { CariHesap } from "@/types/cari";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface InvoiceItem {
  id: string;
  code?: string;
  name: string;
  description?: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  discountRate: number;
  discountAmount: number;
  vatRate: number;
  vatAmount: number;
  total: number;
  netTotal: number;
}

// ProductType tanımı ile uyumlu olması için
interface ProductType {
  id: string;
  kod: string;
  isim: string;
  aciklama?: string;
  birim: string;
  fiyat: number;
  kdv_oran: number;
  stok: number | null;
}

export interface InvoiceFormData {
  invoiceNumber: string;
  invoiceType: string;
  invoiceDate: Date;
  dueDate: Date;
  customer: CariHesap | null;
  waybillNumber?: string;
  waybillDate?: Date;
  items: InvoiceItem[];
  currency: string;
  notes?: string;
  subtotal: number;
  totalDiscount: number;
  totalVat: number;
  grandTotal: number;
  paymentMethod?: string;
  status: "draft" | "approved";
}

export const CreateSalesInvoice = () => {
  const [activeTab, setActiveTab] = useState("details");
  const [stockTracking, setStockTracking] = useState<"enabled" | "disabled">("enabled");
  
  // Default values
  const defaultInvoiceNumber = `FT-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, "0")}${String(new Date().getDate()).padStart(2, "0")}-${String(Math.floor(Math.random() * 1000)).padStart(3, "0")}`;
  
  // Form state with initial values
  const [invoiceData, setInvoiceData] = useState<InvoiceFormData>({
    invoiceNumber: defaultInvoiceNumber,
    invoiceType: "sales",
    invoiceDate: new Date(),
    dueDate: addDays(new Date(), 30),
    customer: null,
    items: [],
    currency: "TRY",
    subtotal: 0,
    totalDiscount: 0,
    totalVat: 0,
    grandTotal: 0,
    status: "draft"
  });
  
  // Temporary state for the new item input row
  const [newItem, setNewItem] = useState<Partial<InvoiceItem>>({
    quantity: 1,
    unit: "Adet",
    unitPrice: 0,
    discountRate: 0,
    discountAmount: 0,
    vatRate: 18
  });

  const [vatRates] = useState(["0", "1", "8", "10", "18", "20"]);
  const [units] = useState(["Adet", "Kg", "Metre", "Litre", "Paket", "Kutu", "Saat", "Gün"]);
  const [currencies] = useState(["TRY", "USD", "EUR", "GBP"]);
  const [paymentMethods] = useState(["Nakit", "Banka Havalesi", "Kredi Kartı", "Çek/Senet", "Açık Hesap"]);
  const [invoiceTypes] = useState(["sales", "proforma", "export"]);

  // Calculate all totals whenever items change
  useEffect(() => {
    calculateTotals();
  }, [invoiceData.items]);

  // Handler for customer selection
  const handleCustomerSelect = (customer: CariHesap | null) => {
    setInvoiceData(prev => {
      // Default due date based on customer payment terms (example value)
      const paymentTermDays = 30; // This would come from customer data in a real app
      return {
        ...prev,
        customer,
        dueDate: customer ? addDays(prev.invoiceDate, paymentTermDays) : addDays(new Date(), 30)
      };
    });
  };

  // Calculate totals for invoice
  const calculateTotals = () => {
    const items = invoiceData.items;
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const totalDiscount = items.reduce((sum, item) => sum + item.discountAmount, 0);
    const totalVat = items.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = items.reduce((sum, item) => sum + item.total, 0);

    setInvoiceData(prev => ({
      ...prev,
      subtotal,
      totalDiscount,
      totalVat,
      grandTotal
    }));
  };
  
  // Add item to invoice
  const addInvoiceItem = () => {
    if (!newItem.name) {
      toast.error("Ürün/Hizmet adı girmelisiniz");
      return;
    }
    
    const id = crypto.randomUUID();
    const quantity = newItem.quantity || 0;
    const unitPrice = newItem.unitPrice || 0;
    const discountRate = newItem.discountRate || 0;
    const discountAmount = (quantity * unitPrice) * (discountRate / 100);
    const netAmount = (quantity * unitPrice) - discountAmount;
    const vatRate = newItem.vatRate || 0;
    const vatAmount = netAmount * (vatRate / 100);
    const total = netAmount + vatAmount;
    
    const item: InvoiceItem = {
      id,
      name: newItem.name,
      description: newItem.description || "",
      code: newItem.code,
      quantity,
      unit: newItem.unit || "Adet",
      unitPrice,
      discountRate,
      discountAmount,
      vatRate,
      vatAmount,
      netTotal: netAmount,
      total
    };
    
    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, item]
    }));
    
    // Reset new item form
    setNewItem({
      quantity: 1,
      unit: "Adet",
      unitPrice: 0,
      discountRate: 0,
      discountAmount: 0,
      vatRate: 18
    });
  };
  
  // Remove item from invoice
  const removeInvoiceItem = (id: string) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id)
    }));
  };

  // Handle product selection from the product selector
  const handleProductSelect = (product: ProductType) => {
    if (product) {
      setNewItem({
        code: product.kod,
        name: product.isim,
        description: product.aciklama,
        unit: product.birim || "Adet",
        unitPrice: product.fiyat || 0,
        vatRate: product.kdv_oran || 18,
        quantity: newItem.quantity || 1,
        discountRate: newItem.discountRate || 0,
        discountAmount: newItem.discountAmount || 0,
      });
    }
  };

  // Format currency amounts
  const formatCurrency = (amount: number): string => {
    return amount.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  };

  // Save invoice
  const saveInvoice = (status: "draft" | "approved") => {
    if (!invoiceData.customer) {
      toast.error("Lütfen bir müşteri seçin");
      return;
    }

    if (invoiceData.items.length === 0) {
      toast.error("Faturaya en az bir kalem eklemelisiniz");
      return;
    }

    const finalInvoice = {
      ...invoiceData,
      status
    };

    // In a real app, this would save to a database
    console.log("Saving invoice:", finalInvoice);
    
    toast.success(
      status === "draft" 
        ? "Fatura taslak olarak kaydedildi" 
        : "Fatura başarıyla oluşturuldu", 
      { 
        description: `Fatura No: ${invoiceData.invoiceNumber}`
      }
    );
  };

  // Preview invoice
  const previewInvoice = () => {
    if (!invoiceData.customer) {
      toast.error("Lütfen bir müşteri seçin");
      return;
    }

    if (invoiceData.items.length === 0) {
      toast.error("Faturaya en az bir kalem eklemelisiniz");
      return;
    }

    // In a real app, this would show a preview modal
    toast.info("Fatura önizleme özelliği", {
      description: "Bu özellik henüz geliştirme aşamasında"
    });
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Fatura Bilgileri
          </TabsTrigger>
          <TabsTrigger value="items" className="flex items-center gap-2">
            <PackagePlus className="h-4 w-4" />
            Fatura Kalemleri
          </TabsTrigger>
          <TabsTrigger value="payment" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Ödeme Bilgileri
          </TabsTrigger>
          <TabsTrigger value="notes" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Notlar ve Ek Bilgiler
          </TabsTrigger>
        </TabsList>

        {/* Fatura Bilgileri Tab */}
        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <FileText className="h-5 w-5 text-primary" />
                Fatura Detayları
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="invoiceType">Fatura Tipi</Label>
                      <Select 
                        value={invoiceData.invoiceType} 
                        onValueChange={(value) => setInvoiceData({...invoiceData, invoiceType: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Fatura Tipi Seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sales">Satış Faturası</SelectItem>
                          <SelectItem value="proforma">Proforma Fatura</SelectItem>
                          <SelectItem value="export">İhracat Faturası</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="invoiceNumber">Fatura No</Label>
                      <Input 
                        id="invoiceNumber" 
                        value={invoiceData.invoiceNumber}
                        onChange={(e) => setInvoiceData({...invoiceData, invoiceNumber: e.target.value})}
                        readOnly
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="invoiceDate">Fatura Tarihi</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {invoiceData.invoiceDate ? format(invoiceData.invoiceDate, 'dd.MM.yyyy') : 'Tarih Seçin'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={invoiceData.invoiceDate}
                            onSelect={(date) => date && setInvoiceData({...invoiceData, invoiceDate: date})}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dueDate">Vade Tarihi</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {invoiceData.dueDate ? format(invoiceData.dueDate, 'dd.MM.yyyy') : 'Tarih Seçin'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={invoiceData.dueDate}
                            onSelect={(date) => date && setInvoiceData({...invoiceData, dueDate: date})}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="waybillNumber">İrsaliye No</Label>
                      <Input 
                        id="waybillNumber" 
                        placeholder="İrsaliye numarası" 
                        value={invoiceData.waybillNumber || ""}
                        onChange={(e) => setInvoiceData({...invoiceData, waybillNumber: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="waybillDate">İrsaliye Tarihi</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {invoiceData.waybillDate ? format(invoiceData.waybillDate, 'dd.MM.yyyy') : 'Tarih Seçin'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={invoiceData.waybillDate}
                            onSelect={(date) => date && setInvoiceData({...invoiceData, waybillDate: date})}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">Para Birimi</Label>
                    <Select 
                      value={invoiceData.currency} 
                      onValueChange={(value) => setInvoiceData({...invoiceData, currency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Para Birimi Seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.map(currency => (
                          <SelectItem key={currency} value={currency}>
                            {currency === "TRY" ? "₺ TL" : 
                             currency === "USD" ? "$ USD" :
                             currency === "EUR" ? "€ EUR" : "£ GBP"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="flex items-center justify-between">
                      <span>Müşteri Bilgileri</span>
                      <Button variant="ghost" size="sm" className="h-6 gap-1 text-xs" onClick={() => toast.info("Yeni Müşteri", { description: "Yeni müşteri ekleme özelliği geliştiriliyor" })}>
                        <UserPlus className="h-3 w-3" />
                        Yeni Ekle
                      </Button>
                    </Label>
                    <CustomerSelector 
                      selectedCustomer={invoiceData.customer} 
                      onCustomerSelect={handleCustomerSelect} 
                    />
                  </div>
                  
                  {invoiceData.customer && (
                    <Card className="border-dashed bg-muted/50">
                      <CardContent className="p-4 space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{invoiceData.customer.unvan}</span>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <span className="text-muted-foreground">Vergi No:</span>
                            <span className="ml-2">{invoiceData.customer.vknTckn}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Vergi Dairesi:</span>
                            <span className="ml-2">{invoiceData.customer.vergiDairesi || "-"}</span>
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Adres:</span>
                          <span className="ml-2">
                            {invoiceData.customer.adres || "-"}
                            {invoiceData.customer.ilce && `, ${invoiceData.customer.ilce}`}
                            {invoiceData.customer.il && `, ${invoiceData.customer.il}`}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <span className="text-muted-foreground">Telefon:</span>
                            <span className="ml-2">{invoiceData.customer.telefon || "-"}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">E-posta:</span>
                            <span className="ml-2">{invoiceData.customer.email || "-"}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="mt-4">
                    <Label htmlFor="stockTracking" className="mb-2 block">Stok İşlemi</Label>
                    <RadioGroup
                      value={stockTracking}
                      onValueChange={(value) => setStockTracking(value as "enabled" | "disabled")}
                      className="grid grid-cols-2 gap-4"
                    >
                      <div className={cn(
                        "flex items-start space-x-2 border rounded-md p-3",
                        stockTracking === "enabled" ? "border-primary bg-primary/5" : ""
                      )}>
                        <RadioGroupItem value="enabled" id="stock-tracking-enabled" className="mt-1" />
                        <div className="space-y-1">
                          <Label htmlFor="stock-tracking-enabled" className="font-bold">STOK ÇIKIŞI YAPILSIN</Label>
                          <p className="text-xs text-muted-foreground">
                            Stok çıkışı fatura ile otomatik yapılır
                          </p>
                        </div>
                      </div>
                      <div className={cn(
                        "flex items-start space-x-2 border rounded-md p-3",
                        stockTracking === "disabled" ? "border-primary bg-primary/5" : ""
                      )}>
                        <RadioGroupItem value="disabled" id="stock-tracking-disabled" className="mt-1" />
                        <div className="space-y-1">
                          <Label htmlFor="stock-tracking-disabled" className="font-bold">STOK ÇIKIŞI YAPILMASIN</Label>
                          <p className="text-xs text-muted-foreground">
                            Hizmet/stok takibi gerektirmeyen kalemler için
                          </p>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("notes")}>
              Notlar ve Ek Bilgiler
            </Button>
            <Button onClick={() => setActiveTab("items")} className="gap-2">
              Fatura Kalemlerine Geç
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </TabsContent>

        {/* Fatura Kalemleri Tab */}
        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <PackagePlus className="h-5 w-5 text-primary" />
                Fatura Kalemleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-8 gap-2 pb-2 font-medium border-b text-sm">
                  <div className="col-span-2">ÜRÜN / HİZMET</div>
                  <div>MİKTAR</div>
                  <div>BİRİM</div>
                  <div>BR. FİYAT</div>
                  <div>İSKONTO</div>
                  <div>KDV</div>
                  <div>TUTAR</div>
                </div>
                
                {invoiceData.items.map((item) => (
                  <div key={item.id} className="grid grid-cols-8 gap-2 items-center text-sm py-1 border-b border-dashed">
                    <div className="col-span-2">
                      <div className="font-medium">{item.name}</div>
                      {item.description && <div className="text-xs text-muted-foreground">{item.description}</div>}
                      {item.code && <div className="text-xs text-muted-foreground">Kod: {item.code}</div>}
                    </div>
                    <div>{item.quantity}</div>
                    <div>{item.unit}</div>
                    <div>{formatCurrency(item.unitPrice)} {invoiceData.currency}</div>
                    <div className="text-sm">
                      <div>{item.discountRate}%</div>
                      {item.discountAmount > 0 && (
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(item.discountAmount)} {invoiceData.currency}
                        </div>
                      )}
                    </div>
                    <div className="text-sm">
                      <div>%{item.vatRate}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatCurrency(item.vatAmount)} {invoiceData.currency}
                      </div>
                    </div>
                    <div className="font-medium">{formatCurrency(item.total)} {invoiceData.currency}</div>
                    <div className="flex justify-end">
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => removeInvoiceItem(item.id)}
                        className="h-7 w-7"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                {/* New Item Input Row */}
                <div className="grid grid-cols-8 gap-2 items-center pt-2 border-t">
                  <div className="col-span-2">
                    <ProductSelector 
                      selectedProduct={newItem} 
                      onProductSelect={handleProductSelect}
                      onChange={(value) => setNewItem({ ...newItem, name: value })}
                      value={newItem.name || ''}
                    />
                  </div>
                  <div>
                    <Input 
                      type="number"
                      value={newItem.quantity}
                      onChange={(e) => setNewItem({ 
                        ...newItem, 
                        quantity: parseFloat(e.target.value) || 0 
                      })}
                      className="h-9"
                    />
                  </div>
                  <div>
                    <Select 
                      value={newItem.unit}
                      onValueChange={value => setNewItem({ ...newItem, unit: value })}
                    >
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder="Birim" />
                      </SelectTrigger>
                      <SelectContent>
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Input 
                      type="number"
                      value={newItem.unitPrice}
                      onChange={(e) => setNewItem({ 
                        ...newItem, 
                        unitPrice: parseFloat(e.target.value) || 0 
                      })}
                      className="h-9"
                    />
                  </div>
                  <div>
                    <div className="flex">
                      <Input 
                        type="number"
                        value={newItem.discountRate}
                        onChange={(e) => setNewItem({ 
                          ...newItem, 
                          discountRate: parseFloat(e.target.value) || 0 
                        })}
                        className="h-9"
                      />
                      <div className="flex items-center justify-center ml-1 text-sm font-medium">%</div>
                    </div>
                  </div>
                  <div>
                    <Select 
                      value={newItem.vatRate?.toString()}
                      onValueChange={value => setNewItem({ 
                        ...newItem, 
                        vatRate: parseInt(value) 
                      })}
                    >
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder="KDV" />
                      </SelectTrigger>
                      <SelectContent>
                        {vatRates.map(rate => (
                          <SelectItem key={rate} value={rate}>%{rate}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="relative">
                    <Input 
                      type="text"
                      value={(newItem.quantity && newItem.unitPrice) ? 
                        formatCurrency(newItem.quantity * newItem.unitPrice) : '0,00'}
                      readOnly
                      className="pr-10 h-9"
                    />
                    <span className="absolute right-3 top-2">{invoiceData.currency}</span>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={addInvoiceItem}
                      disabled={!newItem.name}
                      className="h-9 gap-1"
                    >
                      <Plus className="h-4 w-4" />
                      Ekle
                    </Button>
                  </div>
                </div>
                
                <div className="flex justify-between">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="h-7 gap-1 text-xs" 
                    onClick={() => {
                      setNewItem({
                        ...newItem,
                        description: newItem.description ? `${newItem.description}\n` : "Detay: "
                      });
                    }}
                  >
                    <Plus className="h-3 w-3" /> Kalem açıklaması ekle
                  </Button>
                  
                  {newItem.description && (
                    <div className="col-span-8 my-2">
                      <Textarea 
                        placeholder="Kalem için açıklama" 
                        value={newItem.description}
                        onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                        className="h-20 text-sm"
                      />
                    </div>
                  )}
                </div>
                
                {/* Totals Section */}
                <div className="grid grid-cols-2 gap-4 pt-4 mt-4 border-t">
                  <div></div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium">ARA TOPLAM</div>
                      <div className="text-right">
                        <div className="text-base font-medium">{formatCurrency(invoiceData.subtotal)} {invoiceData.currency}</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium">TOPLAM İSKONTO</div>
                      <div className="text-right">
                        <div className="text-base font-medium">{formatCurrency(invoiceData.totalDiscount)} {invoiceData.currency}</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium">TOPLAM KDV</div>
                      <div className="text-right">
                        <div className="text-base font-medium">{formatCurrency(invoiceData.totalVat)} {invoiceData.currency}</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center pt-2 border-t">
                      <div className="text-base font-bold">GENEL TOPLAM</div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatCurrency(invoiceData.grandTotal)} {invoiceData.currency}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("details")}>
              Fatura Bilgilerine Dön
            </Button>
            <Button onClick={() => setActiveTab("payment")} className="gap-2">
              Ödeme Bilgilerine Geç
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </TabsContent>

        {/* Ödeme Bilgileri Tab */}
        <TabsContent value="payment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-primary" />
                Ödeme Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Ödeme Şekli</Label>
                    <Select 
                      value={invoiceData.paymentMethod} 
                      onValueChange={(value) => setInvoiceData({...invoiceData, paymentMethod: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Ödeme Şekli Seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map(method => (
                          <SelectItem key={method} value={method}>{method}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="p-4 border rounded-md bg-muted/50">
                    <div className="text-sm">
                      <div className="font-medium mb-2">Fatura Özeti</div>
                      <div className="grid grid-cols-2 gap-y-2">
                        <div className="text-muted-foreground">Fatura No:</div>
                        <div>{invoiceData.invoiceNumber}</div>
                        
                        <div className="text-muted-foreground">Müşteri:</div>
                        <div>{invoiceData.customer?.unvan || "-"}</div>
                        
                        <div className="text-muted-foreground">Toplam Tutar:</div>
                        <div className="font-medium">{formatCurrency(invoiceData.grandTotal)} {invoiceData.currency}</div>
                        
                        <div className="text-muted-foreground">Düzenleme Tarihi:</div>
                        <div>{format(invoiceData.invoiceDate, 'dd.MM.yyyy')}</div>
                        
                        <div className="text-muted-foreground">Vade Tarihi:</div>
                        <div>{format(invoiceData.dueDate, 'dd.MM.yyyy')}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="rounded-md border bg-muted/30 p-4">
                    <div className="flex items-center justify-center mb-3">
                      <div className="text-lg font-medium text-center">Ödeme Tutarı</div>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-3xl font-bold">
                        {formatCurrency(invoiceData.grandTotal)} {invoiceData.currency}
                      </div>
                    </div>
                    <div className="flex justify-center mt-3">
                      <Button 
                        className="gap-2" 
                        onClick={() => toast.info("Ödeme Al", { description: "Ödeme alma ekranı geliştiriliyor" })}
                      >
                        <Calculator className="h-4 w-4" />
                        Hemen Tahsilat Yap
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("items")}>
              Fatura Kalemlerine Dön
            </Button>
            <Button onClick={() => setActiveTab("notes")} className="gap-2">
              Notlar ve Ek Bilgilere Geç
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </TabsContent>

        {/* Notlar ve Ek Bilgiler Tab */}
        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <FileText className="h-5 w-5 text-primary" />
                Notlar ve Ek Bilgiler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notes">Fatura Notu</Label>
                  <Textarea 
                    id="notes" 
                    placeholder="Faturaya özel notlar..."
                    value={invoiceData.notes || ""}
                    onChange={(e) => setInvoiceData({...invoiceData, notes: e.target.value})}
                    className="min-h-32"
                  />
                  <p className="text-xs text-muted-foreground">
                    Bu not faturanın alt kısmında görüntülenecektir.
                  </p>
                </div>

                <div className="border-t pt-4 mt-4">
                  <div className="font-medium mb-2">Fatura Şablonları</div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Button 
                      variant="outline" 
                      className="h-auto py-3 px-4 flex-col items-center justify-center gap-2 text-center"
                      onClick={() => toast.info("Şablon Özelliği", { description: "Şablon özelliği geliştiriliyor" })}
                    >
                      <FileText className="h-8 w-8 text-primary/70" />
                      <span className="text-xs font-normal">Standart</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="h-auto py-3 px-4 flex-col items-center justify-center gap-2 text-center"
                      onClick={() => toast.info("Şablon Özelliği", { description: "Şablon özelliği geliştiriliyor" })}
                    >
                      <FileText className="h-8 w-8 text-blue-500/70" />
                      <span className="text-xs font-normal">Kurumsal</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="h-auto py-3 px-4 flex-col items-center justify-center gap-2 text-center"
                      onClick={() => toast.info("Şablon Özelliği", { description: "Şablon özelliği geliştiriliyor" })}
                    >
                      <FileText className="h-8 w-8 text-green-500/70" />
                      <span className="text-xs font-normal">Modern</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="h-auto py-3 px-4 flex-col items-center justify-center gap-2 text-center"
                      onClick={() => toast.info("Özel Şablon", { description: "Özel şablon oluşturma özelliği geliştiriliyor" })}
                    >
                      <Plus className="h-8 w-8 text-muted-foreground" />
                      <span className="text-xs font-normal">Yeni Şablon</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("payment")}>
              Ödeme Bilgilerine Dön
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => previewInvoice()} className="gap-2">
                <Eye className="h-4 w-4" />
                Önizle
              </Button>
              <Button onClick={() => saveInvoice("approved")} className="gap-2">
                <Save className="h-4 w-4" />
                Faturayı Kaydet
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="flex flex-wrap justify-between gap-2 pt-4 border-t">
        <div className="flex flex-wrap gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => saveInvoice("draft")}
            className="h-9 gap-1"
          >
            <Save className="h-4 w-4" />
            Taslak Olarak Kaydet
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={previewInvoice}
            className="h-9 gap-1"
          >
            <Eye className="h-4 w-4" />
            Önizle
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button 
            variant="outline" 
            size="sm"
            className="h-9 gap-1"
            onClick={() => toast.info("Yazdırma", { description: "Yazdırma özelliği geliştiriliyor" })}
          >
            <Printer className="h-4 w-4" />
            Yazdır
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            className="h-9 gap-1"
            onClick={() => toast.info("PDF İndir", { description: "PDF indirme özelliği geliştiriliyor" })}
          >
            <Download className="h-4 w-4" />
            PDF İndir
          </Button>
          <Button 
            size="sm"
            onClick={() => saveInvoice("approved")}
            className="h-9 gap-1"
          >
            <Save className="h-4 w-4" />
            Faturayı Kaydet
          </Button>
        </div>
      </div>
    </div>
  );
};

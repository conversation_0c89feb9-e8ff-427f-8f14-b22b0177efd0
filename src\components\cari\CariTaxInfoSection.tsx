
import { TaxIdentificationField } from "./sections/TaxIdentificationField";
import { TaxOfficeField } from "./sections/TaxOfficeField";

interface CariTaxInfoSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

export const CariTaxInfoSection = ({ formData, updateFormData, errors }: CariTaxInfoSectionProps) => {
  return (
    <div className="space-y-2 bg-white p-3 rounded-lg border shadow-sm">
      <h4 className="text-md font-semibold text-primary border-b pb-1">Ver<PERSON> Bilgileri</h4>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        <TaxIdentificationField 
          value={formData.vknTckn || ""}
          onChange={(value) => updateFormData("vknTckn", value)}
          error={errors.vknTckn}
        />
        
        <TaxOfficeField 
          value={formData.vergiDairesi || ""}
          onChange={(value) => updateFormData("vergiDairesi", value)}
          error={errors.vergiDairesi}
        />
      </div>
    </div>
  );
};

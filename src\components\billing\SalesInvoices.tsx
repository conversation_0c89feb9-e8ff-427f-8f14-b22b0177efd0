
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { EnhancedDataTable } from "./EnhancedDataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { NewSalesInvoiceDialog } from "./NewSalesInvoiceDialog";

// Demo data
const salesInvoicesData = [
  { id: "1", invoiceNumber: "S2024-001", customer: "Müşteri A Ltd. Şti.", issueDate: "2024-07-02", dueDate: "2024-08-02", amount: 18500.00, taxAmount: 3330.00, totalAmount: 21830.00, currency: "TRY", status: "sent", paymentStatus: "paid" },
  { id: "2", invoiceNumber: "S2024-002", customer: "Müşteri B A.Ş.", issueDate: "2024-07-05", dueDate: "2024-08-05", amount: 12300.50, taxAmount: 2214.09, totalAmount: 14514.59, currency: "TRY", status: "sent", paymentStatus: "partially_paid" },
  { id: "3", invoiceNumber: "S2024-003", customer: "Müşteri C Holding", issueDate: "2024-07-08", dueDate: "2024-08-08", amount: 28700.00, taxAmount: 5166.00, totalAmount: 33866.00, currency: "TRY", status: "sent", paymentStatus: "pending" },
  { id: "4", invoiceNumber: "S2024-004", customer: "Müşteri D GmbH", issueDate: "2024-07-10", dueDate: "2024-08-10", amount: 7500.00, taxAmount: 0, totalAmount: 7500.00, currency: "EUR", status: "draft", paymentStatus: "pending" }
];

// Column definitions
const salesInvoicesColumns = [
  {
    header: "Fatura No",
    accessorKey: "invoiceNumber",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Müşteri",
    accessorKey: "customer",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Fatura Tarihi",
    accessorKey: "issueDate",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Vade Tarihi",
    accessorKey: "dueDate",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    enableSorting: true,
    enableFiltering: true,
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span>{currency} {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "KDV",
    accessorKey: "taxAmount",
    enableSorting: true,
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span>{currency} {row.original.taxAmount.toLocaleString()}</span>;
    }
  },
  {
    header: "Toplam",
    accessorKey: "totalAmount",
    enableSorting: true,
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span className="font-medium">{currency} {row.original.totalAmount.toLocaleString()}</span>;
    }
  },
  {
    header: "Fatura Durumu",
    accessorKey: "status",
    enableSorting: true,
    enableFiltering: true,
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant: "default" | "success" | "warning" = "default";
      
      if (status === "sent") {
        badgeVariant = "success";
      } else if (status === "draft") {
        badgeVariant = "warning";
      }
      
      return <Badge variant={badgeVariant}>{status === "sent" ? "Gönderildi" : "Taslak"}</Badge>;
    }
  },
  {
    header: "Tahsilat Durumu",
    accessorKey: "paymentStatus",
    enableSorting: true,
    enableFiltering: true,
    cell: ({ row }) => {
      const status = row.original.paymentStatus;
      let badgeVariant: "default" | "success" | "warning" = "default";
      let label = "";
      
      if (status === "paid") {
        badgeVariant = "success";
        label = "Tahsil Edildi";
      } else if (status === "pending") {
        badgeVariant = "warning";
        label = "Bekliyor";
      } else if (status === "partially_paid") {
        badgeVariant = "default";
        label = "Kısmi Tahsilat";
      }
      
      return <Badge variant={badgeVariant}>{label}</Badge>;
    }
  }
];

export const SalesInvoices = () => {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [newInvoiceDialogOpen, setNewInvoiceDialogOpen] = useState(false);
  
  // Filter data based on selected status
  const filteredData = statusFilter 
    ? salesInvoicesData.filter(invoice => invoice.paymentStatus === statusFilter)
    : salesInvoicesData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={statusFilter === null ? "default" : "outline"}
            onClick={() => setStatusFilter(null)}
          >
            Tümü
          </Button>
          <Button 
            variant={statusFilter === "pending" ? "default" : "outline"}
            onClick={() => setStatusFilter("pending")}
          >
            Tahsilatı Bekleyenler
          </Button>
          <Button 
            variant={statusFilter === "paid" ? "default" : "outline"}
            onClick={() => setStatusFilter("paid")}
          >
            Tahsil Edilenler
          </Button>
          <Button 
            variant={statusFilter === "partially_paid" ? "default" : "outline"}
            onClick={() => setStatusFilter("partially_paid")}
          >
            Kısmi Tahsilatlar
          </Button>
        </div>
        
        <Button onClick={() => setNewInvoiceDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Satış Faturası
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Satış Faturaları</CardTitle>
        </CardHeader>
        <CardContent>
          <EnhancedDataTable 
            columns={salesInvoicesColumns} 
            data={filteredData} 
            title="Satış Faturaları"
          />
        </CardContent>
      </Card>
      
      <NewSalesInvoiceDialog 
        open={newInvoiceDialogOpen}
        onOpenChange={setNewInvoiceDialogOpen}
      />
    </div>
  );
};

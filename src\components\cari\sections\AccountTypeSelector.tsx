
import { AlertCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface AccountTypeSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  error?: string;
}

export const AccountTypeSelector = ({ value, onValueChange, error }: AccountTypeSelectorProps) => {
  return (
    <div className="space-y-1">
      <RadioGroup 
        value={value} 
        onValueChange={onValueChange}
        className="flex space-x-4"
      >
        <div className="flex items-center space-x-2 border p-1 rounded-md">
          <RadioGroupItem value="120-Alıcı" id="alici" />
          <Label htmlFor="alici" className="flex-1 text-sm">Alıcı (120)</Label>
        </div>
        <div className="flex items-center space-x-2 border p-1 rounded-md">
          <RadioGroupItem value="320-Satıcı" id="satici" />
          <Label htmlFor="satici" className="flex-1 text-sm">Satıcı (320)</Label>
        </div>
      </RadioGroup>
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
};

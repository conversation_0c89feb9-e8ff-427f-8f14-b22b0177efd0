
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useMaintenanceStore } from "@/stores/maintenanceStore";
import { useVehicleStore } from "@/stores/vehicleStore";
import { useToast } from "@/hooks/use-toast";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface CreateScheduleD<PERSON>ogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateScheduleDialog = ({ open, onOpenChange }: CreateScheduleDialogProps) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [vehicleId, setVehicleId] = useState("");
  const [frequency, setFrequency] = useState<"daily" | "weekly" | "monthly" | "quarterly" | "yearly" | "custom">("monthly");
  const [intervalDays, setIntervalDays] = useState("");
  const [nextDate, setNextDate] = useState<Date | undefined>(new Date());
  const [reminderDays, setReminderDays] = useState("3");
  const [estimatedCost, setEstimatedCost] = useState("");

  const { addMaintenanceSchedule } = useMaintenanceStore();
  const { vehicles } = useVehicleStore();
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title || !vehicleId || !nextDate) {
      toast({
        title: "Hata",
        description: "Lütfen gerekli alanları doldurun.",
        variant: "destructive",
      });
      return;
    }

    // Create a new maintenance schedule
    const scheduleData = {
      title,
      description,
      vehicleId,
      frequency,
      intervalDays: frequency === 'custom' ? parseInt(intervalDays) : undefined,
      nextDate: nextDate.toISOString(),
      reminderDays: parseInt(reminderDays) || 3,
      estimatedCost: parseFloat(estimatedCost) || 0,
      isActive: true,
    };
    
    // Add the maintenance schedule
    const newSchedule = addMaintenanceSchedule(scheduleData);

    toast({
      title: "Bakım Takvimi Oluşturuldu",
      description: "Yeni bakım takvimi başarıyla oluşturuldu.",
    });

    // Reset form and close dialog
    resetForm();
    onOpenChange(false);
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setVehicleId("");
    setFrequency("monthly");
    setIntervalDays("");
    setNextDate(new Date());
    setReminderDays("3");
    setEstimatedCost("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Yeni Bakım Takvimi Oluştur</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Bakım Başlığı
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="vehicle" className="text-right">
                Araç
              </Label>
              <Select 
                value={vehicleId} 
                onValueChange={setVehicleId}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Araç seçin" />
                </SelectTrigger>
                <SelectContent>
                  {vehicles.map((vehicle) => (
                    <SelectItem key={vehicle.id} value={vehicle.id}>
                      {vehicle.name} ({vehicle.plate})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="frequency" className="text-right">
                Sıklık
              </Label>
              <Select 
                value={frequency} 
                onValueChange={(value: "daily" | "weekly" | "monthly" | "quarterly" | "yearly" | "custom") => setFrequency(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Sıklık seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Günlük</SelectItem>
                  <SelectItem value="weekly">Haftalık</SelectItem>
                  <SelectItem value="monthly">Aylık</SelectItem>
                  <SelectItem value="quarterly">Üç Aylık</SelectItem>
                  <SelectItem value="yearly">Yıllık</SelectItem>
                  <SelectItem value="custom">Özel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {frequency === 'custom' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="intervalDays" className="text-right">
                  Gün Aralığı
                </Label>
                <Input
                  id="intervalDays"
                  type="number"
                  min="1"
                  value={intervalDays}
                  onChange={(e) => setIntervalDays(e.target.value)}
                  className="col-span-3"
                  required={frequency === 'custom'}
                />
              </div>
            )}
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nextDate" className="text-right">
                İlk Tarih
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "col-span-3 justify-start text-left font-normal",
                      !nextDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {nextDate ? format(nextDate, "PPP", { locale: tr }) : <span>Tarih seçin</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={nextDate}
                    onSelect={setNextDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reminderDays" className="text-right">
                Hatırlatıcı (gün)
              </Label>
              <Input
                id="reminderDays"
                type="number"
                min="0"
                value={reminderDays}
                onChange={(e) => setReminderDays(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="estimatedCost" className="text-right">
                Tahmini Maliyet (₺)
              </Label>
              <Input
                id="estimatedCost"
                type="number"
                value={estimatedCost}
                onChange={(e) => setEstimatedCost(e.target.value)}
                className="col-span-3"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Açıklama
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Oluştur</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};


import { Button } from "@/components/ui/button";
import { CreateVehicleDialog } from "./CreateVehicleDialog";
import { CreateDriverDialog } from "./CreateDriverDialog";
import { CreateFuelRecordDialog } from "./CreateFuelRecordDialog";

export const VehiclesHeader = () => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h2 className="text-3xl font-bold tracking-tight"><PERSON><PERSON></h2>
        <p className="text-muted-foreground">
          Ara<PERSON>lar<PERSON>, sürücüleri ve yakıt kayıtlarını yönetin
        </p>
      </div>
      <div className="flex items-center gap-4">
        <CreateVehicleDialog />
        <CreateDriverDialog />
        <CreateFuelRecordDialog />
      </div>
    </div>
  );
};

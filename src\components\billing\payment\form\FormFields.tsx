
import React from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CalendarIcon, User } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { UseFormReturn } from "react-hook-form";
import { PaymentOrderFormValues } from "../PaymentOrderFormTypes";

type DocumentFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const DocumentNumberField = ({ form }: DocumentFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="documentNumber"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="text-[11px]">Evrak No</FormLabel>
          <FormControl>
            <div className="flex items-center gap-1">
              <Input placeholder="OE-6675" {...field} className="h-9 text-xs py-1" />
              <Badge variant="outline" className="text-[9px] h-5 whitespace-nowrap">
                Otomatik
              </Badge>
            </div>
          </FormControl>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type RequestingUserFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const RequestingUserField = ({ form }: RequestingUserFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="requestingUser"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="flex items-center gap-0.5 text-[11px]">
            <User className="h-3 w-3" />
            Oluşturan
          </FormLabel>
          <FormControl>
            <Input placeholder="Ad Soyad" {...field} className="h-9 text-xs py-1" />
          </FormControl>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type DateFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const DateField = ({ form }: DateFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="date"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="flex items-center gap-0.5 text-[11px]">
            <CalendarIcon className="h-3 w-3" />
            Tarih
          </FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    "pl-3 text-left font-normal h-9 text-xs w-full",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value ? (
                    format(field.value, "dd.MM.yy", { locale: tr })
                  ) : (
                    <span>Tarih</span>
                  )}
                  <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                disabled={(date) => date < new Date("1900-01-01")}
                initialFocus
                className={cn("p-2 pointer-events-auto")}
                locale={tr}
              />
            </PopoverContent>
          </Popover>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type DueDateFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const DueDateField = ({ form }: DueDateFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="dueDate"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="flex items-center gap-0.5 text-[11px]">
            <CalendarIcon className="h-3 w-3" />
            Ödeme Tar.
          </FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    "pl-3 text-left font-normal h-9 text-xs w-full",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value ? (
                    format(field.value, "dd.MM.yy", { locale: tr })
                  ) : (
                    <span>Tarih</span>
                  )}
                  <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                disabled={(date) => date < new Date()}
                initialFocus
                className={cn("p-2 pointer-events-auto")}
                locale={tr}
              />
            </PopoverContent>
          </Popover>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type AmountFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const AmountField = ({ form }: AmountFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="amount"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="text-[11px]">Tutar</FormLabel>
          <FormControl>
            <div className="relative">
              <Input 
                {...field} 
                type="text" 
                className="pl-6 h-9 text-xs py-1"
                placeholder="0.00" 
              />
              <span className="absolute left-3 top-2 text-muted-foreground text-xs">₺</span>
            </div>
          </FormControl>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type SourceTypeFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const SourceTypeField = ({ form }: SourceTypeFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="sourceType"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="text-[11px]">Ödeme Kaynağı</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger className="h-9 text-xs">
                <SelectValue placeholder="Seçin" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="nakit">Nakit</SelectItem>
              <SelectItem value="avans">Avans</SelectItem>
              <SelectItem value="diğer">Diğer</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type PriorityFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const PriorityField = ({ form }: PriorityFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="priority"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="text-[11px]">Önceliği</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger className="h-9 text-xs">
                <SelectValue placeholder="Seçin" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="high" className="text-red-500 font-medium text-xs">Yüksek</SelectItem>
              <SelectItem value="medium" className="text-amber-500 font-medium text-xs">Orta</SelectItem>
              <SelectItem value="low" className="text-green-500 font-medium text-xs">Düşük</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type DetailsFieldProps = {
  form: UseFormReturn<PaymentOrderFormValues>;
};

export const DetailsField = ({ form }: DetailsFieldProps) => {
  return (
    <FormField
      control={form.control}
      name="details"
      render={({ field }) => (
        <FormItem className="space-y-1">
          <FormLabel className="text-[11px]">Açıklama</FormLabel>
          <FormControl>
            <Textarea 
              placeholder="Ödeme detayları..." 
              {...field} 
              className="resize-none h-16 text-xs min-h-0 py-2 px-3"
            />
          </FormControl>
          <FormMessage className="text-[9px]" />
        </FormItem>
      )}
    />
  );
};

type SupplierInfoProps = {
  invoice: any | null;
};

export const SupplierInfo = ({ invoice }: SupplierInfoProps) => {
  if (!invoice) return null;
  
  return (
    <div className="space-y-1 col-span-2">
      <label className="text-[11px] font-medium">Cari</label>
      <div className="flex h-9 rounded-md border border-input bg-muted px-3 py-2 text-xs">
        <div className="truncate max-w-full" title={invoice.supplier}>
          {invoice.supplier}
        </div>
      </div>
    </div>
  );
};

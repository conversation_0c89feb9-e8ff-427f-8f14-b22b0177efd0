
import { AlertCircle, Building, FileDigit, Layers, Tag, User } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface BasicInfoFieldsProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

export const BasicInfoFields = ({ formData, updateFormData, errors }: BasicInfoFieldsProps) => {
  return (
    <div className="space-y-2">
      <div className="space-y-1">
        <div className="flex items-center gap-1">
          <FileDigit className="h-4 w-4 text-primary" />
          <Label htmlFor="kod" className="font-medium text-sm">
            Car<PERSON> <span className="text-red-500">*</span>
          </Label>
        </div>
        <Input 
          id="kod" 
          placeholder="Örnek: 120.0001"
          value={formData.kod || ""}
          onChange={(e) => updateFormData("kod", e.target.value)}
          className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
        />
        {errors.kod && (
          <p className="text-xs text-red-500 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.kod}
          </p>
        )}
      </div>
      
      <div className="space-y-1">
        <div className="flex items-center gap-1">
          <Building className="h-4 w-4 text-primary" />
          <Label htmlFor="unvan" className="font-medium text-sm">
            Firma Adı / Şahıs Adı Soyadı <span className="text-red-500">*</span>
          </Label>
        </div>
        <Input 
          id="unvan" 
          placeholder="Firma adını veya şahıs adı-soyadını girin" 
          value={formData.unvan || ""}
          onChange={(e) => updateFormData("unvan", e.target.value)}
          className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
        />
        {errors.unvan && (
          <p className="text-xs text-red-500 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.unvan}
          </p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-2">
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <Layers className="h-4 w-4 text-primary" />
            <Label htmlFor="grup" className="font-medium text-sm">Grup</Label>
          </div>
          <Input 
            id="grup" 
            placeholder="Grup kodu/adı" 
            value={formData.grup || ""}
            onChange={(e) => updateFormData("grup", e.target.value)}
            className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
          />
        </div>
        
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <Tag className="h-4 w-4 text-primary" />
            <Label htmlFor="ozelKod" className="font-medium text-sm">Özel Kod</Label>
          </div>
          <Input 
            id="ozelKod" 
            placeholder="Özel kodlama" 
            value={formData.ozelKod || ""}
            onChange={(e) => updateFormData("ozelKod", e.target.value)}
            className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-2">
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <Tag className="h-4 w-4 text-primary" />
            <Label htmlFor="ozelTanim" className="font-medium text-sm">Özel Tanımlar</Label>
          </div>
          <Input 
            id="ozelTanim" 
            placeholder="Özel tanımlar" 
            value={formData.ozelTanim || ""}
            onChange={(e) => updateFormData("ozelTanim", e.target.value)}
            className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
          />
        </div>
      </div>

      <div className="space-y-1">
        <div className="flex items-center gap-1">
          <User className="h-4 w-4 text-primary" />
          <Label htmlFor="ilgiliKisi" className="font-medium text-sm">İlgili Kişi</Label>
        </div>
        <Input 
          id="ilgiliKisi" 
          placeholder="İlgili kişi adı-soyadı" 
          value={formData.ilgiliKisi || ""}
          onChange={(e) => updateFormData("ilgiliKisi", e.target.value)}
          className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
        />
      </div>
    </div>
  );
};

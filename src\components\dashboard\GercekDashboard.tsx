
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState, useEffect } from 'react';
import { MusteriListesi } from './MusteriListesi';
import { FaturaListesi } from './FaturaListesi';
import { musteriService, faturaService, urunService } from '@/services/apiService';

export const GercekDashboard = () => {
  const [istatistikler, setIstatistikler] = useState({
    musteriSayisi: 0,
    faturaSayisi: 0,
    bekleyenFaturaTutar: 0,
    odenenFaturaTutar: 0,
    urunSayisi: 0,
    loading: true
  });

  useEffect(() => {
    const fetchIstatistikler = async () => {
      try {
        const [musteriler, faturalar, urunler] = await Promise.all([
          musteriService.getAll(),
          faturaService.getAll(),
          urunService.getAll()
        ]);

        const odenenFaturalar = faturalar.filter((fatura: any) => fatura.durum === 'odendi');
        const bekleyenFaturalar = faturalar.filter((fatura: any) => fatura.durum === 'bekliyor');

        setIstatistikler({
          musteriSayisi: musteriler.length,
          faturaSayisi: faturalar.length,
          bekleyenFaturaTutar: bekleyenFaturalar.reduce((toplam: number, fatura: any) => toplam + fatura.toplam, 0),
          odenenFaturaTutar: odenenFaturalar.reduce((toplam: number, fatura: any) => toplam + fatura.toplam, 0),
          urunSayisi: urunler.length,
          loading: false
        });
      } catch (error) {
        console.error('İstatistikler alınırken hata oluştu:', error);
        setIstatistikler(prev => ({ ...prev, loading: false }));
      }
    };

    fetchIstatistikler();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Gerçek Verilerle Panel</h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Müşteri</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {istatistikler.loading ? '...' : istatistikler.musteriSayisi}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Toplam Fatura</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {istatistikler.loading ? '...' : istatistikler.faturaSayisi}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Ödemeler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {istatistikler.loading ? '...' : `₺${istatistikler.bekleyenFaturaTutar.toLocaleString('tr-TR')}`}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Ödenen Faturalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {istatistikler.loading ? '...' : `₺${istatistikler.odenenFaturaTutar.toLocaleString('tr-TR')}`}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="musteriler" className="space-y-6">
        <TabsList>
          <TabsTrigger value="musteriler">Müşteriler</TabsTrigger>
          <TabsTrigger value="faturalar">Faturalar</TabsTrigger>
        </TabsList>

        <TabsContent value="musteriler">
          <MusteriListesi />
        </TabsContent>

        <TabsContent value="faturalar">
          <FaturaListesi />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Briefcase, Calendar, Users, Clock } from "lucide-react";

const ProfesyonelHizmetler = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Profesyonel Hizmetler</h2>
          <p className="text-muted-foreground">
            Proje/Görev yönetimi, zaman ve masraf yönetimi, kaynak yönetimi ve planlama
          </p>
        </div>
        
        <Tabs defaultValue="projects" className="space-y-4">
          <TabsList>
            <TabsTrigger value="projects">Proje/Görev</TabsTrigger>
            <TabsTrigger value="timesheet">Zaman ve Masraf</TabsTrigger>
            <TabsTrigger value="resources">Kaynak Yönetimi</TabsTrigger>
          </TabsList>
          
          <TabsContent value="projects" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Proje ve Görev Yönetimi</CardTitle>
                <CardDescription>
                  Müşterilerinize sunduğunuz profesyonel hizmetleri planlayın ve yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Briefcase className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Proje ve görev verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="timesheet" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Zaman ve Masraf Yönetimi</CardTitle>
                <CardDescription>
                  Çalışanların harcadığı zamanı ve yaptığı masrafları takip edin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Clock className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Zaman ve masraf takibi burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="resources" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Kaynak Yönetimi ve Planlama</CardTitle>
                <CardDescription>
                  Danışmanlarınızı ve uzmanlarınızı yetkinliklerine göre yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Users className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Kaynak yönetimi verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default ProfesyonelHizmetler;

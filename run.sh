#!/bin/bash

# Log için tarih
TARIH=$(date "+%Y-%m-%d %H:%M:%S")

# server.js'in çalışıp çalışmadığını kontrol et
if ! pgrep -f "node.*server.js" > /dev/null
then
    echo "$TARIH - server.js çalışmıyor. Başlatılıyor..." >> /var/log/myapp.log
    # server.js'i başlat
    /usr/bin/node /var/www/html/server.js >> /var/log/myapp.log 2>&1 &
else
    echo "$TARIH - server.js zaten çalışıyor." >> /var/log/myapp.log
fi

# senkronize.js'in çalışıp çalışmadığını kontrol et
if ! pgrep -f "node.*senkronize.js" > /dev/null
then
    echo "$TARIH - senkronize.js çalışmıyor. Başlatılıyor..." >> /var/log/myapp.log
    # senkronize.js'i başlat
    /usr/bin/node /var/www/html/senkronize.js >> /var/log/myapp.log 2>&1 &
else
    echo "$TARIH - senkronize.js zaten çalışıyor." >> /var/log/myapp.log
fi

# senkronize.js'in çalışıp çalışmadığını kontrol et
if ! pgrep -f "node.*senkronize2.js" > /dev/null
then
    echo "$TARIH - senkronize2.js çalışmıyor. Başlatılıyor..." >> /var/log/myapp.log
    # senkronize2.js'i başlat
    /usr/bin/node /var/www/html/senkronize2.js >> /var/log/myapp.log 2>&1 &
else
    echo "$TARIH - senkronize2.js zaten çalışıyor." >> /var/log/myapp.log
fi


import { Invoice } from "@/stores/billingStore";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const invoiceColumns: ColumnDef<Invoice>[] = [
  {
    accessorKey: "number",
    header: "Fatura No",
  },
  {
    accessorKey: "createdAt",
    header: "Tarih",
    cell: ({ row }) => {
      return new Date(row.getValue("createdAt")).toLocaleDateString("tr-TR");
    },
  },
  {
    accessorKey: "total",
    header: "Tutar",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("total"));
      const currency = row.original.currency;
      
      return new Intl.NumberFormat("tr-TR", {
        style: "currency",
        currency: currency,
      }).format(amount);
    },
  },
  {
    accessorKey: "status",
    header: "Durum",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      
      const statusMap: Record<string, { label: string; variant: "default" | "destructive" | "outline" | "secondary" }> = {
        pending: { label: "Bekliyor", variant: "outline" },
        paid: { label: "Ödendi", variant: "default" },
        cancelled: { label: "İptal", variant: "destructive" },
        refunded: { label: "İade", variant: "secondary" },
      };
      
      const { label, variant } = statusMap[status] || statusMap.pending;
      
      return <Badge variant={variant}>{label}</Badge>;
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Ödeme Yöntemi",
    cell: ({ row }) => {
      const method = row.getValue("paymentMethod") as string;
      
      const methodMap: Record<string, string> = {
        credit_card: "Kredi Kartı",
        bank_transfer: "Havale/EFT",
        online_payment: "Online Ödeme",
      };
      
      return methodMap[method] || "-";
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const invoice = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Düzenle</DropdownMenuItem>
            <DropdownMenuItem>Yazdır</DropdownMenuItem>
            <DropdownMenuItem>E-posta Gönder</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-destructive">
              Sil
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];


import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useMaintenanceStore } from "@/stores/maintenanceStore";
import { useVehicleStore } from "@/stores/vehicleStore";
import { MaintenanceItemsList } from "./MaintenanceItemsList";
import { MaintenanceSchedulesList } from "./MaintenanceSchedulesList";
import { MaintenanceCostChart } from "./MaintenanceCostChart";
import { MaintenanceCalendar } from "./MaintenanceCalendar";
import { MaintenanceStats } from "./MaintenanceStats";

export const MaintenanceDashboard = () => {
  const { maintenanceItems, maintenanceSchedules } = useMaintenanceStore();
  const { vehicles } = useVehicleStore();
  
  return (
    <Tabs defaultValue="overview" className="space-y-4">
      <TabsList>
        <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
        <TabsTrigger value="all">Tüm Bakımlar</TabsTrigger>
        <TabsTrigger value="schedules">Bakım Takvimleri</TabsTrigger>
        <TabsTrigger value="calendar">Takvim Görünümü</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        <MaintenanceStats />
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="col-span-2">
            <MaintenanceCostChart />
          </div>
          
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Yaklaşan Bakımlar</h3>
            <MaintenanceSchedulesList 
              schedules={maintenanceSchedules.slice(0, 5)} 
              vehicles={vehicles}
              compact
            />
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Son Bakım Kayıtları</h3>
          <MaintenanceItemsList 
            items={maintenanceItems.sort((a, b) => 
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            ).slice(0, 5)} 
            vehicles={vehicles}
          />
        </div>
      </TabsContent>
      
      <TabsContent value="all" className="space-y-4">
        <MaintenanceItemsList 
          items={maintenanceItems} 
          vehicles={vehicles}
        />
      </TabsContent>
      
      <TabsContent value="schedules" className="space-y-4">
        <MaintenanceSchedulesList 
          schedules={maintenanceSchedules} 
          vehicles={vehicles}
        />
      </TabsContent>
      
      <TabsContent value="calendar" className="space-y-4">
        <MaintenanceCalendar />
      </TabsContent>
    </Tabs>
  );
};

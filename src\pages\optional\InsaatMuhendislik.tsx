
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Construction, FileText, Users, Building } from "lucide-react";

const InsaatMuhendislik = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">İnşaat ve Mühendislik</h2>
          <p className="text-muted-foreground">
            Proje maliyet yönetimi, hakediş yönetimi ve alt yüklenici yönetimi
          </p>
        </div>
        
        <Tabs defaultValue="cost" className="space-y-4">
          <TabsList>
            <TabsTrigger value="cost">Proje <PERSON></TabsTrigger>
            <TabsTrigger value="progress">Hakediş Yönetimi</TabsTrigger>
            <TabsTrigger value="subcontractor">Alt Yüklenici</TabsTrigger>
          </TabsList>
          
          <TabsContent value="cost" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Proje Maliyet Yönetimi</CardTitle>
                <CardDescription>
                  Projelerinizin maliyet analizini yapın ve kontrol altında tutun
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Construction className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Maliyet grafikleri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="progress" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Hakediş Yönetimi</CardTitle>
                <CardDescription>
                  Proje ilerlemesine göre hakediş işlemlerinizi yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <FileText className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Hakediş takip verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="subcontractor" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Alt Yüklenici Yönetimi</CardTitle>
                <CardDescription>
                  Taşeron firmalarınızı ve sözleşmelerinizi yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Users className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Alt yüklenici bilgileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default InsaatMuhendislik;

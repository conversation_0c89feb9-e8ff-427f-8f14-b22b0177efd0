
import React, { useState, useEffect } from 'react';
import { useMaintenanceStore } from '@/stores/maintenanceStore';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Wrench, 
  Clock, 
  CheckCircle, 
  AlertTriangle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

// Union type for calendar items
type CalendarItem = {
  id: string;
  title: string;
  description: string;
  vehicleId: string;
  date: Date;
  type: 'item' | 'schedule';
  isCompleted: boolean;
  createdAt: string;
};

export const MaintenanceCalendar = () => {
  const { maintenanceItems, maintenanceSchedules } = useMaintenanceStore();
  const [date, setDate] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [calendarItems, setCalendarItems] = useState<CalendarItem[]>([]);
  const [selectedDateEvents, setSelectedDateEvents] = useState<CalendarItem[]>([]);

  useEffect(() => {
    // Convert maintenance items and schedules to calendar items
    const items = maintenanceItems.map(item => ({
      ...item,
      date: new Date(item.scheduledDate), // Use scheduledDate from the store
      type: 'item' as const,
      isCompleted: item.status === 'completed',
      title: item.title // Ensure title is explicitly mapped
    }));

    const schedules = maintenanceSchedules.map(schedule => ({
      ...schedule,
      date: new Date(schedule.nextDate), // Use nextDate from the store
      type: 'schedule' as const,
      isCompleted: schedule.isActive ? false : true, // Map isActive to isCompleted
      title: schedule.title // Ensure title is explicitly mapped
    }));

    setCalendarItems([...items, ...schedules]);
  }, [maintenanceItems, maintenanceSchedules]);

  useEffect(() => {
    if (selectedDate) {
      const events = calendarItems.filter(item => {
        const itemDate = item.date;
        return (
          itemDate.getDate() === selectedDate.getDate() &&
          itemDate.getMonth() === selectedDate.getMonth() &&
          itemDate.getFullYear() === selectedDate.getFullYear()
        );
      });
      setSelectedDateEvents(events);
    }
  }, [selectedDate, calendarItems]);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  // Function to render calendar day with badges for events
  const renderCalendarDay = (day: Date) => {
    // Count events for this day
    const dayEvents = calendarItems.filter(item => {
      const itemDate = item.date;
      return (
        itemDate.getDate() === day.getDate() &&
        itemDate.getMonth() === day.getMonth() &&
        itemDate.getFullYear() === day.getFullYear()
      );
    });

    if (dayEvents.length === 0) return null;

    const maintenanceCount = dayEvents.filter(e => e.type === 'item').length;
    const scheduleCount = dayEvents.filter(e => e.type === 'schedule').length;
    const completedCount = dayEvents.filter(e => e.isCompleted).length;
    const pendingCount = dayEvents.length - completedCount;

    return (
      <div className="flex flex-col items-center mt-1">
        {maintenanceCount > 0 && (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 text-[10px] h-4 px-1 mb-1">
            {maintenanceCount}
          </Badge>
        )}
        {scheduleCount > 0 && (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 text-[10px] h-4 px-1">
            {scheduleCount}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card className="card-modern">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Bakım Takvimi</CardTitle>
        </CardHeader>
        <CardContent>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            locale={tr}
            className="rounded-md border p-3 pointer-events-auto"
            components={{
              DayContent: (props) => (
                <div className="flex flex-col items-center">
                  <div>{format(props.date, 'd')}</div>
                  {renderCalendarDay(props.date)}
                </div>
              ),
            }}
          />
        </CardContent>
      </Card>

      <Card className="card-modern">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-lg font-medium">
            {selectedDate ? format(selectedDate, 'd MMMM yyyy', { locale: tr }) : 'Seçilen Tarih'} Etkinlikleri
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedDateEvents.length === 0 ? (
            <div className="text-center p-6 text-muted-foreground">
              Bu tarihte planlanmış bakım veya etkinlik bulunmamaktadır.
            </div>
          ) : (
            <div className="space-y-4">
              {selectedDateEvents.map((event) => (
                <div 
                  key={event.id} 
                  className={cn(
                    "p-3 border rounded-lg hover-card",
                    event.type === 'item' && event.isCompleted ? "bg-green-50" : "",
                    event.type === 'schedule' && event.isCompleted ? "bg-green-50" : "",
                    event.type === 'item' && !event.isCompleted ? "bg-blue-50" : "",
                    event.type === 'schedule' && !event.isCompleted ? "bg-purple-50" : ""
                  )}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      {event.type === 'item' ? (
                        <Wrench className="h-4 w-4 text-blue-600" />
                      ) : (
                        <Clock className="h-4 w-4 text-purple-600" />
                      )}
                      <div>
                        <h4 className="font-medium text-sm">{event.title}</h4>
                        <p className="text-xs text-muted-foreground">
                          {event.type === 'item' ? 'Bakım Görevi' : 'Planlı Bakım'}
                        </p>
                      </div>
                    </div>
                    {event.isCompleted ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-amber-600" />
                    )}
                  </div>
                  <p className="text-sm mt-2">{event.description}</p>
                  {!event.isCompleted && (
                    <div className="mt-3 flex justify-end">
                      <Button size="sm" variant="outline" className="hover:bg-green-50 hover:text-green-600 hover:border-green-200">
                        Tamamlandı İşaretle
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

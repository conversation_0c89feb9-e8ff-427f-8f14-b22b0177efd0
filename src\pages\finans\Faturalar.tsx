
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { PurchaseInvoices } from "@/components/billing/PurchaseInvoices";
import { SalesInvoices } from "@/components/billing/SalesInvoices";

const Faturalar = () => {
  const tabs: TabItem[] = [
    { id: "purchaseInvoices", label: "Alış Faturaları", component: <PurchaseInvoices /> },
    { id: "salesInvoices", label: "Satış Faturaları", component: <SalesInvoices /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Fatura Yönetimi</h2>
          <p className="text-muted-foreground">
            <PERSON>ış ve satış faturaları yönetimi
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Faturalar;

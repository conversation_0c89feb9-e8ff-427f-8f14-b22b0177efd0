import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  LineChart, 
  Line,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { DataTable } from "./DataTable";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AlertCircle } from "lucide-react";

// Demo veriler
const inventoryItems = [
  { id: "1", sku: "P001", name: "Ürün A", stock: 120, minLevel: 50, value: 24000, category: "Elektronik" },
  { id: "2", sku: "P002", name: "<PERSON>rün <PERSON>", stock: 35, minLevel: 40, value: 17500, category: "Elektronik" },
  { id: "3", sku: "P003", name: "Ürün C", stock: 85, minLevel: 30, value: 12750, category: "Ofis" },
  { id: "4", sku: "P004", name: "<PERSON><PERSON><PERSON><PERSON> <PERSON>", stock: 18, minLevel: 25, value: 9000, category: "Ofis" },
  { id: "5", sku: "P005", name: "Ürün E", stock: 210, minLevel: 100, value: 31500, category: "Diğer" }
];

const lowStockItems = [
  { id: "2", sku: "P002", name: "Ürün B", stock: 35, minLevel: 40, value: 17500, category: "Elektronik" },
  { id: "4", sku: "P004", name: "Ürün D", stock: 18, minLevel: 25, value: 9000, category: "Ofis" }
];

const pendingOrders = [
  { id: "1", orderNo: "ORD-001", customer: "ABC Şirketi", items: 3, totalValue: 12500, orderDate: "2024-07-01", expectedDelivery: "2024-07-15", status: "Hazırlanıyor" },
  { id: "2", orderNo: "ORD-002", customer: "XYZ Ltd", items: 5, totalValue: 28750, orderDate: "2024-07-03", expectedDelivery: "2024-07-18", status: "Beklemede" },
  { id: "3", orderNo: "ORD-003", customer: "123 Holding", items: 2, totalValue: 7800, orderDate: "2024-07-05", expectedDelivery: "2024-07-12", status: "Hazırlanıyor" },
  { id: "4", orderNo: "ORD-004", customer: "Tech Çözümler", items: 4, totalValue: 15200, orderDate: "2024-07-07", expectedDelivery: "2024-07-20", status: "Beklemede" }
];

const inventoryMovementsData = [
  { date: "2024-06-01", in: 45, out: 38 },
  { date: "2024-06-08", in: 52, out: 43 },
  { date: "2024-06-15", in: 38, out: 45 },
  { date: "2024-06-22", in: 65, out: 48 },
  { date: "2024-06-29", in: 42, out: 39 },
  { date: "2024-07-06", in: 53, out: 47 }
];

const deliveryPerformanceData = [
  { month: "Ocak", onTime: 92, delayed: 8 },
  { month: "Şubat", onTime: 88, delayed: 12 },
  { month: "Mart", onTime: 94, delayed: 6 },
  { month: "Nisan", onTime: 90, delayed: 10 },
  { month: "Mayıs", onTime: 95, delayed: 5 },
  { month: "Haziran", onTime: 91, delayed: 9 }
];

// Tablo sütunları
const inventoryColumns = [
  {
    header: "SKU",
    accessorKey: "sku"
  },
  {
    header: "Ürün Adı",
    accessorKey: "name"
  },
  {
    header: "Kategori",
    accessorKey: "category"
  },
  {
    header: "Stok Durumu",
    accessorKey: "stock",
    cell: ({ row }) => {
      const stock = row.original.stock;
      const minLevel = row.original.minLevel;
      const percentage = (stock / (minLevel * 2)) * 100;
      let statusColor = "bg-green-500";
      
      if (stock <= minLevel) {
        statusColor = "bg-red-500";
      } else if (stock <= minLevel * 1.5) {
        statusColor = "bg-amber-500";
      }
      
      return (
        <div className="flex items-center space-x-2">
          <span>{stock}</span>
          <Progress 
            value={percentage} 
            className={`w-24 [&>div]:${statusColor}`} 
          />
        </div>
      );
    }
  },
  {
    header: "Min. Seviye",
    accessorKey: "minLevel"
  },
  {
    header: "Stok Değeri",
    accessorKey: "value",
    cell: ({ row }) => <span>₺{row.original.value.toLocaleString()}</span>
  }
];

const lowStockColumns = [
  {
    header: "",
    id: "alert",
    cell: () => <AlertCircle className="h-5 w-5 text-red-500" />
  },
  {
    header: "SKU",
    accessorKey: "sku"
  },
  {
    header: "Ürün Adı",
    accessorKey: "name"
  },
  {
    header: "Mevcut Stok",
    accessorKey: "stock"
  },
  {
    header: "Min. Seviye",
    accessorKey: "minLevel"
  },
  {
    header: "Stok Durumu",
    id: "stockStatus",
    cell: ({ row }) => {
      const stock = row.original.stock;
      const minLevel = row.original.minLevel;
      const shortage = minLevel - stock;
      
      return (
        <Badge className="bg-red-500">
          {shortage > 0 ? `${shortage} adet eksik` : "Kritik seviyede"}
        </Badge>
      );
    }
  }
];

const ordersColumns = [
  {
    header: "Sipariş No",
    accessorKey: "orderNo"
  },
  {
    header: "Müşteri",
    accessorKey: "customer"
  },
  {
    header: "Ürün Sayısı",
    accessorKey: "items"
  },
  {
    header: "Toplam Değer",
    accessorKey: "totalValue",
    cell: ({ row }) => <span>₺{row.original.totalValue.toLocaleString()}</span>
  },
  {
    header: "Sipariş Tarihi",
    accessorKey: "orderDate"
  },
  {
    header: "Tahmini Teslimat",
    accessorKey: "expectedDelivery"
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;
      let color = "bg-amber-500";
      
      if (status === "Hazırlanıyor") {
        color = "bg-blue-500";
      }
      
      return <Badge className={color}>{status}</Badge>;
    }
  }
];

export const InventoryManagement = () => {
  // Hesaplamalar
  const totalInventoryValue = inventoryItems.reduce((sum, item) => sum + item.value, 0);
  const totalItems = inventoryItems.length;
  const lowStockCount = lowStockItems.length;
  const pendingOrdersValue = pendingOrders.reduce((sum, order) => sum + order.totalValue, 0);
  
  // Ortalama teslimat süresi (demo veri)
  const avgDeliveryTime = 3.2;
  // Zamanında teslimat oranı (demo veri)
  const onTimeDeliveryRate = 91.8;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Stok ve Sipariş Yönetimi</h2>
      </div>

      {/* Özet Kartları */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Stok Değeri</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">₺{totalInventoryValue.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Ürün</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Düşük Stok Ürünleri</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{lowStockCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Bekleyen Siparişler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">₺{pendingOrdersValue.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Stok Durumu */}
      <Card>
        <CardHeader>
          <CardTitle>Stok Durumu</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={inventoryColumns} data={inventoryItems} />
        </CardContent>
      </Card>

      {/* Düşük Stok Uyarıları */}
      <Card>
        <CardHeader>
          <CardTitle>Düşük Stok Uyarıları</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={lowStockColumns} data={lowStockItems} />
        </CardContent>
      </Card>

      {/* Stok Hareket Analizi */}
      <Card>
        <CardHeader>
          <CardTitle>Stok Hareket Analizi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={inventoryMovementsData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="in" name="Stok Giriş" stroke="#4f46e5" activeDot={{ r: 8 }} />
                <Line type="monotone" dataKey="out" name="Stok Çıkış" stroke="#ef4444" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Bekleyen Siparişler */}
      <Card>
        <CardHeader>
          <CardTitle>Bekleyen Siparişler</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={ordersColumns} data={pendingOrders} />
        </CardContent>
      </Card>

      {/* Teslimat Performansı */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Teslimat Performansı</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={deliveryPerformanceData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  stackOffset="expand"
                  barSize={30}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `${value}%`} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="onTime" name="Zamanında Teslimat" stackId="a" fill="#10b981" />
                  <Bar dataKey="delayed" name="Gecikmeli Teslimat" stackId="a" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Teslimat İstatistikleri</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-muted-foreground">Ortalama Teslimat Süresi</p>
              <p className="text-2xl font-bold">{avgDeliveryTime} gün</p>
            </div>
            <div>
              <p className="text-muted-foreground">Zamanında Teslimat Oranı</p>
              <p className="text-2xl font-bold text-green-600">%{onTimeDeliveryRate}</p>
              <Progress value={onTimeDeliveryRate} className="h-2 mt-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

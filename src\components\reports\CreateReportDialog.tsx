import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useReportsStore, ReportType, TimeFrame, ChartType } from "@/stores/reportsStore";
import { useToast } from "@/hooks/use-toast";

interface CreateReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateReportDialog = ({ open, onOpenChange }: CreateReportDialogProps) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [type, setType] = useState<ReportType>("sales");
  const [timeframe, setTimeframe] = useState<TimeFrame>("monthly");
  const [chartType, setChartType] = useState<ChartType>("line");

  const { createReport, saveReport } = useReportsStore();
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name) {
      toast({
        title: "Hata",
        description: "Rapor adı girmelisiniz.",
        variant: "destructive",
      });
      return;
    }

    // Create a new report
    const reportData = {
      name,
      description,
      type,
      timeframe,
      chartType,
      schedule: {
        enabled: false,
        frequency: null,
        recipients: [],
        lastSent: null,
      },
      filters: {},
      data: [],
    };
    
    // Call createReport and get the new report
    const newReport = createReport(reportData);
    
    // Save the report - no need to check truthiness since createReport returns the report
    saveReport(newReport);

    toast({
      title: "Rapor Oluşturuldu",
      description: "Yeni rapor başarıyla oluşturuldu.",
    });

    // Reset form and close dialog
    setName("");
    setDescription("");
    setType("sales");
    setTimeframe("monthly");
    setChartType("line");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Yeni Rapor Oluştur</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Rapor Adı
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Açıklama
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Rapor Tipi
              </Label>
              <Select 
                value={type} 
                onValueChange={(value: ReportType) => setType(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Rapor tipi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Satış</SelectItem>
                  <SelectItem value="inventory">Envanter</SelectItem>
                  <SelectItem value="orders">Siparişler</SelectItem>
                  <SelectItem value="customers">Müşteriler</SelectItem>
                  <SelectItem value="custom">Özel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="timeframe" className="text-right">
                Zaman Aralığı
              </Label>
              <Select 
                value={timeframe} 
                onValueChange={(value: TimeFrame) => setTimeframe(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Zaman aralığı seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Günlük</SelectItem>
                  <SelectItem value="weekly">Haftalık</SelectItem>
                  <SelectItem value="monthly">Aylık</SelectItem>
                  <SelectItem value="quarterly">Üç Aylık</SelectItem>
                  <SelectItem value="yearly">Yıllık</SelectItem>
                  <SelectItem value="custom">Özel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="chartType" className="text-right">
                Grafik Türü
              </Label>
              <Select 
                value={chartType} 
                onValueChange={(value: ChartType) => setChartType(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Grafik türü seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Çizgi Grafik</SelectItem>
                  <SelectItem value="bar">Çubuk Grafik</SelectItem>
                  <SelectItem value="pie">Pasta Grafik</SelectItem>
                  <SelectItem value="area">Alan Grafik</SelectItem>
                  <SelectItem value="table">Tablo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Oluştur</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};


import { useVehicleStore } from "@/stores/vehicleStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

export const VehiclesList = () => {
  const vehicles = useVehicleStore((state) => state.vehicles);

  return (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Araç Adı</TableHead>
            <TableHead>Plaka</TableHead>
            <TableHead>Tip</TableHead>
            <TableHead>Marka/Model</TableHead>
            <TableHead>Durum</TableHead>
            <TableHead>Son Kilometre</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {vehicles.map((vehicle) => (
            <TableRow key={vehicle.id}>
              <TableCell>{vehicle.name}</TableCell>
              <TableCell>{vehicle.plate}</TableCell>
              <TableCell>{vehicle.type}</TableCell>
              <TableCell>{vehicle.brand} {vehicle.model}</TableCell>
              <TableCell>
                <Badge variant={vehicle.status === 'active' ? 'default' : 'destructive'}>
                  {vehicle.status}
                </Badge>
              </TableCell>
              <TableCell>{vehicle.currentOdometer} km</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

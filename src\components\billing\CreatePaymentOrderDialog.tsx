
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, CheckCircle2, Clock, User, Info, AlertCircle, FileText } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";

type CreatePaymentOrderDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: any | null;
  mode: 'single' | 'multiple' | 'free';
  onFreePaymentOrder: () => void;
};

// Form schema validation
const paymentOrderSchema = z.object({
  requestingUser: z.string().min(2, { message: "Kullanıcı adı gereklidir" }),
  date: z.date({ required_error: "Tarih seçilmelidir" }),
  details: z.string().min(10, { message: "En az 10 karakter girmelisiniz" }),
  priority: z.enum(["high", "medium", "low"], { required_error: "Öncelik seçilmelidir" }),
  sourceType: z.enum(["nakit", "avans", "diğer"], { required_error: "Kaynak türü seçilmelidir" }),
  documentNumber: z.string().optional(),
  amount: z.string().min(1, "Tutar gereklidir"),
  dueDate: z.date({ required_error: "Vade tarihi seçilmelidir" }),
});

type PaymentOrderForm = z.infer<typeof paymentOrderSchema>;

const pendingInvoices = [
  { id: "2", invoiceNumber: "A2024-002", supplier: "XYZ Dağıtım A.Ş.", dueDate: "2024-08-08", totalAmount: 10325.59, currency: "TRY" },
  { id: "3", invoiceNumber: "A2024-003", supplier: "Teknoloji Partner Ltd.", dueDate: "2024-08-10", totalAmount: 26904.00, currency: "TRY" },
  { id: "4", invoiceNumber: "A2024-004", supplier: "Global Satış A.Ş.", dueDate: "2024-08-12", totalAmount: 6136.00, currency: "USD" },
];

export const CreatePaymentOrderDialog = ({
  open,
  onOpenChange,
  invoice,
  mode,
  onFreePaymentOrder
}: CreatePaymentOrderDialogProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  
  // Default values for the form
  const defaultValues: Partial<PaymentOrderForm> = {
    date: new Date(),
    dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    priority: "medium",
    sourceType: "nakit",
    details: invoice ? `${invoice.invoiceNumber} numaralı fatura ödemesi` : "",
    amount: invoice ? invoice.totalAmount.toString() : "",
  };

  // Initialize the form
  const form = useForm<PaymentOrderForm>({
    resolver: zodResolver(paymentOrderSchema),
    defaultValues,
  });

  // Handle form submission
  const onSubmit = async (data: PaymentOrderForm) => {
    setLoading(true);
    
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log("Payment order data submitted:", data);
      
      toast({
        title: "Ödeme Emri Oluşturuldu",
        description: "Ödeme emriniz başarıyla oluşturuldu ve onay için gönderildi.",
      });
      
      // Reset the form and close dialog
      form.reset(defaultValues);
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ödeme emri oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleInvoiceSelection = (id: string) => {
    setSelectedInvoices(prev => 
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  // Get dialog title based on mode
  const getDialogTitle = () => {
    switch(mode) {
      case 'single':
        return `${invoice?.invoiceNumber || ''} Faturası için Ödeme Emri`;
      case 'multiple':
        return "Bekleyen Faturalar İçin Ödeme Emri";
      case 'free':
        return "Serbest Ödeme Emri Oluştur";
      default:
        return "Ödeme Emri Oluştur";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {getDialogTitle()}
          </DialogTitle>
        </DialogHeader>

        {mode === 'multiple' && (
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium">Bekleyen Faturalar</h3>
              <Button variant="link" size="sm" onClick={onFreePaymentOrder}>
                Serbest Ödeme Emri Oluştur
              </Button>
            </div>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full text-sm">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-4 py-2 text-left font-medium">Seç</th>
                    <th className="px-4 py-2 text-left font-medium">Fatura No</th>
                    <th className="px-4 py-2 text-left font-medium">Tedarikçi</th>
                    <th className="px-4 py-2 text-left font-medium">Vade Tarihi</th>
                    <th className="px-4 py-2 text-right font-medium">Tutar</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {pendingInvoices.map((inv) => (
                    <tr key={inv.id} className="hover:bg-muted/50">
                      <td className="px-4 py-2">
                        <input 
                          type="checkbox" 
                          checked={selectedInvoices.includes(inv.id)}
                          onChange={() => toggleInvoiceSelection(inv.id)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                      </td>
                      <td className="px-4 py-2">{inv.invoiceNumber}</td>
                      <td className="px-4 py-2">{inv.supplier}</td>
                      <td className="px-4 py-2">{inv.dueDate}</td>
                      <td className="px-4 py-2 text-right">
                        {inv.currency === "TRY" ? "₺" : inv.currency === "USD" ? "$" : "€"} 
                        {inv.totalAmount.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {selectedInvoices.length > 0 && (
              <div className="flex justify-end mt-2">
                <Badge variant="outline" className="bg-muted">
                  {selectedInvoices.length} fatura seçildi
                </Badge>
              </div>
            )}
          </div>
        )}

        <Card className="border-2 border-muted">
          <CardContent className="pt-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-4">
                    {/* Document Number */}
                    <FormField
                      control={form.control}
                      name="documentNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Evrak No</FormLabel>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <Input placeholder="OE-6675" {...field} />
                              <Badge variant="outline" className="text-muted-foreground">
                                Otomatik
                              </Badge>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Requesting User */}
                    <FormField
                      control={form.control}
                      name="requestingUser"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            Oluşturan
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Ad Soyad" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Date Field */}
                    <FormField
                      control={form.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel className="flex items-center gap-1">
                            <CalendarIcon className="h-4 w-4" />
                            Tarih
                          </FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd.MM.yyyy", { locale: tr })
                                  ) : (
                                    <span>Tarih Seçin</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date("1900-01-01")}
                                initialFocus
                                className={cn("p-3 pointer-events-auto")}
                                locale={tr}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Due Date Field */}
                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel className="flex items-center gap-1">
                            <CalendarIcon className="h-4 w-4" />
                            Ödeme Tarihi
                          </FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd.MM.yyyy", { locale: tr })
                                  ) : (
                                    <span>Tarih Seçin</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date()}
                                initialFocus
                                className={cn("p-3 pointer-events-auto")}
                                locale={tr}
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-4">
                    {/* Amount Field */}
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tutar</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input 
                                {...field} 
                                type="text" 
                                className="pl-8"
                                placeholder="0.00" 
                              />
                              <span className="absolute left-3 top-2.5 text-muted-foreground">₺</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Source Type */}
                    <FormField
                      control={form.control}
                      name="sourceType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ödeme Kaynağı</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Kaynak türü seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="nakit">Nakit</SelectItem>
                              <SelectItem value="avans">Avans</SelectItem>
                              <SelectItem value="diğer">Diğer</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Priority Field */}
                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Önceliği</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Öncelik seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="high" className="text-red-500 font-medium">Yüksek</SelectItem>
                              <SelectItem value="medium" className="text-amber-500 font-medium">Orta</SelectItem>
                              <SelectItem value="low" className="text-green-500 font-medium">Düşük</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Cari Account (Read-only) */}
                    {invoice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium">Cari</label>
                        <div className="flex h-10 rounded-md border border-input bg-muted px-3 py-2 text-sm">
                          {invoice.supplier}
                        </div>
                      </div>
                    )}

                  </div>
                </div>

                {/* Transaction Details */}
                <FormField
                  control={form.control}
                  name="details"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Açıklama</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Ödeme detaylarını buraya girin..." 
                          {...field} 
                          className="resize-y min-h-[80px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Read-only approval fields */}
                <div className="bg-muted/40 p-3 rounded-lg border border-border space-y-2">
                  <h3 className="text-sm font-medium flex items-center gap-1 pb-1 border-b border-border">
                    <Clock className="h-4 w-4" />
                    Onay Bilgileri
                  </h3>
                  
                  <div className="grid grid-cols-3 gap-3 text-sm">
                    <div>
                      <label className="text-xs font-medium text-muted-foreground">Onaylama Durumu</label>
                      <div className="bg-background px-3 py-1.5 rounded border border-border">
                        Beklemede
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-muted-foreground">Onaylayan</label>
                      <div className="bg-background px-3 py-1.5 rounded border border-border text-muted-foreground">
                        -
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-xs font-medium text-muted-foreground">Onaylama Tarih/Saat</label>
                      <div className="bg-background px-3 py-1.5 rounded border border-border text-muted-foreground">
                        -
                      </div>
                    </div>
                  </div>
                </div>

                <CardFooter className="flex justify-end gap-2 px-0 pb-0 pt-4">
                  <Button 
                    variant="outline" 
                    type="button"
                    onClick={() => onOpenChange(false)}
                  >
                    İptal
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? "Kaydediliyor..." : "Kaydet"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
};

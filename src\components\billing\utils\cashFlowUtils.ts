
// Demo data representing monthly cash flow amounts for each category
export const months = [
  "Ocak", "<PERSON><PERSON><PERSON>", "Mart", "Nisan", "<PERSON><PERSON><PERSON>", "Haziran", 
  "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık"
];

// Initialize empty cash flow data
export const generateEmptyCashFlowData = () => {
  const data: Record<string, number> = {};
  months.forEach(month => {
    data[month] = 0;
  });
  return data;
};

// Format currency
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount).replace('₺', '') + ' TL';
};

// Demo initial balance
export const initialBalance = 75000;

// Cash flow categories with their data
export const cashFlowData = {
  income: {
    title: "Gelirlerim ve Varlıklarım",
    isExpanded: true,
    data: generateEmptyCashFlowData(),
    bgColor: "bg-green-50",
  },
  expenses: {
    title: "Ödemelerim",
    isExpanded: true,
    data: generateEmptyCashFlowData(),
    bgColor: "bg-orange-50",
    subcategories: [
      {
        id: "services",
        title: "Giderlerim (Alınan Hizmet)",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "invoices",
        title: "Alış Faturası",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "orders",
        title: "Alış Siparişi (Ödemeli)",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "receipts",
        title: "Alınan Serbest Meslek Makbuzlarım",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "cashPayments",
        title: "Nakit Ödemelerim (Kasa)",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "bankTransfers",
        title: "Giden Havalelerim (Banka)",
        data: generateEmptyCashFlowData(),
      },
      {
        id: "checkPayments",
        title: "Çek Ödemelerim (Çek Çıkışı)",
        data: generateEmptyCashFlowData(),
      },
    ]
  },
  total: {
    title: "Toplam Nakit Akış",
    data: generateEmptyCashFlowData(),
    bgColor: "bg-blue-50",
  }
};

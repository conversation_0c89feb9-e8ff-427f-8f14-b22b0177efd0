
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Wrench, Calendar, Users, Smartphone } from "lucide-react";

const SahaServis = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight"><PERSON><PERSON></h2>
          <p className="text-muted-foreground">
            Servis yönetimi, planlama ve atama, mobil saha servis uygulaması
          </p>
        </div>
        
        <Tabs defaultValue="service" className="space-y-4">
          <TabsList>
            <TabsTrigger value="service">Servis Yönetimi</TabsTrigger>
            <TabsTrigger value="dispatch">Planlama ve Atama</TabsTrigger>
            <TabsTrigger value="mobile"><PERSON><PERSON></TabsTrigger>
          </TabsList>
          
          <TabsContent value="service" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Servis Yönetimi</CardTitle>
                <CardDescription>
                  Servis taleplerini ve iş emirlerini yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Wrench className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Servis yönetimi verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="dispatch" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Planlama ve Atama</CardTitle>
                <CardDescription>
                  Teknisyenleri yetkinlik ve konumlarına göre görevlere atayın
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Calendar className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Planlama ve atama araçları burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="mobile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Mobil Saha Servis Uygulaması</CardTitle>
                <CardDescription>
                  Teknisyenlerinizin sahada kullanacağı mobil uygulama yönetimi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Smartphone className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Mobil uygulama entegrasyonu burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default SahaServis;

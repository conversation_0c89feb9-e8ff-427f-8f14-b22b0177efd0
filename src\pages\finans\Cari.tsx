
import MainLayout from "@/components/layout/MainLayout";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import { CariListView } from "@/components/cari/CariListView";
import { CariFormView } from "@/components/cari/CariFormView";
import { CariHesap } from "@/types/cari";
import { cariService } from "@/services/cariService";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

const Cari = () => {
  const [activeTab, setActiveTab] = useState("liste");
  const [cariHesaplar, setCariHesaplar] = useState<CariHesap[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const fetchCariHesaplar = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await cariService.getAll();
      setCariHesaplar(data);
    } catch (err) {
      console.error('Cari hesaplar yüklenirken hata:', err);
      setError('Cari hesaplar yüklenirken bir hata oluştu.');
      toast.error('Cari hesaplar yüklenemedi. Lütfen daha sonra tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCariHesaplar();
  }, []);

  const handleAddNewClick = () => {
    setActiveTab("ekle");
  };

  const handleCancel = () => {
    setActiveTab("liste");
  };

  const handleCariAdded = async () => {
    toast.success('Cari hesap başarıyla eklendi');
    setActiveTab("liste");
    await fetchCariHesaplar(); // Listeyi yenile
  };

  return (
    <MainLayout>
      <div className="space-y-3">
        <div className="py-1 bg-blue-50 dark:bg-blue-950/30 px-4 rounded-lg">
          <h2 className="text-xl font-bold tracking-tight">Cari Hesap Yönetimi</h2>
          <p className="text-muted-foreground text-xs">
            Müşteri ve tedarikçi hesaplarınızı yönetin
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="liste">Cari Hesap Listesi</TabsTrigger>
            <TabsTrigger value="ekle">Yeni Cari Hesap</TabsTrigger>
          </TabsList>
          
          <TabsContent value="liste" className="space-y-4">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Cari hesaplar yükleniyor...</span>
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
                <p>{error}</p>
                <button 
                  onClick={fetchCariHesaplar}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                >
                  Yeniden Dene
                </button>
              </div>
            ) : (
              <CariListView 
                cariHesaplar={cariHesaplar} 
                onAddNewClick={handleAddNewClick}
                onRefresh={fetchCariHesaplar}
              />
            )}
          </TabsContent>
          
          <TabsContent value="ekle">
            <CariFormView 
              onCancel={handleCancel} 
              onSuccess={handleCariAdded} 
            />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Cari;

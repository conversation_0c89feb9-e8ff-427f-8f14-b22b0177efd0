import { SiloData } from '@/models/SiloData';
import { UrunModel } from '@/models/UrunModel';

export interface ProductTypeSummary {
    stokKodu: string;
    urunAdi: string;
    toplamAgirlik: number;
    adet: number;
    kategori?: 'Delikli' | 'Deliksiz' | 'Keçeli';
    uzunluk?: number;
}

export interface CategorySummary {
    kategori: string;
    toplamUzunluk: number;
    ortalamaAgirlik: number;
    adet: number;
}

export interface CategoryStats {
    count: number;
    totalLength: number;
    avgWeight: number;
    totalWeight: number;
    weightPerMeter: number;
}

export class DataCalculations {
    /**
     * Ürün kodundan çap bilgisini alır
     */
    static getUrunCap(stokKodu: string): string {
        // Stok kodlarına göre doğrudan eşleştirme yapıyoruz
        const capMapping: Record<string, string> = {
            // Ø 80 BORU Çeşitleri
            'MRZ-ST00661': '80',
            'MRZ-ST00662': '80',
            'MRZ-ST00663': '80',

            // Ø 100 BORU Çeşitleri
            'MGS0055': '100',
            'MRZ-ST00659': '100',
            'MRZ-ST02549': '100',

            // Ø 125 BORU Çeşitleri
            'MRZ-ST00665': '125',
            'MRZ-ST00664': '125',
            'MRZ-ST00666': '125',

            // Ø 160 BORU Çeşitleri
            'MGS0053': '160',
            'MRZ-ST00658': '160',
            'MRZ-ST02550': '160',

            // Ø 200 BORU Çeşitleri
            'MRZ-ST02551': '200',
            'MRZ-ST00660': '200',
            'MGS0056': '200'
        };

        // Direkt eşleştirme varsa kullan
        if (capMapping[stokKodu]) {
            return capMapping[stokKodu];
        }

        // Eşleştirme yoksa diğer yöntemleri dene
        // MRZ-ST kodları için kontrol
        const parts = stokKodu.split('-');
        if (parts.length > 1) {
            // MRZ-ST00659 formatı için
            const codeMatch = parts[1].match(/\d+/);
            if (codeMatch) {
                // Kod numarası ile bilinen çapları eşleştir
                const codeNum = codeMatch[0];
                if (['00661', '00662', '00663'].includes(codeNum)) return '80';
                if (['00659', '02549'].includes(codeNum)) return '100';
                if (['00665', '00664', '00666'].includes(codeNum)) return '125';
                if (['00658', '02550'].includes(codeNum)) return '160';
                if (['02551', '00660'].includes(codeNum)) return '200';
            }
        }

        // MGS kodları için kontrol
        if (stokKodu.startsWith('MGS')) {
            if (stokKodu.includes('0055')) return '100';
            if (stokKodu.includes('0053')) return '160';
            if (stokKodu.includes('0056')) return '200';
        }

        // Çapı doğrudan içeren kodlar için kontrol (Ø sembolü)
        if (stokKodu.includes('Ø')) {
            const match = stokKodu.match(/Ø\s*(\d+)/);
            if (match && match[1]) {
                return match[1];
            }
        }

        return '';
    }

    /**
     * Ürün adından çap bilgisini alır
     */
    static getUrunCapFromStokAdi(stokAdi: string): string {
        if (!stokAdi) return '';

        // Çap sembolünü bul (Ø)
        const matchSymbol = stokAdi.match(/Ø\s*(\d+)/);
        if (matchSymbol && matchSymbol[1]) {
            return matchSymbol[1];
        }

        // Sembol yoksa sayıyı bul
        const matchNumber = stokAdi.match(/\b(\d+)\s*(mm)?\s*(BORU|DELİKLİ|DELİKSİZ|KEÇELİ)/i);
        if (matchNumber && matchNumber[1]) {
            return matchNumber[1];
        }

        return '';
    }

    /**
     * Ürün tipini belirler (DELİKLİ, DELİKSİZ, KEÇELİ)
     */
    static getUrunTipi(boruAdi?: string, stokKodu?: string, stokAdi?: string): string {
        // 1. İlk olarak boruAdi kontrol et
        if (boruAdi && typeof boruAdi === 'string') {
            const boruAdiLower = boruAdi.toLowerCase();

            if (boruAdiLower.includes('delikli')) {
                return 'DELİKLİ';
            } else if (boruAdiLower.includes('keçeli') || boruAdiLower.includes('keceli') || boruAdiLower.includes('keçe sargili')) {
                return 'KEÇELİ';
            } else if (boruAdiLower.includes('deliksiz')) {
                return 'DELİKSİZ';
            }
        }

        // 2. Sonra stokAdi kontrol et
        if (stokAdi && typeof stokAdi === 'string') {
            const stokAdiLower = stokAdi.toLowerCase();

            if (stokAdiLower.includes('delikli')) {
                return 'DELİKLİ';
            } else if (stokAdiLower.includes('keçeli') || stokAdiLower.includes('keceli') || stokAdiLower.includes('keçe sargili')) {
                return 'KEÇELİ';
            } else if (stokAdiLower.includes('deliksiz')) {
                return 'DELİKSİZ';
            }
        }

        // 3. stokKodu kontrol et ve belirli stok kodu paternlerine göre kategori belirle
        if (stokKodu && typeof stokKodu === 'string') {
            // Örnek stok kodu eşleştirme - gerçek stok kodu desenlerine göre ayarlanmalı
            if (stokKodu.includes('MGS')) {
                return 'DELİKLİ';
            }
        }

        // Varsayılan kategori
        return 'DELİKSİZ';
    }

    /**
     * Belirli tarih aralığındaki verileri filtreler
     */
    static filterDataByDateRange(
        data: SiloData[],
        startDate: Date | null,
        endDate: Date | null
    ): SiloData[] {
        if (!startDate && !endDate) return data;

        return data.filter(item => {
            const itemDate = new Date(
                item.tarih.getFullYear(),
                item.tarih.getMonth(),
                item.tarih.getDate()
            );

            if (startDate && endDate) {
                const start = new Date(
                    startDate.getFullYear(),
                    startDate.getMonth(),
                    startDate.getDate()
                );
                const end = new Date(
                    endDate.getFullYear(),
                    endDate.getMonth(),
                    endDate.getDate()
                );

                return itemDate >= start && itemDate <= end;
            } else if (startDate) {
                const start = new Date(
                    startDate.getFullYear(),
                    startDate.getMonth(),
                    startDate.getDate()
                );
                return itemDate >= start;
            } else if (endDate) {
                const end = new Date(
                    endDate.getFullYear(),
                    endDate.getMonth(),
                    endDate.getDate()
                );
                return itemDate <= end;
            }

            return true;
        });
    }

    /**
     * En son tarihe ait verileri getirir
     */
    static getLatestDateData(data: SiloData[]): SiloData[] {
        if (data.length === 0) return [];

        // Önce verileri tarihe göre sırala (en yeni tarih en üstte)
        const sortedDates = [...data].sort((a, b) => b.tarih.getTime() - a.tarih.getTime());

        // En son üretim yapılmış günü bul (boş olmayan ilk tarih)
        const latestDate = new Date(
            sortedDates[0].tarih.getFullYear(),
            sortedDates[0].tarih.getMonth(),
            sortedDates[0].tarih.getDate()
        );

        // O tarihe ait tüm verileri filtrele
        return data.filter(item => {
            const itemDate = new Date(
                item.tarih.getFullYear(),
                item.tarih.getMonth(),
                item.tarih.getDate()
            );
            return itemDate.getTime() === latestDate.getTime();
        });
    }

    /**
     * Verileri çap bilgisine göre filtreler
     */
    static filterDataByDiameter(
        data: SiloData[],
        diameters: string[],
        getUrunCap: (stokKodu: string) => string
    ): SiloData[] {
        if (!diameters || diameters.length === 0) return data;

        return data.filter(item => {
            const cap = getUrunCap(item.stokKodu);
            return diameters.includes(cap);
        });
    }

    /**
     * Ürün tipine göre verileri gruplar
     */
    static groupDataByProductType(data: SiloData[]): Record<string, SiloData[]> {
        const grouped: Record<string, SiloData[]> = {};

        data.forEach(item => {
            if (!grouped[item.stokKodu]) {
                grouped[item.stokKodu] = [];
            }
            grouped[item.stokKodu].push(item);
        });

        return grouped;
    }

    /**
     * Veriyi belirli bir alana göre gruplandırır
     */
    static groupBy<T>(array: T[], key: string): Record<string, T[]> {
        return array.reduce((acc: Record<string, T[]>, item: any) => {
            const groupKey = item[key] || 'undefined';
            if (!acc[groupKey]) {
                acc[groupKey] = [];
            }
            acc[groupKey].push(item);
            return acc;
        }, {});
    }

    /**
     * Gruplara göre istatistik hesaplar
     */
    static calculateGroupStats(grouped: Record<string, SiloData[]>): any[] {
        const result: any[] = []

        Object.entries(grouped).forEach(([stokKodu, items]) => {
            // Toplam ağırlık hesapla
            const totalWeight = items.reduce((sum, item) => {
                return sum + (item.boruAg || 0);
            }, 0);

            result.push({
                stokKodu,
                adet: items.length,
                toplamAgirlik: totalWeight,
                items
            });
        });

        return result;
    }

    static calculateTotalWeight(data: SiloData[]): number {
        return data.reduce((sum, item) => {
            return sum + (item.boruAg || 0);
        }, 0);
    }

    static calculateTotalLength(data: SiloData[], urunler: UrunModel[]): number {
        return data.reduce((sum, item) => {
            const urun = urunler.find(u => u.stokKodu === item.stokKodu);
            if (urun) {
                return sum + urun.uzunluk;
            }
            return sum;
        }, 0);
    }

    static getProductCategory(stokAdi: string): 'Delikli' | 'Deliksiz' | 'Keçeli' | null {
        const lowerName = stokAdi.toLowerCase();
        if (lowerName.includes('delikli')) {
            return 'Delikli';
        } else if (lowerName.includes('deliksiz')) {
            return 'Deliksiz';
        } else if (lowerName.includes('keçe') || lowerName.includes('kece')) {
            return 'Keçeli';
        }
        return null;
    }

    /**
     * Verilen SiloData listesini kategorilere göre gruplar
     * @param data SiloData listesi
     * @param urunler Ürün modelleri
     * @param getUrunAdi Ürün adını döndüren fonksiyon
     * @returns Her kategori için istatistik bilgilerini içeren nesne
     */
    static categorizeProducts(
        data: SiloData[],
        urunler: UrunModel[],
        getUrunAdi: (stokKodu: string) => string
    ): { category: string; stats: CategoryStats }[] {
        const categories: Record<string, {
            items: SiloData[];
            totalWeight: number;
            totalLength: number;
        }> = {
            'DELİKLİ': { items: [], totalWeight: 0, totalLength: 0 },
            'DELİKSİZ': { items: [], totalWeight: 0, totalLength: 0 },
            'KEÇELİ': { items: [], totalWeight: 0, totalLength: 0 }
        };

        // Her veriyi doğru kategoriye dağıt
        data.forEach(item => {
            const urunAdi = getUrunAdi(item.stokKodu);
            let kategori = 'DELİKSİZ'; // Varsayılan kategori

            if (urunAdi.toLowerCase().includes('delikli')) {
                kategori = 'DELİKLİ';
            } else if (urunAdi.toLowerCase().includes('keçeli') || urunAdi.toLowerCase().includes('keceli')) {
                kategori = 'KEÇELİ';
            }

            // İlgili kategoriye ekle
            categories[kategori].items.push(item);

            // Ağırlık toplamını güncelle
            categories[kategori].totalWeight += (item.boruAg || 0);
        });

        // Kategori bazlı toplam uzunluk hesabı
        // Önce stok koduna göre grupla
        Object.keys(categories).forEach(kategori => {
            // Stok kodlarını grupla ve say
            const stokKoduSayisi: Record<string, number> = {};
            categories[kategori].items.forEach(item => {
                const stokKodu = item.stokKodu;
                stokKoduSayisi[stokKodu] = (stokKoduSayisi[stokKodu] || 0) + 1;
            });

            // Her stok kodu için uzunluk * adet hesapla
            Object.entries(stokKoduSayisi).forEach(([stokKodu, adet]) => {
                const urunModel = urunler.find(urun => urun.stokKodu === stokKodu);
                if (urunModel && urunModel.uzunluk) {
                    // Toplam uzunluk = boru sayısı * boru uzunluğu
                    categories[kategori].totalLength += adet * urunModel.uzunluk;
                }
            });

            console.log(`${kategori} kategorisi: ${categories[kategori].items.length} adet, toplam uzunluk: ${categories[kategori].totalLength}`);
        });

        // Her kategori için istatistikleri hesapla
        return Object.entries(categories).map(([category, data]) => {
            const count = data.items.length;
            const totalWeight = data.totalWeight;
            const totalLength = data.totalLength;

            // Ağırlık/uzunluk hesapla (kg/m)
            const weightPerMeter = totalLength > 0 ? totalWeight / totalLength : 0;

            // Ortalama ağırlık
            const avgWeight = count > 0 ? totalWeight / count : 0;

            return {
                category,
                stats: {
                    count,
                    totalLength,
                    avgWeight,
                    totalWeight,
                    weightPerMeter
                }
            };
        });
    }

    /**
     * Her kategorinin toplam uzunluğunu hesaplar
     * @param data SiloData listesi
     * @param urunler Ürün modelleri listesi
     * @returns Her kategori için toplam uzunluk
     */
    static calculateTotalLengthByCategory(
        data: SiloData[],
        urunler: UrunModel[],
        getUrunAdi: (stokKodu: string) => string
    ): Record<string, number> {
        const result: Record<string, number> = {
            'DELİKLİ': 0,
            'DELİKSİZ': 0,
            'KEÇELİ': 0
        };

        // Her bir kayıt için kategori belirle ve uzunluk değerini topla
        data.forEach(item => {
            // Ürün adını kullanarak kategori belirle
            const urunAdi = getUrunAdi(item.stokKodu);
            let kategori = 'DELİKSİZ'; // Varsayılan kategori

            if (urunAdi.toLowerCase().includes('delikli')) {
                kategori = 'DELİKLİ';
            } else if (urunAdi.toLowerCase().includes('keçeli') || urunAdi.toLowerCase().includes('keceli')) {
                kategori = 'KEÇELİ';
            }

            // İlgili ürünün uzunluğunu bul ve toplam uzunluğu güncelle
            const urunModel = urunler.find(urun => urun.stokKodu === item.stokKodu);
            if (urunModel && urunModel.uzunluk) {
                result[kategori] += urunModel.uzunluk;
            }
        });

        return result;
    }

    static calculateProductTypeSummary(
        data: SiloData[],
        getUrunAdi: (stokKodu: string) => string,
        urunler?: UrunModel[]
    ): ProductTypeSummary[] {
        const groupedByType: Record<string, SiloData[]> = {};

        for (const item of data) {
            const key = item.stokKodu;
            if (!groupedByType[key]) {
                groupedByType[key] = [];
            }
            groupedByType[key].push(item);
        }

        const summary: ProductTypeSummary[] = [];

        Object.entries(groupedByType).forEach(([stokKodu, items]) => {
            const totalWeight = items.reduce((sum, item) => {
                return sum + (item.boruAg || 0);
            }, 0);

            const urunAdi = getUrunAdi(stokKodu);
            const count = items.length;

            const kategori = this.getProductCategory(urunAdi);

            let uzunluk = 0;
            if (urunler) {
                const urun = urunler.find(u => u.stokKodu === stokKodu);
                if (urun) {
                    uzunluk = urun.uzunluk * count;
                }
            }

            summary.push({
                stokKodu,
                urunAdi,
                toplamAgirlik: totalWeight,
                adet: count,
                kategori: kategori || undefined,
                uzunluk
            });
        });

        return summary;
    }
} 

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Company } from "../types";

interface AddUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  newUser: {
    name: string;
    email: string;
    role: string;
    companyId: number;
  };
  onNewUserChange: (user: {
    name: string;
    email: string;
    role: string;
    companyId: number;
  }) => void;
  onAddUser: () => void;
  companies: Company[];
}

export const AddUserDialog = ({
  open,
  onOpenChange,
  newUser,
  onNewUserChange,
  onAddUser,
  companies
}: AddUserDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle><PERSON><PERSON></DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="userName">Ad Soyad</Label>
            <Input
              id="userName"
              value={newUser.name}
              onChange={(e) => onNewUserChange({...newUser, name: e.target.value})}
              placeholder="Kullanıcı adı ve soyadı"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="userEmail">E-posta</Label>
            <Input
              id="userEmail"
              type="email"
              value={newUser.email}
              onChange={(e) => onNewUserChange({...newUser, email: e.target.value})}
              placeholder="<EMAIL>"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="userRole">Rol</Label>
            <select
              id="userRole"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={newUser.role}
              onChange={(e) => onNewUserChange({...newUser, role: e.target.value})}
            >
              <option value="Müdür">Müdür</option>
              <option value="Muhasebeci">Muhasebeci</option>
              <option value="Satış Temsilcisi">Satış Temsilcisi</option>
              <option value="Depo Sorumlusu">Depo Sorumlusu</option>
              <option value="Kullanıcı">Kullanıcı</option>
            </select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="userCompany">Firma</Label>
            <select
              id="userCompany"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={newUser.companyId}
              onChange={(e) => onNewUserChange({...newUser, companyId: parseInt(e.target.value)})}
            >
              <option value="0">Firma Seçin</option>
              {companies.filter(c => c.id !== 1).map(company => (
                <option key={company.id} value={company.id}>
                  {company.name} ({company.code})
                </option>
              ))}
            </select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>İptal</Button>
          <Button onClick={onAddUser}>Kullanıcı Ekle</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

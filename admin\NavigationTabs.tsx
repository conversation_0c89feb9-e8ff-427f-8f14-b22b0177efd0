import React from 'react';
import { useNavigate } from 'react-router-dom';

interface NavigationTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const NavigationTabs = ({ activeTab, setActiveTab }: NavigationTabsProps) => {
  const navigate = useNavigate();

  return (
    <div className="border-b border-gray-200 mb-6">
      <div className="flex">
        <button
          className={`px-6 py-4 font-medium relative transition-all duration-200 ${activeTab === 'uretim'
            ? 'text-blue-600'
            : 'text-gray-500 hover:text-gray-800'
            }`}
          onClick={() => {
            setActiveTab('uretim');
            navigate('/admin');
          }}
        >
          Üretim Verileri
          {activeTab === 'uretim' && (
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 rounded-full"></span>
          )}
        </button>
        <button
          className={`px-6 py-4 font-medium relative transition-all duration-200 ${activeTab === 'ayarlar'
            ? 'text-blue-600'
            : 'text-gray-500 hover:text-gray-800'
            }`}
          onClick={() => {
            setActiveTab('ayarlar');
            navigate('/settings');
          }}
        >
          Ayarlar
          {activeTab === 'ayarlar' && (
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 rounded-full"></span>
          )}
        </button>
      </div>
    </div>
  );
};

export default NavigationTabs;

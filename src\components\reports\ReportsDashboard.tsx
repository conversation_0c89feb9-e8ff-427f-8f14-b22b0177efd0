
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ReportCard } from "./ReportCard";
import { SavedReportsList } from "./SavedReportsList";
import { ScheduledReportsList } from "./ScheduledReportsList";
import { SalesByTimeChart } from "./SalesByTimeChart";
import { OrderStatusChart } from "./OrderStatusChart";
import { InventoryValueChart } from "./InventoryValueChart";
import { useReportsStore } from "@/stores/reportsStore";
import { CreateReportForm } from "./CreateReportForm";

export const ReportsDashboard = () => {
  const { savedReports, scheduledReports } = useReportsStore();
  
  return (
    <Tabs defaultValue="overview" className="space-y-4">
      <TabsList>
        <TabsTrigger value="overview"><PERSON><PERSON> Bakış</TabsTrigger>
        <TabsTrigger value="saved"><PERSON><PERSON>ilen Raporlar ({savedReports.length})</TabsTrigger>
        <TabsTrigger value="scheduled">Zamanlanmış Raporlar ({scheduledReports.length})</TabsTrigger>
        <TabsTrigger value="custom">Özel Rapor Oluştur</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <ReportCard 
            title="Satış Performansı" 
            description="Son 30 günlük satış grafiği"
            icon="chart-line"
          >
            <SalesByTimeChart />
          </ReportCard>
          
          <ReportCard 
            title="Sipariş Durumları" 
            description="Siparişlerin durum dağılımı"
            icon="chart-pie"
          >
            <OrderStatusChart />
          </ReportCard>
          
          <ReportCard 
            title="Envanter Değeri" 
            description="Kategorilere göre envanter değeri"
            icon="chart-bar"
          >
            <InventoryValueChart />
          </ReportCard>
        </div>
      </TabsContent>
      
      <TabsContent value="saved" className="space-y-4">
        <SavedReportsList />
      </TabsContent>
      
      <TabsContent value="scheduled" className="space-y-4">
        <ScheduledReportsList />
      </TabsContent>
      
      <TabsContent value="custom" className="space-y-4">
        <div className="rounded-lg border p-6">
          <h3 className="text-xl font-medium mb-4">Özel Rapor Oluştur</h3>
          <p className="text-muted-foreground mb-6">
            İşletmenizin ihtiyaçlarına göre özelleştirilmiş raporlar oluşturun.
            Veri kaynaklarını, filtreleri ve görselleştirme tipini seçin.
          </p>
          <div className="text-center">
            <CreateReportForm />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};

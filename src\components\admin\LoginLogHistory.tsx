
import { useState } from "react";
import { useAuth, LoginLogType } from "@/contexts/AuthContext";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Clock, User, Globe } from "lucide-react";

export const LoginLogHistory = () => {
  const { loginLogs, user } = useAuth();
  const [displayCount, setDisplayCount] = useState(10); // Default number of logs to display

  // Filter logs for the current user if needed
  const filteredLogs = loginLogs
    .filter(log => user ? log.userId === user.id : true) // If user exists, filter their logs only
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()) // Sort by date (newest first)
    .slice(0, displayCount); // Limit the number of logs shown

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'dd.MM.yyyy HH:mm:ss');
    } catch {
      return timestamp;
    }
  };

  const loadMore = () => {
    setDisplayCount(prev => prev + 10);
  };

  // Check if there are more logs to load
  const hasMoreLogs = loginLogs.filter(log => user ? log.userId === user.id : true).length > displayCount;

  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle className="text-xl flex items-center gap-2">
          <Clock className="h-5 w-5 text-blue-500" />
          Giriş Geçmişi
        </CardTitle>
        <CardDescription>
          Son giriş bilgileri ve aktivite logları
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableCaption>
            {filteredLogs.length === 0 ? (
              "Henüz giriş kaydı bulunmamaktadır."
            ) : hasMoreLogs ? (
              <button 
                onClick={loadMore} 
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                Daha fazla göster...
              </button>
            ) : (
              "Tüm giriş kayıtları gösteriliyor."
            )}
          </TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">Tarih/Saat</TableHead>
              <TableHead>Kullanıcı</TableHead>
              <TableHead>Şirket</TableHead>
              <TableHead>IP Adresi</TableHead>
              <TableHead className="hidden md:table-cell">Tarayıcı</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-muted-foreground py-10">
                  Henüz giriş kaydı bulunmamaktadır.
                </TableCell>
              </TableRow>
            ) : (
              filteredLogs.map((log, index) => (
                <TableRow key={index}>
                  <TableCell className="whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {formatTimestamp(log.timestamp)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {log.userName}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {log.companyName}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      {log.ipAddress}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell truncate max-w-[200px]">
                    {log.userAgent ? (
                      <span title={log.userAgent} className="text-xs text-muted-foreground">
                        {log.userAgent.substring(0, 50)}...
                      </span>
                    ) : (
                      "Bilinmiyor"
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};


import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CustomerStatistics } from "./CustomerStatistics";
import { CustomerList } from "./CustomerList";
import { SupplierList } from "./SupplierList";

export const CustomerSupplierManagement = () => {
  return (
    <div className="space-y-6">
      <CustomerStatistics />
      <Tabs defaultValue="customers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="customers">Müşteriler</TabsTrigger>
          <TabsTrigger value="suppliers">Tedarikçiler</TabsTrigger>
        </TabsList>
        
        <TabsContent value="customers">
          <CustomerList />
        </TabsContent>
        
        <TabsContent value="suppliers">
          <SupplierList />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import { Card } from "@/components/ui/card";
import { useInventoryStore } from "@/stores/inventoryStore";
import { Package2, AlertCircle, TrendingUp, Truck } from "lucide-react";

const InventoryStats = () => {
  const { products, categories, suppliers, lowStockProducts } = useInventoryStore();
  
  // Ürünlerin toplam değerini hesaplama
  const totalValue = products.reduce((acc, product) => {
    return acc + (product.price * product.currentStock);
  }, 0);

  // Son 30 gündeki ürün giriş/çıkışları (demo için sabit değer)
  const stockMovementsLastMonth = 28;

  const stats = [
    {
      title: "Toplam Ürün",
      value: products.length,
      icon: Package2,
      color: "text-blue-500",
    },
    {
      title: "Düşük Stok",
      value: lowStockProducts.length,
      icon: AlertCircle,
      color: "text-red-500",
    },
    {
      title: "<PERSON><PERSON><PERSON>",
      value: `₺${totalValue.toLocaleString('tr-TR')}`,
      icon: TrendingUp,
      color: "text-green-500",
    },
    {
      title: "Tedarikçiler",
      value: suppliers.length,
      icon: Truck,
      color: "text-orange-500",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title} className="hover-card p-6">
          <div className="flex items-center gap-4">
            <stat.icon className={`h-8 w-8 ${stat.color}`} />
            <div>
              <p className="text-sm text-muted-foreground">{stat.title}</p>
              <h3 className="text-2xl font-semibold">{stat.value}</h3>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default InventoryStats;

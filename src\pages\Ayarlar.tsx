
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DatabaseConnectionTest } from "@/components/admin/DatabaseConnectionTest";
import { LoginLogHistory } from "@/components/admin/LoginLogHistory";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Shield, Database, User } from "lucide-react";
import { useLocation } from "react-router-dom";

const Ayarlar = () => {
  const [dbInfo, setDbInfo] = useState({
    host: 'Yükleniyor...',
    port: 'Yükleniyor...',
    name: 'Yükleniyor...'
  });

  const [isDevMode, setIsDevMode] = useState(false);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const defaultTab = queryParams.get('tab') || "general";

  useEffect(() => {
    // Detect if we're in development mode (without backend access)
    const isDevEnv = window.location.hostname === 'localhost' ||
      window.location.hostname.includes('lovableproject.com');
    setIsDevMode(isDevEnv);

    // Mock data for development
    if (isDevEnv) {
      console.log('Geliştirme modunda çalışıyor - mock veriler kullanılıyor');
      // Fetch için 2 saniye gecikme ekleyelim ki yükleme durumunu görebilelim
      const timer = setTimeout(() => {
        const mockData = {
          connection_info: {
            host: '*************',
            port: '3306',
            database: 'finans_db',
            user: 'mehmet'
          }
        };

        setDbInfo({
          host: mockData.connection_info.host,
          port: mockData.connection_info.port,
          name: mockData.connection_info.database
        });

        // Bilgilendirme mesajı gösterelim
        toast.info('Geliştirme modunda - Backend bağlantısı simüle ediliyor', {
          duration: 5000
        });
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      // Normal API isteği
      fetch('/api/test/connection')
        .then(response => {
          if (!response.ok) {
            throw new Error(`API yanıt vermedi: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.connection_info) {
            setDbInfo({
              host: data.connection_info.host || 'Belirtilmemiş',
              port: data.connection_info.port || '3306',
              name: data.connection_info.database || 'Belirtilmemiş'
            });
          }
        })
        .catch(error => {
          console.error('Veritabanı bilgileri alınırken hata oluştu:', error);
          toast.error('Veritabanı bilgileri alınamadı');
          setDbInfo({
            host: 'Bağlantı hatası',
            port: 'Bağlantı hatası',
            name: 'Bağlantı hatası'
          });
        });
    }
  }, []);

  return (
    <MainLayout>
      <div className="flex flex-col">
        <div className="flex-1 space-y-4 p-0 pb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-3xl font-bold tracking-tight">Sistem Ayarları</h2>
            {isDevMode && (
              <div className="flex items-center bg-yellow-50 text-yellow-700 px-3 py-1 rounded-md text-sm border border-yellow-200">
                <Shield className="h-4 w-4 mr-2" />
                Geliştirme Modu
              </div>
            )}
          </div>

          <Tabs defaultValue={defaultTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="general">Genel Ayarlar</TabsTrigger>
              <TabsTrigger value="security">Güvenlik</TabsTrigger>
              <TabsTrigger value="database">Veritabanı</TabsTrigger>
              <TabsTrigger value="backup">Yedekleme</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Genel Sistem Ayarları</CardTitle>
                  <CardDescription>
                    Temel sistem yapılandırması
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Bu bölüm geliştirme aşamasındadır.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
                <LoginLogHistory />
              </div>
            </TabsContent>

            <TabsContent value="database" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
                <div className="col-span-2">
                  <DatabaseConnectionTest isDevMode={isDevMode} />
                </div>

                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Veritabanı Bilgileri</CardTitle>
                      <CardDescription>Sistem ayarları ve bağlantı bilgileri</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-sm font-medium">Veritabanı Tipi</div>
                          <div className="text-sm">MySQL</div>
                        </div>
                        <Separator className="my-1" />
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-sm font-medium">Host</div>
                          <div className="text-sm">{dbInfo.host}</div>
                        </div>
                        <Separator className="my-1" />
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-sm font-medium">Port</div>
                          <div className="text-sm">{dbInfo.port}</div>
                        </div>
                        <Separator className="my-1" />
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-sm font-medium">Veritabanı</div>
                          <div className="text-sm">{dbInfo.name}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Araçlar</CardTitle>
                      <CardDescription>Veritabanı yönetim araçları</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">
                          Veritabanı yönetimi için kullanılabilecek araçlar:
                        </p>
                        <ul className="list-disc list-inside text-sm space-y-1">
                          <li>MySQL Workbench</li>
                          <li>phpMyAdmin</li>
                          <li>DBeaver</li>
                          <li>HeidiSQL</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="backup">
              <Card>
                <CardHeader>
                  <CardTitle>Yedekleme Ayarları</CardTitle>
                  <CardDescription>
                    Veri yedekleme ve geri yükleme
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Bu bölüm geliştirme aşamasındadır.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
};

export default Ayarlar;

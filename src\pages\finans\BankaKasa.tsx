
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { BankManagement } from "@/components/billing/BankManagement";
import { CashBoxManagement } from "@/components/billing/CashBoxManagement";

const BankaKasa = () => {
  const tabs: TabItem[] = [
    { id: "bankAccounts", label: "Banka Hesapları", component: <BankManagement /> },
    { id: "cashRegisters", label: "Kasa Yönetimi", component: <CashBoxManagement /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Banka ve Kasa Yönetimi</h2>
          <p className="text-muted-foreground">
            Banka hesapları ve kasa yönetimi işlemleri
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default BankaKasa;

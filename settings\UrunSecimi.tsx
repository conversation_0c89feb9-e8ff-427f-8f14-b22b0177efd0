
import React from 'react';
import { UrunModel } from '../src/models/UrunModel';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Statik ürün listesi - Hat 1, 2, 3 i<PERSON><PERSON> (KEÇELİ DÖNÜŞÜM eklendi)
const StaticUrunData = {
  boruTipleri: ["Ø 80", "Ø 100", "Ø 125", "Ø 160", "Ø 200"],
  altUrunler: {
    "Ø 80": [
      { ad: "Ø 80 DELİKSİZ BORU", stokKodu: "MRZ-ST00662" },
      { ad: "Ø 80 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST00663" },
      { ad: "Ø 80 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST10663" }
    ],
    "Ø 100": [
      { ad: "Ø 100 DELİKSİZ BORU", stokKodu: "MRZ-ST00659" },
      { ad: "Ø 100 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02549" },
      { ad: "Ø 100 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12549" }
    ],
    "Ø 125": [
      { ad: "Ø 125 DELİKSİZ BORU", stokKodu: "MRZ-ST00664" },
      { ad: "Ø 125 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST00666" },
      { ad: "Ø 125 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST10666" }
    ],
    "Ø 160": [
      { ad: "Ø 160 DELİKSİZ BORU", stokKodu: "MRZ-ST00658" },
      { ad: "Ø 160 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02550" },
      { ad: "Ø 160 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12550" }
    ],
    "Ø 200": [
      { ad: "Ø 200 DELİKSİZ BORU", stokKodu: "MRZ-ST00660" },
      { ad: "Ø 200 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02551" },
      { ad: "Ø 200 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12551" }
    ]
  }
};

interface UrunSecimiProps {
  urunler: UrunModel[];
  secilenUrunTipi: string;
  secilenAltUrun: string;
  handleUrunTipiChange: (value: string) => void;
  handleAltUrunChange: (value: string) => void;
  handleSaveStokKodu: () => void;
  paketNo?: number;
}

const UrunSecimi = ({
  urunler,
  secilenUrunTipi,
  secilenAltUrun,
  handleUrunTipiChange,
  handleAltUrunChange,
  handleSaveStokKodu,
  paketNo = 1
}: UrunSecimiProps) => {
  // Seçilen boru tipine göre alt ürünleri getir
  const getAltUrunler = () => {
    if (!secilenUrunTipi || !StaticUrunData.altUrunler[secilenUrunTipi]) {
      return [];
    }
    return StaticUrunData.altUrunler[secilenUrunTipi];
  };

  // Seçilen alt ürünün ismini bul
  const getSecilenAltUrunAdi = () => {
    if (!secilenUrunTipi || !secilenAltUrun) return '';

    const altUrun = StaticUrunData.altUrunler[secilenUrunTipi]?.find(
      item => item.stokKodu === secilenAltUrun
    );

    return altUrun?.ad || '';
  };

  return (
    <Card className="p-3 shadow-lg border border-gray-200 rounded-xl bg-white h-full flex flex-col">
      {/* Başlık */}
      <div className="flex items-center gap-1 mb-1">
        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span className="font-medium text-blue-600 text-xs">Ürün Seçimi</span>
      </div>

      <div className="mb-2 p-2 bg-blue-50 rounded-lg border border-blue-100">
        <div className="flex items-center text-xs">
          <svg className="w-3 h-3 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-gray-700">Seçilen stok kodu silodata tablosuna kaydedilir.</span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3 flex-grow">
        <div>
          <div className="font-medium text-xs text-gray-700 mb-1 flex items-center">
            <span className="bg-blue-100 text-blue-800 mr-1 rounded-full w-4 h-4 inline-flex items-center justify-center text-xs">1</span>
            Boru Tipi
          </div>
          <Select value={secilenUrunTipi} onValueChange={handleUrunTipiChange}>
            <SelectTrigger className="w-full rounded-lg border border-gray-300 bg-white py-1 text-xs">
              <SelectValue placeholder="Boru tipini seçiniz" />
            </SelectTrigger>
            <SelectContent>
              {StaticUrunData.boruTipleri.map(tip => (
                <SelectItem key={tip} value={tip} className="text-xs">
                  {tip}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <div className="font-medium text-xs text-gray-700 mb-1 flex items-center">
            <span className="bg-blue-100 text-blue-800 mr-1 rounded-full w-4 h-4 inline-flex items-center justify-center text-xs">2</span>
            Alt Ürün
          </div>
          <Select
            value={secilenAltUrun}
            onValueChange={handleAltUrunChange}
            disabled={!secilenUrunTipi}
          >
            <SelectTrigger className="w-full rounded-lg border border-gray-300 bg-white py-1 text-xs">
              <SelectValue placeholder="Alt ürün seçin" />
            </SelectTrigger>
            <SelectContent>
              {getAltUrunler().map(urun => (
                <SelectItem key={urun.stokKodu} value={urun.stokKodu} className="text-xs">
                  {urun.ad}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {secilenAltUrun && (
          <div className="mt-1 p-2 bg-green-50 rounded-lg border border-green-100">
            <div className="flex items-center justify-between text-xs">
              <div className="font-medium text-gray-700">Paket No:</div>
              <div className="font-bold text-blue-700">{paketNo}</div>
            </div>
          </div>
        )}
      </div>

      <div className="mt-auto pt-2">
        <Button
          onClick={handleSaveStokKodu}
          disabled={!secilenAltUrun}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 rounded-lg font-medium text-xs shadow-md disabled:bg-gray-300 disabled:text-gray-600 transition-all duration-200"
        >
          Stok Kodunu Kaydet
        </Button>
      </div>
    </Card>
  );
};

export default UrunSecimi;

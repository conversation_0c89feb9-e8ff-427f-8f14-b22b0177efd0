
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";
import { useVehicleStore } from "@/stores/vehicleStore";
import { Vehicle, VehicleType, VehicleStatus, FuelType } from "@/stores/vehicleStore";

export function CreateVehicleDialog() {
  const [open, setOpen] = useState(false);
  const addVehicle = useVehicleStore((state) => state.addVehicle);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const vehicle: Omit<Vehicle, 'id' | 'createdAt'> = {
      name: formData.get('name') as string,
      plate: formData.get('plate') as string,
      type: formData.get('type') as VehicleType,
      brand: formData.get('brand') as string,
      model: formData.get('model') as string,
      modelYear: parseInt(formData.get('modelYear') as string),
      vin: formData.get('vin') as string,
      status: formData.get('status') as VehicleStatus,
      assignedTo: null,
      department: formData.get('department') as string,
      fuelType: formData.get('fuelType') as FuelType,
      purchaseDate: new Date().toISOString(),
      initialOdometer: parseInt(formData.get('initialOdometer') as string),
      currentOdometer: parseInt(formData.get('initialOdometer') as string),
      insuranceExpiry: new Date().toISOString(),
      inspectionExpiry: new Date().toISOString(),
      notes: formData.get('notes') as string,
    };

    addVehicle(vehicle);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Yeni Araç Ekle</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Yeni Araç Ekle</DialogTitle>
            <DialogDescription>
              Yeni bir araç eklemek için aşağıdaki bilgileri doldurun.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Araç Adı
              </Label>
              <Input id="name" name="name" className="col-span-3" required />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="plate" className="text-right">
                Plaka
              </Label>
              <Input id="plate" name="plate" className="col-span-3" required />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Tip
              </Label>
              <Select name="type" required>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Araç tipi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="car">Otomobil</SelectItem>
                  <SelectItem value="truck">Kamyon</SelectItem>
                  <SelectItem value="van">Van</SelectItem>
                  <SelectItem value="bus">Otobüs</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* Additional fields */}
          </div>
          <DialogFooter>
            <Button type="submit">Kaydet</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}


import express from 'express';
import { query } from '../../utils/db';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Tüm cari hesapları getir
router.get('/', async (req, res) => {
  try {
    const cariHesaplar = await query('SELECT * FROM cari_hesaplar ORDER BY olusturma_tarihi DESC');
    res.json(cariHesaplar);
  } catch (error) {
    console.error('Cari hesapları getirme hatası:', error);
    res.status(500).json({ error: 'Cari hesaplar getirilemedi' });
  }
});

// ID'ye göre cari hesap getir
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const cariHesap = await query('SELECT * FROM cari_hesaplar WHERE id = ?', [id]);
    
    if (Array.isArray(cariHesap) && cariHesap.length === 0) {
      return res.status(404).json({ error: 'Cari hesap bulunamadı' });
    }
    
    res.json(cariHesap[0]);
  } catch (error) {
    console.error('Cari hesap getirme hatası:', error);
    res.status(500).json({ error: 'Cari hesap getirilemedi' });
  }
});

// Yeni cari hesap ekle
router.post('/', async (req, res) => {
  try {
    const { kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!kod || !unvan || !tip) {
      return res.status(400).json({ error: 'Kod, unvan ve tip alanları zorunludur' });
    }
    
    // ID oluştur
    const id = uuidv4();
    const kayitTarihi = new Date().toISOString().split('T')[0];
    
    await query(
      `INSERT INTO cari_hesaplar 
      (id, kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar, kayitTarihi) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar, kayitTarihi]
    );
    
    res.status(201).json({ 
      message: 'Cari hesap başarıyla oluşturuldu', 
      id 
    });
  } catch (error) {
    console.error('Cari hesap oluşturma hatası:', error);
    res.status(500).json({ error: 'Cari hesap oluşturulamadı' });
  }
});

// Cari hesap güncelle
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!kod || !unvan || !tip) {
      return res.status(400).json({ error: 'Kod, unvan ve tip alanları zorunludur' });
    }
    
    await query(
      `UPDATE cari_hesaplar 
      SET kod = ?, unvan = ?, vknTckn = ?, tip = ?, telefon = ?, email = ?, 
      vergi_dairesi = ?, adres = ?, ilce = ?, il = ?, postaKodu = ?, iban = ?, notlar = ? 
      WHERE id = ?`,
      [kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar, id]
    );
    
    res.json({ message: 'Cari hesap başarıyla güncellendi' });
  } catch (error) {
    console.error('Cari hesap güncelleme hatası:', error);
    res.status(500).json({ error: 'Cari hesap güncellenemedi' });
  }
});

// Cari hesap sil
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await query('DELETE FROM cari_hesaplar WHERE id = ?', [id]);
    res.json({ message: 'Cari hesap başarıyla silindi' });
  } catch (error) {
    console.error('Cari hesap silme hatası:', error);
    res.status(500).json({ error: 'Cari hesap silinemedi' });
  }
});

export default router;

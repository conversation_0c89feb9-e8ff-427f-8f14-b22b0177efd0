
import { useReportsStore } from "@/stores/reportsStore";
import { useEffect, useState } from "react";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

export const InventoryValueChart = () => {
  const { inventoryData, generateDemoData } = useReportsStore();
  const [chartData, setChartData] = useState<any[]>([]);
  
  useEffect(() => {
    if (inventoryData.length === 0) {
      generateDemoData();
    } else {
      // Format data for chart
      setChartData(inventoryData);
    }
  }, [inventoryData, generateDemoData]);

  if (chartData.length === 0) {
    return <div className="flex justify-center items-center h-60">Yükleniyor...</div>;
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="product" tick={{ fontSize: 10 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip 
            formatter={(value: string) => [`₺${parseFloat(value).toLocaleString('tr-TR')}`, 'Değer']}
          />
          <Bar dataKey="value" fill="#82ca9d" name="Stok Değeri" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};


import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { CreditCard, Edit, Settings } from "lucide-react";
import { Company } from "../types";

interface CompanyListItemProps {
  company: Company;
  onToggleStatus: (id: number) => void;
  onEdit: (company: Company) => void;
}

export const CompanyListItem = ({ company, onToggleStatus, onEdit }: CompanyListItemProps) => {
  return (
    <div className="grid grid-cols-7 gap-4 p-3 items-center hover:bg-muted/20">
      <div className="col-span-2">
        <div className="font-medium">{company.name}</div>
        <div className="text-xs text-muted-foreground">{company.type}</div>
        {company.contactInfo?.email && (
          <div className="text-xs text-muted-foreground">{company.contactInfo.email}</div>
        )}
      </div>
      <div>
        <Badge variant="outline" className="font-mono">
          {company.code}
        </Badge>
      </div>
      <div>
        <div className="flex flex-col gap-1">
          <Badge variant="outline" className="flex items-center gap-1 w-fit mb-1">
            {company.modules.length + company.optionalModules.length} modül
          </Badge>
          <div className="flex gap-1">
            <span className="text-xs text-blue-600">Temel: {company.modules.length}</span>
            <span className="text-xs text-orange-600">Ops: {company.optionalModules.length}</span>
          </div>
        </div>
      </div>
      <div>
        {company.subscription?.plan ? (
          <div className="flex flex-col">
            <span className="text-sm font-medium">{company.subscription.plan}</span>
            {company.subscription.status && (
              <span className={`text-xs ${company.subscription.status === 'Aktif' ? 'text-green-600' : 'text-amber-600'}`}>
                {company.subscription.status}
              </span>
            )}
            {company.subscription.amount > 0 && (
              <span className="text-xs text-muted-foreground flex items-center gap-1">
                <CreditCard className="h-3 w-3" /> {company.subscription.amount} ₺/ay
              </span>
            )}
          </div>
        ) : (
          <span className="text-xs text-muted-foreground">Tanımsız</span>
        )}
      </div>
      <div>
        <Switch 
          checked={company.active}
          onCheckedChange={() => onToggleStatus(company.id)}
          disabled={company.id === 1} // Cannot disable admin
        />
      </div>
      <div className="flex space-x-2">
        <Button variant="ghost" size="sm" onClick={() => onEdit(company)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

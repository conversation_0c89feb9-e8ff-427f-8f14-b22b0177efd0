
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142.1 76.2% 36.3%;
    --success-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .glass-panel {
    @apply bg-white/80 backdrop-blur-lg border border-white/20 shadow-lg rounded-lg;
  }
  
  .dark .glass-panel {
    @apply bg-slate-900/80 backdrop-blur-lg border border-slate-800/40 shadow-lg;
  }
  
  .hover-card {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  .side-navigation {
    @apply fixed top-0 left-0 h-full w-64 glass-panel;
  }
  
  .card-gradient {
    @apply bg-gradient-to-br from-white to-slate-50 hover:from-blue-50 hover:to-white transition-all duration-300;
  }

  .dark .card-gradient {
    @apply bg-gradient-to-br from-slate-900 to-slate-800 hover:from-blue-900/30 hover:to-slate-900 transition-all duration-300;
  }

  .card-modern {
    @apply hover-card card-gradient border border-slate-200 dark:border-slate-700;
  }
  
  .badge-pulse {
    @apply relative inline-flex h-3 w-3;
  }
  
  .badge-pulse::before {
    @apply animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75 content-[''];
  }
  
  .stats-card {
    @apply flex flex-col p-6 rounded-xl shadow-sm border border-slate-200 bg-white hover:shadow-md transition-all duration-300 dark:bg-slate-800 dark:border-slate-700;
  }
  
  .stats-card-icon {
    @apply p-2 rounded-full bg-primary/10 text-primary mb-4 dark:bg-primary/20;
  }
  
  .dashboard-grid {
    @apply grid gap-6 md:grid-cols-2 lg:grid-cols-4;
  }
  
  .data-card {
    @apply p-6 rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

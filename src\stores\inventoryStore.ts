
import { create } from 'zustand';

// Türler
export type ProductCategory = {
  id: string;
  name: string;
  description?: string;
};

export type Supplier = {
  id: string;
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
};

export type Product = {
  id: string;
  name: string;
  sku: string;
  description?: string;
  price: number;
  cost: number;
  currentStock: number;
  minStockLevel: number;
  categoryId: string;
  supplierId: string;
  unit: string;
  barcode?: string;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type StockMovement = {
  id: string;
  productId: string;
  type: 'in' | 'out';
  quantity: number;
  reason: string;
  notes?: string;
  createdAt: Date;
};

type InventoryState = {
  products: Product[];
  categories: ProductCategory[];
  suppliers: Supplier[];
  stockMovements: StockMovement[];
  isLoading: boolean;
  error: string | null;
  lowStockProducts: Product[];

  // Ürün işlemleri
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, updates: Partial<Product>) => void;
  deleteProduct: (id: string) => void;

  // Kategori işlemleri
  addCategory: (category: Omit<ProductCategory, 'id'>) => void;
  updateCategory: (id: string, updates: Partial<ProductCategory>) => void;
  deleteCategory: (id: string) => void;

  // Tedarikçi işlemleri
  addSupplier: (supplier: Omit<Supplier, 'id'>) => void;
  updateSupplier: (id: string, updates: Partial<Supplier>) => void;
  deleteSupplier: (id: string) => void;

  // Stok hareketleri
  addStockMovement: (movement: Omit<StockMovement, 'id' | 'createdAt'>) => void;
  
  // Demo verilerini yükle
  loadDemoData: () => void;
};

export const useInventoryStore = create<InventoryState>((set, get) => ({
  products: [],
  categories: [],
  suppliers: [],
  stockMovements: [],
  isLoading: false,
  error: null,
  lowStockProducts: [],

  addProduct: (productData) => {
    const product: Product = {
      id: crypto.randomUUID(),
      ...productData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    set((state) => {
      const products = [...state.products, product];
      const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);
      return { 
        products,
        lowStockProducts
      };
    });
  },
  
  updateProduct: (id, updates) => {
    set((state) => {
      const products = state.products.map(product => 
        product.id === id 
          ? { ...product, ...updates, updatedAt: new Date() } 
          : product
      );
      const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);
      return { 
        products,
        lowStockProducts
      };
    });
  },
  
  deleteProduct: (id) => {
    set((state) => {
      const products = state.products.filter(product => product.id !== id);
      const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);
      return { 
        products,
        lowStockProducts
      };
    });
  },

  addCategory: (categoryData) => {
    const category: ProductCategory = {
      id: crypto.randomUUID(),
      ...categoryData,
    };

    set((state) => ({
      categories: [...state.categories, category],
    }));
  },
  
  updateCategory: (id, updates) => {
    set((state) => ({
      categories: state.categories.map(category => 
        category.id === id 
          ? { ...category, ...updates } 
          : category
      ),
    }));
  },
  
  deleteCategory: (id) => {
    set((state) => ({
      categories: state.categories.filter(category => category.id !== id),
    }));
  },

  addSupplier: (supplierData) => {
    const supplier: Supplier = {
      id: crypto.randomUUID(),
      ...supplierData,
    };

    set((state) => ({
      suppliers: [...state.suppliers, supplier],
    }));
  },
  
  updateSupplier: (id, updates) => {
    set((state) => ({
      suppliers: state.suppliers.map(supplier => 
        supplier.id === id 
          ? { ...supplier, ...updates } 
          : supplier
      ),
    }));
  },
  
  deleteSupplier: (id) => {
    set((state) => ({
      suppliers: state.suppliers.filter(supplier => supplier.id !== id),
    }));
  },

  addStockMovement: (movementData) => {
    const movement: StockMovement = {
      id: crypto.randomUUID(),
      ...movementData,
      createdAt: new Date(),
    };

    set((state) => ({
      stockMovements: [...state.stockMovements, movement],
    }));

    // Ürünün stok miktarını güncelle
    const { products } = get();
    const product = products.find(p => p.id === movementData.productId);
    
    if (product) {
      const newStock = movementData.type === 'in' 
        ? product.currentStock + movementData.quantity
        : product.currentStock - movementData.quantity;
      
      get().updateProduct(product.id, { currentStock: newStock });
    }
  },

  loadDemoData: () => {
    set((state) => {
      // Demo kategoriler
      const categories: ProductCategory[] = [
        { id: '1', name: 'Elektronik', description: 'Bilgisayarlar, telefonlar ve diğer elektronik cihazlar' },
        { id: '2', name: 'Mobilya', description: 'Ofis ve ev mobilyaları' },
        { id: '3', name: 'Giyim', description: 'Kıyafetler ve aksesuarlar' },
        { id: '4', name: 'Gıda', description: 'Yiyecek ve içecek ürünleri' },
      ];

      // Demo tedarikçiler
      const suppliers: Supplier[] = [
        { id: '1', name: 'TechCo', contactName: 'Ahmet Yılmaz', email: '<EMAIL>', phone: '0212 555 1234' },
        { id: '2', name: 'MobiTürk', contactName: 'Ayşe Demir', email: '<EMAIL>', phone: '0216 444 5678' },
        { id: '3', name: 'FashionWorld', contactName: 'Mehmet Can', email: '<EMAIL>', phone: '0312 333 9876' },
        { id: '4', name: 'FoodSupply', contactName: 'Zeynep Kaya', email: '<EMAIL>', phone: '0232 222 4444' },
      ];

      // Demo ürünler
      const products: Product[] = [
        {
          id: '1',
          name: 'Laptop X500',
          sku: 'EL001',
          description: 'Yüksek performanslı iş laptopu',
          price: 15000,
          cost: 12000,
          currentStock: 25,
          minStockLevel: 10,
          categoryId: '1',
          supplierId: '1',
          unit: 'adet',
          barcode: '1234567890123',
          location: 'A1-B2',
          createdAt: new Date(2023, 0, 15),
          updatedAt: new Date(2023, 5, 10),
        },
        {
          id: '2',
          name: 'Çalışma Masası',
          sku: 'MB001',
          description: 'Ergonomik çalışma masası',
          price: 2500,
          cost: 1800,
          currentStock: 8,
          minStockLevel: 10,
          categoryId: '2',
          supplierId: '2',
          unit: 'adet',
          barcode: '2345678901234',
          location: 'C3-D4',
          createdAt: new Date(2023, 1, 20),
          updatedAt: new Date(2023, 1, 20),
        },
        {
          id: '3',
          name: 'T-Shirt Basic',
          sku: 'GY001',
          description: 'Pamuklu basic t-shirt',
          price: 299.99,
          cost: 150,
          currentStock: 120,
          minStockLevel: 50,
          categoryId: '3',
          supplierId: '3',
          unit: 'adet',
          barcode: '3456789012345',
          location: 'E5-F6',
          createdAt: new Date(2023, 2, 5),
          updatedAt: new Date(2023, 6, 12),
        },
        {
          id: '4',
          name: 'Kahve Paketi',
          sku: 'GD001',
          description: 'Premium filtre kahve 250g',
          price: 149.99,
          cost: 90,
          currentStock: 45,
          minStockLevel: 20,
          categoryId: '4',
          supplierId: '4',
          unit: 'paket',
          barcode: '4567890123456',
          location: 'G7-H8',
          createdAt: new Date(2023, 3, 10),
          updatedAt: new Date(2023, 3, 10),
        },
      ];

      // Demo stok hareketleri
      const stockMovements: StockMovement[] = [
        {
          id: '1',
          productId: '1',
          type: 'in',
          quantity: 30,
          reason: 'Başlangıç stoku',
          notes: 'İlk sipariş',
          createdAt: new Date(2023, 0, 15),
        },
        {
          id: '2',
          productId: '1',
          type: 'out',
          quantity: 5,
          reason: 'Satış',
          notes: 'Online satış',
          createdAt: new Date(2023, 2, 20),
        },
        {
          id: '3',
          productId: '2',
          type: 'in',
          quantity: 15,
          reason: 'Başlangıç stoku',
          notes: 'İlk sipariş',
          createdAt: new Date(2023, 1, 20),
        },
        {
          id: '4',
          productId: '2',
          type: 'out',
          quantity: 7,
          reason: 'Satış',
          notes: 'Mağaza satışı',
          createdAt: new Date(2023, 4, 5),
        },
      ];

      // Düşük stoklu ürünleri belirle
      const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);

      return { 
        categories, 
        suppliers, 
        products, 
        stockMovements,
        lowStockProducts,
        isLoading: false,
        error: null
      };
    });
  },
}));

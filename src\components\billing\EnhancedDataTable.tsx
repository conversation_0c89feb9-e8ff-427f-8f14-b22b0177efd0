
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  getFilteredRowModel,
  ColumnFiltersState,
  FilterFn,
  VisibilityState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { ArrowUpDown, Download, Printer, Search, Filter } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
// Fix import statement for jsPDF
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import { toast } from "sonner";

interface EnhancedDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  searchable?: boolean;
  printable?: boolean;
}

export function EnhancedDataTable<TData, TValue>({
  columns,
  data,
  title = "Veri Tablosu",
  searchable = true,
  printable = true,
}: EnhancedDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      columnVisibility,
    },
  });

  const generatePDF = () => {
    try {
      const doc = new jsPDF();
      
      // Başlık ekle
      doc.setFontSize(16);
      doc.text(title, 14, 15);
      doc.setFontSize(10);
      
      // Filtreleme bilgisi
      if (columnFilters.length > 0 || globalFilter) {
        let filterText = "Filtreler: ";
        if (globalFilter) {
          filterText += `Genel: ${globalFilter}, `;
        }
        columnFilters.forEach((filter) => {
          filterText += `${filter.id}: ${filter.value}, `;
        });
        doc.text(filterText.slice(0, -2), 14, 22);
      }
      
      // Tarih bilgisi
      const now = new Date();
      doc.text(`Oluşturma Tarihi: ${now.toLocaleDateString('tr-TR')}`, 14, 28);
      
      // Tabloyu oluştur
      const headers = columns
        .filter(column => !column.id?.includes('actions') && table.getColumn(column.id as string)?.getIsVisible())
        .map(column => {
          const header = column.header?.toString() || '';
          return header;
        });
      
      const rows = table.getFilteredRowModel().rows.map(row => {
        return columns
          .filter(column => !column.id?.includes('actions') && table.getColumn(column.id as string)?.getIsVisible())
          .map(column => {
            const cell = row.getVisibleCells().find(cell => cell.column.id === column.id);
            if (!cell) return '';
            
            // Özel hücre render fonksiyonu varsa ve basit bir değer dönüyorsa
            const content = flexRender(column.cell, cell.getContext());
            
            // React bileşenlerini string'e dönüştürmeye çalış
            if (typeof content === 'object') {
              if ('props' in content && 'children' in content.props) {
                return String(content.props.children || '');
              }
              return '';
            }
            
            return String(content || '');
          });
      });
      
      autoTable(doc, {
        head: [headers],
        body: rows,
        startY: 35,
        theme: 'grid',
        styles: {
          font: 'helvetica',
          fontSize: 8,
          overflow: 'linebreak',
          cellPadding: 2,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
      });
      
      // PDF'i indir
      doc.save(`${title.replace(/\s+/g, '_')}_${now.toISOString().split('T')[0]}.pdf`);
      toast.success("PDF başarıyla oluşturuldu");
    } catch (error) {
      console.error("PDF oluşturulurken hata:", error);
      toast.error("PDF oluşturulurken bir hata oluştu");
    }
  };

  const printTable = () => {
    try {
      const printContent = document.createElement('div');
      printContent.innerHTML = `
        <h1 style="font-size: 18px; margin-bottom: 10px;">${title}</h1>
        <p style="font-size: 12px; margin-bottom: 15px;">Yazdırma Tarihi: ${new Date().toLocaleDateString('tr-TR')}</p>
      `;
      
      const tableClone = document.createElement('table');
      tableClone.style.width = '100%';
      tableClone.style.borderCollapse = 'collapse';
      tableClone.style.marginBottom = '20px';
      
      // Başlık satırı
      const thead = document.createElement('thead');
      const headerRow = document.createElement('tr');
      
      columns
        .filter(column => !column.id?.includes('actions') && table.getColumn(column.id as string)?.getIsVisible())
        .forEach(column => {
          const th = document.createElement('th');
          th.style.border = '1px solid #ddd';
          th.style.padding = '8px';
          th.style.backgroundColor = '#f2f2f2';
          th.style.textAlign = 'left';
          th.textContent = column.header?.toString() || '';
          headerRow.appendChild(th);
        });
      
      thead.appendChild(headerRow);
      tableClone.appendChild(thead);
      
      // Veri satırları
      const tbody = document.createElement('tbody');
      
      table.getFilteredRowModel().rows.forEach(row => {
        const tr = document.createElement('tr');
        
        columns
          .filter(column => !column.id?.includes('actions') && table.getColumn(column.id as string)?.getIsVisible())
          .forEach(column => {
            const td = document.createElement('td');
            td.style.border = '1px solid #ddd';
            td.style.padding = '8px';
            
            const cell = row.getVisibleCells().find(cell => cell.column.id === column.id);
            if (cell) {
              const content = flexRender(column.cell, cell.getContext());
              
              if (typeof content === 'object') {
                if ('props' in content && 'children' in content.props) {
                  td.textContent = String(content.props.children || '');
                } else {
                  td.textContent = '';
                }
              } else {
                td.textContent = String(content || '');
              }
            }
            
            tr.appendChild(td);
          });
        
        tbody.appendChild(tr);
      });
      
      tableClone.appendChild(tbody);
      printContent.appendChild(tableClone);
      
      const printWindow = window.open('', '_blank');
      
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${title}</title>
              <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
              </style>
            </head>
            <body>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        setTimeout(() => {
          printWindow.print();
          toast.success("Yazdırma işlemi başlatıldı");
        }, 500);
      } else {
        toast.error("Yazdırma penceresi açılamadı. Lütfen popup engelleyiciyi kontrol edin.");
      }
    } catch (error) {
      console.error("Yazdırma sırasında hata:", error);
      toast.error("Yazdırma sırasında bir hata oluştu");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        {searchable && (
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Genel arama..."
                value={globalFilter || ""}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-8 w-full md:w-64"
              />
            </div>
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="mr-2 h-4 w-4" />
                Görünürlük
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          
          {printable && (
            <>
              <Button variant="outline" size="sm" onClick={printTable} className="h-9">
                <Printer className="mr-2 h-4 w-4" />
                Yazdır
              </Button>
              <Button variant="outline" size="sm" onClick={generatePDF} className="h-9">
                <Download className="mr-2 h-4 w-4" />
                PDF
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : (
                        <div className="flex items-center space-x-2">
                          <div
                            className={
                              header.column.getCanSort()
                                ? "flex items-center cursor-pointer select-none"
                                : ""
                            }
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <ArrowUpDown className="ml-2 h-4 w-4" />
                            )}
                          </div>
                          {header.column.getCanFilter() && (
                            <div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Filter className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="start">
                                  <div className="p-2">
                                    <Input
                                      placeholder={`Filtrele...`}
                                      value={
                                        (header.column.getFilterValue() as string) ?? ""
                                      }
                                      onChange={(e) =>
                                        header.column.setFilterValue(e.target.value)
                                      }
                                      className="max-w-sm"
                                    />
                                  </div>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          )}
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Kayıt bulunamadı.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Önceki
        </Button>
        <div className="text-sm text-muted-foreground">
          Sayfa {table.getState().pagination.pageIndex + 1} / {table.getPageCount()}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Sonraki
        </Button>
      </div>
    </div>
  );
}

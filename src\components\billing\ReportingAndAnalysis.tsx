
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  Bar<PERSON>hart, 
  Bar, 
  LineChart, 
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { DownloadCloud, Filter, PlusCircle } from "lucide-react";
import { useReportsStore } from "@/stores/reportsStore";
import { SummaryReports } from "./SummaryReports"; 

// Demo veriler
const salesComparison = [
  { month: "Ocak", thisYear: 125000, lastYear: 110000 },
  { month: "Şubat", thisYear: 145000, lastYear: 120000 },
  { month: "Mart", thisYear: 162000, lastYear: 135000 },
  { month: "<PERSON><PERSON>", thisYear: 150000, lastYear: 140000 },
  { month: "Mayıs", thisYear: 180000, lastYear: 155000 },
  { month: "Haziran", thisYear: 190000, lastYear: 165000 }
];

const profitabilityData = [
  { product: "Ürün A", revenue: 120000, cost: 72000, profit: 48000, margin: 40 },
  { product: "Ürün B", revenue: 85000, cost: 59500, profit: 25500, margin: 30 },
  { product: "Hizmet X", revenue: 150000, cost: 60000, profit: 90000, margin: 60 },
  { product: "Hizmet Y", revenue: 95000, cost: 47500, profit: 47500, margin: 50 },
  { product: "Ürün C", revenue: 65000, cost: 45500, profit: 19500, margin: 30 }
];

const customerProfitabilityData = [
  { customer: "ABC Şirketi", revenue: 220000, cost: 132000, profit: 88000, margin: 40 },
  { customer: "XYZ Ltd", revenue: 185000, cost: 129500, profit: 55500, margin: 30 },
  { customer: "123 Holding", revenue: 150000, cost: 75000, profit: 75000, margin: 50 },
  { customer: "Tech Çözümler", revenue: 120000, cost: 78000, profit: 42000, margin: 35 }
];

const taxData = [
  { period: "Ocak 2024", taxType: "KDV", amount: 28500, status: "Ödendi", dueDate: "2024-02-26" },
  { period: "Şubat 2024", taxType: "KDV", amount: 32400, status: "Ödendi", dueDate: "2024-03-26" },
  { period: "Mart 2024", taxType: "KDV", amount: 35800, status: "Ödendi", dueDate: "2024-04-26" },
  { period: "Nisan 2024", taxType: "KDV", amount: 33200, status: "Ödendi", dueDate: "2024-05-26" },
  { period: "Mayıs 2024", taxType: "KDV", amount: 39600, status: "Ödendi", dueDate: "2024-06-26" },
  { period: "Haziran 2024", taxType: "KDV", amount: 41800, status: "Bekliyor", dueDate: "2024-07-26" }
];

const cashFlowAnalysis = [
  { month: "Ocak", operatingCF: 85000, investingCF: -15000, financingCF: -5000, netCF: 65000 },
  { month: "Şubat", operatingCF: 92000, investingCF: -18000, financingCF: -5000, netCF: 69000 },
  { month: "Mart", operatingCF: 105000, investingCF: -22000, financingCF: -5000, netCF: 78000 },
  { month: "Nisan", operatingCF: 98000, investingCF: -12000, financingCF: -5000, netCF: 81000 },
  { month: "Mayıs", operatingCF: 115000, investingCF: -25000, financingCF: -5000, netCF: 85000 },
  { month: "Haziran", operatingCF: 120000, investingCF: -15000, financingCF: -5000, netCF: 100000 }
];

// Tablo sütunları
const profitabilityColumns = [
  {
    header: "Ürün/Hizmet",
    accessorKey: "product"
  },
  {
    header: "Gelir",
    accessorKey: "revenue",
    cell: ({ row }) => <span>₺{row.original.revenue.toLocaleString()}</span>
  },
  {
    header: "Maliyet",
    accessorKey: "cost",
    cell: ({ row }) => <span>₺{row.original.cost.toLocaleString()}</span>
  },
  {
    header: "Kâr",
    accessorKey: "profit",
    cell: ({ row }) => <span className="text-green-600 font-medium">₺{row.original.profit.toLocaleString()}</span>
  },
  {
    header: "Kâr Marjı",
    accessorKey: "margin",
    cell: ({ row }) => <span>%{row.original.margin}</span>
  }
];

const customerProfitabilityColumns = [
  {
    header: "Müşteri",
    accessorKey: "customer"
  },
  {
    header: "Gelir",
    accessorKey: "revenue",
    cell: ({ row }) => <span>₺{row.original.revenue.toLocaleString()}</span>
  },
  {
    header: "Maliyet",
    accessorKey: "cost",
    cell: ({ row }) => <span>₺{row.original.cost.toLocaleString()}</span>
  },
  {
    header: "Kâr",
    accessorKey: "profit",
    cell: ({ row }) => <span className="text-green-600 font-medium">₺{row.original.profit.toLocaleString()}</span>
  },
  {
    header: "Kâr Marjı",
    accessorKey: "margin",
    cell: ({ row }) => <span>%{row.original.margin}</span>
  }
];

const taxColumns = [
  {
    header: "Dönem",
    accessorKey: "period"
  },
  {
    header: "Vergi Türü",
    accessorKey: "taxType"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => <span>₺{row.original.amount.toLocaleString()}</span>
  },
  {
    header: "Son Ödeme Tarihi",
    accessorKey: "dueDate"
  },
  {
    header: "Durum",
    accessorKey: "status"
  }
];

const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"];

export const ReportingAndAnalysis = () => {
  const [reportTab, setReportTab] = useState("summary");
  const { generateDemoData, savedReports } = useReportsStore();

  // Demo verileri yükle
  if (savedReports.length === 0) {
    generateDemoData();
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Raporlama ve Analiz</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filtrele
          </Button>
          <Button variant="outline" size="sm">
            <DownloadCloud className="h-4 w-4 mr-2" />
            Dışa Aktar
          </Button>
          <Button size="sm">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Rapor
          </Button>
        </div>
      </div>

      <Tabs value={reportTab} onValueChange={setReportTab} className="space-y-4">
        <TabsList className="grid grid-cols-1 md:grid-cols-5 lg:grid-cols-5">
          <TabsTrigger value="summary">Özet Raporlar</TabsTrigger>
          <TabsTrigger value="sales">Satış Raporları</TabsTrigger>
          <TabsTrigger value="profitability">Kârlılık Analizi</TabsTrigger>
          <TabsTrigger value="tax">KDV ve Vergi</TabsTrigger>
          <TabsTrigger value="cashflow">Nakit Akış Analizi</TabsTrigger>
        </TabsList>

        {/* Özet Raporlar */}
        <TabsContent value="summary" className="space-y-4">
          <SummaryReports />
        </TabsContent>

        {/* Satış Raporları */}
        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Dönemsel Satış Karşılaştırması</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={salesComparison}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `₺${value / 1000}K`} />
                    <Tooltip 
                      formatter={(value) => [`₺${value.toLocaleString()}`, undefined]}
                      labelFormatter={(label) => `${label} Ayı`}
                    />
                    <Legend />
                    <Bar dataKey="thisYear" name="2024 Yılı" fill="#4f46e5" />
                    <Bar dataKey="lastYear" name="2023 Yılı" fill="#94a3b8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Toplam Satış (2024)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">
                  ₺{salesComparison.reduce((sum, item) => sum + item.thisYear, 0).toLocaleString()}
                </div>
                <p className="text-green-600 text-sm flex items-center">
                  +%{Math.round((salesComparison.reduce((sum, item) => sum + item.thisYear, 0) / 
                                salesComparison.reduce((sum, item) => sum + item.lastYear, 0) - 1) * 100)} 
                  geçen yıla göre
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Ortalama Aylık Satış (2024)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">
                  ₺{Math.round(salesComparison.reduce((sum, item) => sum + item.thisYear, 0) / 
                             salesComparison.length).toLocaleString()}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Kârlılık Analizi */}
        <TabsContent value="profitability" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Ürün/Hizmet Kârlılığı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={profitabilityData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" tickFormatter={(value) => `₺${value / 1000}K`} />
                      <YAxis dataKey="product" type="category" width={80} />
                      <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, undefined]} />
                      <Legend />
                      <Bar dataKey="cost" name="Maliyet" stackId="a" fill="#94a3b8" />
                      <Bar dataKey="profit" name="Kâr" stackId="a" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Müşteri Kârlılığı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={customerProfitabilityData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" tickFormatter={(value) => `₺${value / 1000}K`} />
                      <YAxis dataKey="customer" type="category" width={90} />
                      <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, undefined]} />
                      <Legend />
                      <Bar dataKey="cost" name="Maliyet" stackId="a" fill="#94a3b8" />
                      <Bar dataKey="profit" name="Kâr" stackId="a" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Ürün/Hizmet Kârlılık Detayları</CardTitle>
            </CardHeader>
            <CardContent>
              <DataTable columns={profitabilityColumns} data={profitabilityData} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Müşteri Kârlılık Detayları</CardTitle>
            </CardHeader>
            <CardContent>
              <DataTable columns={customerProfitabilityColumns} data={customerProfitabilityData} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* KDV ve Vergi */}
        <TabsContent value="tax" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>KDV Tutarları (6 Aylık)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={taxData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "KDV"]} />
                      <Legend />
                      <Line type="monotone" dataKey="amount" name="KDV Tutarı" stroke="#4f46e5" activeDot={{ r: 8 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>KDV Özeti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-muted-foreground">Toplam KDV Tutarı (6 Ay)</p>
                  <p className="text-2xl font-bold text-blue-600">₺{taxData.reduce((sum, item) => sum + item.amount, 0).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Aylık Ortalama KDV</p>
                  <p className="text-2xl font-bold">₺{Math.round(taxData.reduce((sum, item) => sum + item.amount, 0) / taxData.length).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Son Dönem KDV</p>
                  <p className="text-2xl font-bold text-amber-600">₺{taxData[taxData.length - 1].amount.toLocaleString()}</p>
                  <p className="text-xs text-muted-foreground">Son ödeme: {taxData[taxData.length - 1].dueDate}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>KDV Dönemleri ve Tutarları</CardTitle>
            </CardHeader>
            <CardContent>
              <DataTable columns={taxColumns} data={taxData} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Nakit Akış Analizi */}
        <TabsContent value="cashflow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detaylı Nakit Akış Analizi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={cashFlowAnalysis}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `₺${value / 1000}K`} />
                    <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, undefined]} />
                    <Legend />
                    <Bar dataKey="operatingCF" name="Operasyonel Nakit Akışı" fill="#10b981" />
                    <Bar dataKey="investingCF" name="Yatırım Nakit Akışı" fill="#ef4444" />
                    <Bar dataKey="financingCF" name="Finansman Nakit Akışı" fill="#f59e0b" />
                    <Bar dataKey="netCF" name="Net Nakit Akışı" fill="#4f46e5" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Operasyonel Nakit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ₺{cashFlowAnalysis.reduce((sum, item) => sum + item.operatingCF, 0).toLocaleString()}
                </div>
                <p className="text-muted-foreground text-sm">6 aylık toplam</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Yatırım Nakit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  ₺{cashFlowAnalysis.reduce((sum, item) => sum + item.investingCF, 0).toLocaleString()}
                </div>
                <p className="text-muted-foreground text-sm">6 aylık toplam</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Finansman Nakit</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  ₺{cashFlowAnalysis.reduce((sum, item) => sum + item.financingCF, 0).toLocaleString()}
                </div>
                <p className="text-muted-foreground text-sm">6 aylık toplam</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Net Nakit Akışı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  ₺{cashFlowAnalysis.reduce((sum, item) => sum + item.netCF, 0).toLocaleString()}
                </div>
                <p className="text-muted-foreground text-sm">6 aylık toplam</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

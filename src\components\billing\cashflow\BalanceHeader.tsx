
import React from "react";
import { HelpCircle } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatCurrency } from "../utils/cashFlowUtils";
import { type CheckedState } from "@radix-ui/react-checkbox";

interface BalanceHeaderProps {
  initialBalance: number;
  showCurrentMonth: boolean;
  onCheckedChange: (checked: CheckedState) => void;
}

export const BalanceHeader: React.FC<BalanceHeaderProps> = ({ 
  initialBalance, 
  showCurrentMonth, 
  onCheckedChange 
}) => {
  return (
    <div className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 flex justify-between items-center border-b">
      <div className="flex items-center gap-2">
        <span className="font-medium"><PERSON><PERSON><PERSON>mden Devir Edilen Bakiyeniz: </span>
        <span className="font-bold">{formatCurrency(initialBalance)}</span>
      </div>
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">İlgili Ay Nakit Akış Tutarı</span>
          <Checkbox checked={showCurrentMonth} onCheckedChange={onCheckedChange} />
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <HelpCircle className="h-5 w-5 text-gray-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="w-80">
                Bu tablo, yıl boyunca gelir ve giderlerinizin aylık dağılımını gösterir.
                İlgili ay seçeneğini işaretleyerek seçtiğiniz ayın nakit akış tutarlarını görebilirsiniz.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

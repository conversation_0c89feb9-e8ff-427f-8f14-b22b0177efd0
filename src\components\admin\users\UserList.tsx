
import { User, Company } from "../types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Settings, ShieldCheck } from "lucide-react";

interface UserListProps {
  users: User[];
  companies: Company[];
}

export const UserList = ({ users, companies }: UserListProps) => {
  return (
    <div className="border rounded-md divide-y">
      <div className="grid grid-cols-5 gap-4 p-3 bg-muted/50 font-medium text-sm">
        <div className="col-span-2">Kullanıcı</div>
        <div>Rol</div>
        <div>Yetkiler</div>
        <div>İşlemler</div>
      </div>
      
      {users.map(user => (
        <div key={user.id} className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
          <div className="col-span-2 flex items-center gap-3">
            <Avatar>
              <AvatarImage src="" />
              <AvatarFallback className="bg-blue-100 text-blue-600">{user.avatar}</AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">{user.name}</p>
              <p className="text-xs text-muted-foreground">{user.email}</p>
              {user.companyId && <p className="text-xs text-blue-600">{companies.find(c => c.id === user.companyId)?.name}</p>}
            </div>
          </div>
          <div>
            <Badge variant={user.role === "Admin" ? "default" : "outline"} className={user.role === "Admin" ? "bg-blue-500" : ""}>
              {user.role === "Admin" && <ShieldCheck className="h-3 w-3 mr-1" />}
              {user.role}
            </Badge>
          </div>
          <div>
            {user.permissions.includes("all") ? (
              <Badge className="bg-purple-500">Tam Yetki</Badge>
            ) : (
              <div className="flex flex-col gap-1">
                <Progress value={user.permissions.length * 10} className="h-2" />
                <span className="text-xs">{user.permissions.length} modül</span>
              </div>
            )}
          </div>
          <div>
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};


import { useNavigate, useLocation } from "react-router-dom";
import { LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

interface NavMenuItemProps {
  icon: LucideIcon;
  title: string;
  href: string;
  iconColor?: string;
  isAdmin?: boolean;
  hasSubmenu?: boolean;
  renderSubmenu?: () => React.ReactNode;
}

export const NavMenuItem = ({ 
  icon: Icon, 
  title, 
  href, 
  iconColor = "", 
  isAdmin = false,
  hasSubmenu = false,
  renderSubmenu
}: NavMenuItemProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isActive = location.pathname === href;

  if (hasSubmenu && renderSubmenu) {
    return renderSubmenu();
  }

  return (
    <Button
      variant={isActive ? "secondary" : "ghost"}
      className={`w-full justify-start gap-2 ${isActive ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : ''} ${isAdmin ? 'mt-4' : ''}`}
      onClick={() => navigate(href)}
    >
      <Icon className={`h-4 w-4 ${iconColor}`} />
      {title}
      {isAdmin && (
        <span className="ml-auto">
          <span className="flex h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400"></span>
        </span>
      )}
    </Button>
  );
};

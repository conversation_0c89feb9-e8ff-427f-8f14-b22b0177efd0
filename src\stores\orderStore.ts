
import { create } from 'zustand';

export type OrderStatus = 
  | 'pending' 
  | 'processing' 
  | 'shipped' 
  | 'delivered' 
  | 'cancelled' 
  | 'returned';

export type ShippingMethod = 
  | 'standard' 
  | 'express' 
  | 'same_day';

export type ShippingCarrier = 
  | 'aras' 
  | 'yurtici' 
  | 'ups' 
  | 'mng' 
  | 'ptt';

export type ReturnReason = 
  | 'damaged' 
  | 'wrong_item' 
  | 'not_needed' 
  | 'quality_issue' 
  | 'other';

export type Order = {
  id: string;
  number: string;
  customerId: string;
  customerName: string;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  createdAt: Date;
  updatedAt: Date;
  shipping?: ShippingInfo;
  notes?: string;
  returnRequest?: ReturnRequest;
};

export type OrderItem = {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
};

export type ShippingInfo = {
  id: string;
  orderId: string;
  carrier: ShippingCarrier;
  trackingNumber: string;
  method: ShippingMethod;
  status: 'pending' | 'in_transit' | 'delivered' | 'failed';
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  address: {
    fullName: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
  };
  cost: number;
  updatedAt: Date;
};

export type ReturnRequest = {
  id: string;
  orderId: string;
  reason: ReturnReason;
  description: string;
  items: {
    orderItemId: string;
    quantity: number;
  }[];
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  refundAmount?: number;
};

type OrderState = {
  orders: Order[];
  isLoading: boolean;
  error: string | null;
  
  // Order operations
  createOrder: (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateOrder: (id: string, updates: Partial<Order>) => void;
  deleteOrder: (id: string) => void;
  updateOrderStatus: (id: string, status: OrderStatus) => void;
  
  // Shipping operations
  addShippingInfo: (orderId: string, shippingData: Omit<ShippingInfo, 'id' | 'orderId' | 'updatedAt'>) => void;
  updateShippingStatus: (orderId: string, status: ShippingInfo['status'], trackingDetails?: Partial<ShippingInfo>) => void;
  
  // Return operations
  createReturnRequest: (orderId: string, returnData: Omit<ReturnRequest, 'id' | 'orderId' | 'createdAt' | 'updatedAt'>) => void;
  updateReturnStatus: (orderId: string, status: ReturnRequest['status'], refundAmount?: number) => void;
  
  // Demo data
  loadDemoData: () => void;
};

export const useOrderStore = create<OrderState>((set) => ({
  orders: [],
  isLoading: false,
  error: null,

  createOrder: (orderData) => {
    const order: Order = {
      id: crypto.randomUUID(),
      ...orderData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    set((state) => ({
      orders: [...state.orders, order],
    }));
  },

  updateOrder: (id, updates) => {
    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === id
          ? { ...order, ...updates, updatedAt: new Date() }
          : order
      ),
    }));
  },

  deleteOrder: (id) => {
    set((state) => ({
      orders: state.orders.filter((order) => order.id !== id),
    }));
  },

  updateOrderStatus: (id, status) => {
    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === id
          ? { ...order, status, updatedAt: new Date() }
          : order
      ),
    }));
  },

  addShippingInfo: (orderId, shippingData) => {
    const shippingInfo: ShippingInfo = {
      id: crypto.randomUUID(),
      orderId,
      ...shippingData,
      updatedAt: new Date(),
    };

    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === orderId
          ? { ...order, shipping: shippingInfo, updatedAt: new Date() }
          : order
      ),
    }));
  },

  updateShippingStatus: (orderId, status, trackingDetails) => {
    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === orderId && order.shipping
          ? {
              ...order,
              shipping: {
                ...order.shipping,
                status,
                ...trackingDetails,
                updatedAt: new Date(),
              },
              updatedAt: new Date(),
            }
          : order
      ),
    }));
  },

  createReturnRequest: (orderId, returnData) => {
    const returnRequest: ReturnRequest = {
      id: crypto.randomUUID(),
      orderId,
      ...returnData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === orderId
          ? { ...order, returnRequest, updatedAt: new Date() }
          : order
      ),
    }));
  },

  updateReturnStatus: (orderId, status, refundAmount) => {
    set((state) => ({
      orders: state.orders.map((order) =>
        order.id === orderId && order.returnRequest
          ? {
              ...order,
              returnRequest: {
                ...order.returnRequest,
                status,
                ...(refundAmount !== undefined ? { refundAmount } : {}),
                updatedAt: new Date(),
              },
              updatedAt: new Date(),
            }
          : order
      ),
    }));
  },

  loadDemoData: () => {
    const demoOrders: Order[] = [
      {
        id: '1',
        number: 'ORD-2024-001',
        customerId: '1',
        customerName: 'Ahmet Yılmaz',
        items: [
          {
            id: '1',
            productId: '1',
            productName: 'iPhone 15 Pro',
            quantity: 1,
            unitPrice: 64999,
            totalPrice: 64999,
          },
          {
            id: '2',
            productId: '2',
            productName: 'Apple AirPods Pro',
            quantity: 1,
            unitPrice: 7499,
            totalPrice: 7499,
          },
        ],
        totalAmount: 72498,
        status: 'delivered',
        paymentStatus: 'paid',
        createdAt: new Date(2024, 1, 15),
        updatedAt: new Date(2024, 1, 15),
        shipping: {
          id: '1',
          orderId: '1',
          carrier: 'aras',
          trackingNumber: 'AR928374657TR',
          method: 'standard',
          status: 'delivered',
          estimatedDelivery: new Date(2024, 1, 18),
          actualDelivery: new Date(2024, 1, 17),
          address: {
            fullName: 'Ahmet Yılmaz',
            street: 'Atatürk Cad. No:123',
            city: 'İstanbul',
            state: 'Kadıköy',
            zipCode: '34700',
            country: 'Türkiye',
            phone: '+90 ************',
          },
          cost: 0,
          updatedAt: new Date(2024, 1, 17),
        },
      },
      {
        id: '2',
        number: 'ORD-2024-002',
        customerId: '2',
        customerName: 'Ayşe Demir',
        items: [
          {
            id: '3',
            productId: '3',
            productName: 'Samsung Galaxy S24',
            quantity: 1,
            unitPrice: 49999,
            totalPrice: 49999,
          },
        ],
        totalAmount: 49999,
        status: 'shipped',
        paymentStatus: 'paid',
        createdAt: new Date(2024, 2, 10),
        updatedAt: new Date(2024, 2, 10),
        shipping: {
          id: '2',
          orderId: '2',
          carrier: 'yurtici',
          trackingNumber: 'YK76543219TR',
          method: 'express',
          status: 'in_transit',
          estimatedDelivery: new Date(2024, 2, 13),
          address: {
            fullName: 'Ayşe Demir',
            street: 'Bağdat Cad. No:456',
            city: 'İstanbul',
            state: 'Maltepe',
            zipCode: '34840',
            country: 'Türkiye',
            phone: '+90 ************',
          },
          cost: 25,
          updatedAt: new Date(2024, 2, 11),
        },
      },
      {
        id: '3',
        number: 'ORD-2024-003',
        customerId: '3',
        customerName: 'Mehmet Kaya',
        items: [
          {
            id: '4',
            productId: '4',
            productName: 'MacBook Air M3',
            quantity: 1,
            unitPrice: 89999,
            totalPrice: 89999,
          },
        ],
        totalAmount: 89999,
        status: 'cancelled',
        paymentStatus: 'refunded',
        createdAt: new Date(2024, 2, 5),
        updatedAt: new Date(2024, 2, 6),
        notes: 'Müşteri iptal talebi üzerine sipariş iptal edilmiştir.',
      },
      {
        id: '4',
        number: 'ORD-2024-004',
        customerId: '1',
        customerName: 'Ahmet Yılmaz',
        items: [
          {
            id: '5',
            productId: '5',
            productName: 'iPad Air',
            quantity: 1,
            unitPrice: 39999,
            totalPrice: 39999,
          },
        ],
        totalAmount: 39999,
        status: 'returned',
        paymentStatus: 'refunded',
        createdAt: new Date(2024, 1, 20),
        updatedAt: new Date(2024, 1, 25),
        shipping: {
          id: '3',
          orderId: '4',
          carrier: 'ups',
          trackingNumber: 'UP12345678TR',
          method: 'standard',
          status: 'delivered',
          estimatedDelivery: new Date(2024, 1, 23),
          actualDelivery: new Date(2024, 1, 22),
          address: {
            fullName: 'Ahmet Yılmaz',
            street: 'Atatürk Cad. No:123',
            city: 'İstanbul',
            state: 'Kadıköy',
            zipCode: '34700',
            country: 'Türkiye',
            phone: '+90 ************',
          },
          cost: 0,
          updatedAt: new Date(2024, 1, 22),
        },
        returnRequest: {
          id: '1',
          orderId: '4',
          reason: 'damaged',
          description: 'Ürün hasar görmüş şekilde geldi, ekran kırık.',
          items: [
            {
              orderItemId: '5',
              quantity: 1,
            },
          ],
          status: 'completed',
          createdAt: new Date(2024, 1, 24),
          updatedAt: new Date(2024, 1, 25),
          refundAmount: 39999,
        },
      },
      {
        id: '5',
        number: 'ORD-2024-005',
        customerId: '4',
        customerName: 'Zeynep Şahin',
        items: [
          {
            id: '6',
            productId: '6',
            productName: 'Sony WH-1000XM5',
            quantity: 1,
            unitPrice: 17999,
            totalPrice: 17999,
          },
        ],
        totalAmount: 17999,
        status: 'pending',
        paymentStatus: 'pending',
        createdAt: new Date(2024, 2, 18),
        updatedAt: new Date(2024, 2, 18),
      },
    ];

    set({ orders: demoOrders });
  },
}));


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useBillingStore } from "@/stores/billingStore";

// Demo data
const salesData = [
  { name: "<PERSON><PERSON>si", sales: 4000 },
  { name: "<PERSON><PERSON>", sales: 3000 },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", sales: 5000 },
  { name: "<PERSON><PERSON>embe", sales: 2780 },
  { name: "<PERSON><PERSON>", sales: 1890 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", sales: 2390 },
  { name: "<PERSON><PERSON>", sales: 3490 }
];

const weeklySalesData = [
  { name: "Hafta 1", sales: 24000 },
  { name: "Hafta 2", sales: 21000 },
  { name: "<PERSON>fta 3", sales: 32000 },
  { name: "<PERSON>ft<PERSON> 4", sales: 28000 }
];

const monthlySalesData = [
  { name: "<PERSON><PERSON><PERSON>", sales: 95000 },
  { name: "<PERSON><PERSON><PERSON>", sales: 85000 },
  { name: "Mart", sales: 110000 },
  { name: "Nisan", sales: 103000 },
  { name: "Mayıs", sales: 99000 },
  { name: "Haziran", sales: 120000 }
];

const topCustomersData = [
  { name: "ABC Şirketi", value: 240000 },
  { name: "XYZ Ltd", value: 180000 },
  { name: "123 Holding", value: 120000 },
  { name: "Tech Çözümler", value: 75000 },
  { name: "Diğer", value: 185000 }
];

const topProductsData = [
  { name: "Ürün A", value: 180000 },
  { name: "Ürün B", value: 150000 },
  { name: "Hizmet X", value: 120000 },
  { name: "Hizmet Y", value: 90000 },
  { name: "Diğer", value: 260000 }
];

const salesPersonData = [
  { name: "Ahmet", sales: 250000 },
  { name: "Mehmet", sales: 210000 },
  { name: "Ayşe", sales: 290000 },
  { name: "Fatma", sales: 200000 },
  { name: "Ali", sales: 180000 }
];

const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6"];

export const SalesAndInvoicing = () => {
  const { invoices } = useBillingStore();
  const [timeFrame, setTimeFrame] = useState("daily");
  
  const pendingInvoices = invoices.filter(inv => inv.status === 'pending');
  const paidInvoices = invoices.filter(inv => inv.status === 'paid');
  
  const pendingAmount = pendingInvoices.reduce((sum, inv) => sum + inv.total, 0);
  const paidAmount = paidInvoices.reduce((sum, inv) => sum + inv.total, 0);
  const totalAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Satış ve Faturalama</h2>
      </div>

      {/* Fatura Özeti */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Faturalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invoices.length} Fatura</div>
            <div className="text-lg font-medium text-green-600">₺{totalAmount.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Bekleyen Faturalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingInvoices.length} Fatura</div>
            <div className="text-lg font-medium text-amber-600">₺{pendingAmount.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Tahsil Edilen Faturalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paidInvoices.length} Fatura</div>
            <div className="text-lg font-medium text-blue-600">₺{paidAmount.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Satış Takibi */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Satış Takibi</CardTitle>
            <Tabs
              defaultValue="daily"
              value={timeFrame}
              onValueChange={setTimeFrame}
            >
              <TabsList>
                <TabsTrigger value="daily">Günlük</TabsTrigger>
                <TabsTrigger value="weekly">Haftalık</TabsTrigger>
                <TabsTrigger value="monthly">Aylık</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={
                  timeFrame === "daily" 
                    ? salesData 
                    : timeFrame === "weekly" 
                      ? weeklySalesData 
                      : monthlySalesData
                }
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Satış"]} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="#4f46e5"
                  name="Satış Tutarı"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Müşteri Bazlı Satış Analizi */}
        <Card>
          <CardHeader>
            <CardTitle>Müşteri Bazlı Satış Analizi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={topCustomersData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                  >
                    {topCustomersData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Satış Tutarı"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Ürün/Hizmet Bazlı Satış Analizi */}
        <Card>
          <CardHeader>
            <CardTitle>Ürün/Hizmet Bazlı Satış Analizi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={topProductsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                  >
                    {topProductsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Satış Tutarı"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Satış Personeli Performansı */}
      <Card>
        <CardHeader>
          <CardTitle>Satış Personeli Performansı</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salesPersonData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Satış Tutarı"]} />
                <Legend />
                <Bar dataKey="sales" name="Satış Tutarı" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import { User, Company } from "../types";
import { UserListHeader } from "../users/UserListHeader";
import { UserList } from "../users/UserList";
import { AddUserDialog } from "../users/AddUserDialog";
import { useUsers } from "../users/useUsers";

interface UsersTabProps {
  users: User[];
  setUsers: (users: User[]) => void;
  companies: Company[];
}

export const UsersTab = ({ users, setUsers, companies }: UsersTabProps) => {
  const {
    showAddUserDialog,
    setShowAddUserDialog,
    newUser,
    setNewUser,
    handleAddUser
  } = useUsers(users, setUsers);

  return (
    <>
      <UserListHeader onAddUser={() => setShowAddUserDialog(true)} />
      <UserList users={users} companies={companies} />
      <AddUserDialog
        open={showAddUserDialog}
        onOpenChange={setShowAddUserDialog}
        newUser={newUser}
        onNewUserChange={setNewUser}
        onAddUser={handleAddUser}
        companies={companies}
      />
    </>
  );
};


import { Route, Routes } from "react-router-dom";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

export const OptionalModuleRoutes = () => {
  return (
    <Routes>
      <Route path="/satis" element={
        <ProtectedRoute requiredModule="satis">
          <div>Satış Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/proje" element={
        <ProtectedRoute requiredModule="proje">
          <div>Proje <PERSON></div>
        </ProtectedRoute>
      } />
      <Route path="/tedarik" element={
        <ProtectedRoute requiredModule="tedarik">
          <div>Ted<PERSON>k <PERSON>ü</div>
        </ProtectedRoute>
      } />
      <Route path="/insaat" element={
        <ProtectedRoute requiredModule="insaat">
          <div>İnşaat Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/profesyonel" element={
        <ProtectedRoute requiredModule="profesyonel">
          <div>Profesyonel Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/servis" element={
        <ProtectedRoute requiredModule="servis">
          <div>Servis Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/saglik" element={
        <ProtectedRoute requiredModule="saglik">
          <div>Sağlık Modülü</div>
        </ProtectedRoute>
      } />
      <Route path="/konaklama" element={
        <ProtectedRoute requiredModule="konaklama">
          <div>Konaklama Modülü</div>
        </ProtectedRoute>
      } />
    </Routes>
  );
};

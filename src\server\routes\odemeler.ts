
import express from 'express';
import { query, pool } from '../../utils/db';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger';

const router = express.Router();

// Tüm ödemeleri getir
router.get('/', async (req, res) => {
  const transactionId = req.headers['x-transaction-id'] as string || uuidv4();
  
  try {
    logger.info(`Ödeme listesi alınıyor. [TransactionID: ${transactionId}]`);
    
    const odemeler = await query(`
      SELECT o.*, c.unvan as cari_unvan, f.numara as fatura_numara
      FROM odemeler o
      JOIN cari_hesaplar c ON o.cari_id = c.id
      LEFT JOIN faturalar f ON o.fatura_id = f.id
      ORDER BY o.olusturma_tarihi DESC
    `) as any[];
    
    logger.info(`${odemeler.length} adet ödeme alındı. [TransactionID: ${transactionId}]`);
    res.json(odemeler);
  } catch (error) {
    logger.error(`Ödemeleri getirme hatası: ${error}. [TransactionID: ${transactionId}]`);
    res.status(500).json({ error: 'Ödemeler getirilemedi', details: error.message });
  }
});

// ID'ye göre ödeme getir
router.get('/:id', async (req, res) => {
  const transactionId = req.headers['x-transaction-id'] as string || uuidv4();
  
  try {
    const { id } = req.params;
    logger.info(`${id} ID'li ödeme detayı alınıyor. [TransactionID: ${transactionId}]`);
    
    const odeme = await query(`
      SELECT o.*, c.unvan as cari_unvan, f.numara as fatura_numara
      FROM odemeler o
      JOIN cari_hesaplar c ON o.cari_id = c.id
      LEFT JOIN faturalar f ON o.fatura_id = f.id
      WHERE o.id = ?
    `, [id]) as any[];
    
    if (odeme.length === 0) {
      logger.warn(`${id} ID'li ödeme bulunamadı. [TransactionID: ${transactionId}]`);
      return res.status(404).json({ error: 'Ödeme bulunamadı' });
    }
    
    logger.info(`${id} ID'li ödeme detayı alındı. [TransactionID: ${transactionId}]`);
    res.json(odeme[0]);
  } catch (error) {
    logger.error(`Ödeme getirme hatası: ${error}. [TransactionID: ${transactionId}]`);
    res.status(500).json({ error: 'Ödeme getirilemedi', details: error.message });
  }
});

// Yeni ödeme ekle
router.post('/', async (req, res) => {
  const transactionId = req.headers['x-transaction-id'] as string || uuidv4();
  
  // Aynı işlemin tekrar yapılmasını engellemek için referans no kontrolü
  const { cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no } = req.body;
  
  // Veritabanı connection'ı al
  const connection = await pool.getConnection();
  
  try {
    logger.info(`Yeni ödeme kaydı ekleniyor. [TransactionID: ${transactionId}]`);
    
    // Zorunlu alanları kontrol et
    if (!cari_id || !tutar || !odeme_tarihi || !odeme_yontemi) {
      logger.warn(`Eksik alan bilgisi. [TransactionID: ${transactionId}]`);
      return res.status(400).json({ 
        error: 'Cari ID, tutar, ödeme tarihi ve ödeme yöntemi alanları zorunludur',
        transactionId 
      });
    }
    
    // Transaction başlat
    await connection.beginTransaction();
    
    // Eğer referans_no varsa, aynı referans numarasına sahip başka ödeme var mı kontrol et
    // Bu, aynı ödemenin yanlışlıkla iki kez yapılmasını önler
    if (referans_no) {
      const [existingPayments] = await connection.execute(
        'SELECT id FROM odemeler WHERE referans_no = ?', 
        [referans_no]
      );
      
      if (Array.isArray(existingPayments) && existingPayments.length > 0) {
        logger.warn(`${referans_no} referans numaralı ödeme zaten mevcut. [TransactionID: ${transactionId}]`);
        await connection.rollback();
        return res.status(409).json({ 
          error: 'Bu referans numarasına sahip bir ödeme zaten mevcut', 
          transactionId
        });
      }
    }
    
    // ID oluştur
    const id = uuidv4();
    
    // Ödemeyi kaydet
    await connection.execute(
      `INSERT INTO odemeler 
      (id, cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no, transaction_id) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no, transactionId]
    );
    
    // Transaction'ı tamamla
    await connection.commit();
    
    logger.info(`Yeni ödeme kaydı oluşturuldu. ID: ${id} [TransactionID: ${transactionId}]`);
    res.status(201).json({ 
      message: 'Ödeme başarıyla oluşturuldu', 
      id,
      transactionId
    });
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await connection.rollback();
    
    logger.error(`Ödeme oluşturma hatası: ${error}. [TransactionID: ${transactionId}]`);
    res.status(500).json({ 
      error: 'Ödeme oluşturulamadı', 
      details: error.message,
      transactionId
    });
  } finally {
    // Her durumda connection'ı havuza geri ver
    connection.release();
  }
});

// Ödeme güncelle
router.put('/:id', async (req, res) => {
  const transactionId = req.headers['x-transaction-id'] as string || uuidv4();
  const { id } = req.params;
  const { cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no } = req.body;
  
  const connection = await pool.getConnection();
  
  try {
    logger.info(`${id} ID'li ödeme güncelleniyor. [TransactionID: ${transactionId}]`);
    
    // Zorunlu alanları kontrol et
    if (!cari_id || !tutar || !odeme_tarihi || !odeme_yontemi) {
      logger.warn(`Eksik alan bilgisi. [TransactionID: ${transactionId}]`);
      return res.status(400).json({ 
        error: 'Cari ID, tutar, ödeme tarihi ve ödeme yöntemi alanları zorunludur',
        transactionId 
      });
    }
    
    // Transaction başlat
    await connection.beginTransaction();
    
    // İlgili kaydın var olup olmadığını kontrol et
    const [existingPayment] = await connection.execute(
      'SELECT id FROM odemeler WHERE id = ? FOR UPDATE', 
      [id]
    );
    
    if (!Array.isArray(existingPayment) || existingPayment.length === 0) {
      logger.warn(`${id} ID'li ödeme bulunamadı. [TransactionID: ${transactionId}]`);
      await connection.rollback();
      return res.status(404).json({ 
        error: 'Güncellenecek ödeme kaydı bulunamadı',
        transactionId
      });
    }
    
    // Referans no kontrolü (aynı referans no başka bir ödemede var mı?)
    if (referans_no) {
      const [existingRef] = await connection.execute(
        'SELECT id FROM odemeler WHERE referans_no = ? AND id != ?', 
        [referans_no, id]
      );
      
      if (Array.isArray(existingRef) && existingRef.length > 0) {
        logger.warn(`${referans_no} referans numaralı başka bir ödeme zaten mevcut. [TransactionID: ${transactionId}]`);
        await connection.rollback();
        return res.status(409).json({ 
          error: 'Bu referans numarası başka bir ödemede kullanılmaktadır', 
          transactionId
        });
      }
    }
    
    // Ödemeyi güncelle
    await connection.execute(
      `UPDATE odemeler 
      SET cari_id = ?, fatura_id = ?, tutar = ?, odeme_tarihi = ?, 
          odeme_yontemi = ?, aciklama = ?, referans_no = ?, transaction_id = ?, 
          guncelleme_tarihi = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no, transactionId, id]
    );
    
    // Transaction'ı tamamla
    await connection.commit();
    
    logger.info(`${id} ID'li ödeme güncellendi. [TransactionID: ${transactionId}]`);
    res.json({ 
      message: 'Ödeme başarıyla güncellendi',
      transactionId
    });
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await connection.rollback();
    
    logger.error(`Ödeme güncelleme hatası: ${error}. [TransactionID: ${transactionId}]`);
    res.status(500).json({ 
      error: 'Ödeme güncellenemedi', 
      details: error.message,
      transactionId
    });
  } finally {
    // Her durumda connection'ı havuza geri ver
    connection.release();
  }
});

// Ödeme sil
router.delete('/:id', async (req, res) => {
  const transactionId = req.headers['x-transaction-id'] as string || uuidv4();
  const { id } = req.params;
  
  const connection = await pool.getConnection();
  
  try {
    logger.info(`${id} ID'li ödeme siliniyor. [TransactionID: ${transactionId}]`);
    
    // Transaction başlat
    await connection.beginTransaction();
    
    // İlgili kaydın var olup olmadığını kontrol et
    const [existingPayment] = await connection.execute(
      'SELECT id FROM odemeler WHERE id = ? FOR UPDATE', 
      [id]
    );
    
    if (!Array.isArray(existingPayment) || existingPayment.length === 0) {
      logger.warn(`${id} ID'li ödeme bulunamadı. [TransactionID: ${transactionId}]`);
      await connection.rollback();
      return res.status(404).json({ 
        error: 'Silinecek ödeme kaydı bulunamadı',
        transactionId
      });
    }
    
    // Ödemeyi sil
    await connection.execute(
      'DELETE FROM odemeler WHERE id = ?', 
      [id]
    );
    
    // Transaction'ı tamamla
    await connection.commit();
    
    logger.info(`${id} ID'li ödeme silindi. [TransactionID: ${transactionId}]`);
    res.json({ 
      message: 'Ödeme başarıyla silindi',
      transactionId
    });
  } catch (error) {
    // Hata durumunda transaction'ı geri al
    await connection.rollback();
    
    logger.error(`Ödeme silme hatası: ${error}. [TransactionID: ${transactionId}]`);
    res.status(500).json({ 
      error: 'Ödeme silinemedi', 
      details: error.message,
      transactionId
    });
  } finally {
    // Her durumda connection'ı havuza geri ver
    connection.release();
  }
});

export default router;

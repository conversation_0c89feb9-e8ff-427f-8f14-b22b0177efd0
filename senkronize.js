const mysql = require('mysql2/promise'); // Promise tabanlı mysql2 kullanıyoruz

// Veritabanı bağlantı bilgileri
const sourceConfig = {
  host: 'localhost',
  user: 'mehmet',
  password: 'Mb_07112024',
  database: 'borudata'
};

const targetConfig = {
  host: '***********',
  port: 1481,
  user: 'mehmet',
  password: 'Bormeg.mS_07112024s.tech',
  database: 'borudata'
};

async function syncStokKodu() {
  let sourceConn = null;
  let targetConn = null;

  try {
    console.log('Senkronizasyon başlıyor...', new Date().toISOString());

    // Kaynak veritabanına bağlan
    sourceConn = await mysql.createConnection(sourceConfig);
    
    // Kaynak veritabanından NULL olmayan en son stok kodunu al
    const [sourceResults] = await sourceConn.query(`
      SELECT id, yeni_stok_kodu 
      FROM silodata 
      WHERE yeni_stok_kodu IS NOT NULL 
      ORDER BY id DESC 
      LIMIT 1
    `);

    if (!sourceResults.length) {
      console.log('Güncellenecek kayıt yok');
      return;
    }

    // Hedef veritabanına bağlan
    targetConn = await mysql.createConnection(targetConfig);
    
    // Hedef veritabanındaki en son kaydı güncelle
    const [updateResult] = await targetConn.query(
      'UPDATE silodata SET yeni_stok_kodu = ? WHERE id = (SELECT id FROM (SELECT id FROM silodata ORDER BY id DESC LIMIT 1) AS t)',
      [sourceResults[0].yeni_stok_kodu]
    );

    if (updateResult.affectedRows > 0) {
      console.log('Senkronizasyon başarılı:', {
        kaynak_id: sourceResults[0].id,
        stok_kodu: sourceResults[0].yeni_stok_kodu,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('Hedef veritabanında güncellenecek kayıt bulunamadı');
    }

  } catch (error) {
    console.error('Senkronizasyon hatası:', {
      message: error.message,
      code: error.code,
      timestamp: new Date().toISOString()
    });
  } finally {
    // Bağlantıları kapat
    if (sourceConn) {
      try {
        await sourceConn.end();
      } catch (err) {
        console.error('Kaynak bağlantı kapatma hatası:', err);
      }
    }
    if (targetConn) {
      try {
        await targetConn.end();
      } catch (err) {
        console.error('Hedef bağlantı kapatma hatası:', err);
      }
    }
  }
}

// Senkronizasyonu başlat
console.log('Senkronizasyon servisi başlatılıyor...');
setInterval(syncStokKodu, 30000); // 30 saniyede bir çalıştır

// Hata yönetimi
process.on('SIGTERM', () => {
  console.log('Servis durduruluyor...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Servis durduruluyor...');
  process.exit(0);
}); 
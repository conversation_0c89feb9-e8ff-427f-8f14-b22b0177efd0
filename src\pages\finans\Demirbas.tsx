
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { FixedAssetsList } from "@/components/billing/FixedAssetsList";
import { DepreciationCalculation } from "@/components/billing/DepreciationCalculation";

const Demirbas = () => {
  const tabs: TabItem[] = [
    { id: "fixedAssets", label: "Demirbaş Listesi", component: <FixedAssetsList /> },
    { id: "depreciation", label: "Amortisman Hesaplama", component: <DepreciationCalculation /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Demirbaş Yönetimi</h2>
          <p className="text-muted-foreground">
            Demirbaş takibi ve amortisman hesaplamaları
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Demirbas;

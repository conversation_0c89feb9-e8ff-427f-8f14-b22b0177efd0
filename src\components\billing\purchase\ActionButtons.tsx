
import { Button } from "@/components/ui/button";
import { FileText, PlusCircle, ClipboardList } from "lucide-react";

type ActionButtonsProps = {
  onNewInvoice: () => void;
  onBulkPaymentOrder: () => void;
  onPaymentInstruction: () => void;
};

export const ActionButtons = ({ 
  onNewInvoice, 
  onBulkPaymentOrder,
  onPaymentInstruction
}: ActionButtonsProps) => {
  return (
    <div className="flex space-x-2">
      <Button onClick={onNewInvoice}>
        <PlusCircle className="mr-2 h-4 w-4" />
        Yeni Alış Faturası
      </Button>
      <Button variant="outline" onClick={onBulkPaymentOrder}>
        <FileText className="mr-2 h-4 w-4" />
        Ödeme Emri Oluştur
      </Button>
      <Button variant="outline" onClick={onPaymentInstruction}>
        <ClipboardList className="mr-2 h-4 w-4" />
        Talimat Oluştur
      </Button>
    </div>
  );
};

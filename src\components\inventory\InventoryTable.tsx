
import { useState } from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  ArrowUpDown, 
  Plus, 
  Minus 
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { useInventoryStore, Product } from "@/stores/inventoryStore";
import { Badge } from "@/components/ui/badge";
import { useEffect } from "react";

const InventoryTable = () => {
  const { products, categories, suppliers, addStockMovement, loadDemoData } = useInventoryStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Product>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Yük örnekteki verileri
  useEffect(() => {
    if (products.length === 0) {
      loadDemoData();
    }
  }, [products.length, loadDemoData]);

  // Arama ve sıralama fonksiyonları
  const filteredProducts = products.filter(
    (product) =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
    }

    if (aValue instanceof Date && bValue instanceof Date) {
      return sortDirection === "asc"
        ? aValue.getTime() - bValue.getTime()
        : bValue.getTime() - aValue.getTime();
    }

    return 0;
  });

  // Sıralama işlevi
  const handleSort = (field: keyof Product) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Stok hareketi ekleme işlevi
  const handleStockMovement = (productId: string, type: 'in' | 'out') => {
    const quantity = Number(prompt(`Eklenecek/çıkarılacak ${type === 'in' ? 'giriş' : 'çıkış'} miktarı:`, "1"));
    
    if (quantity && !isNaN(quantity) && quantity > 0) {
      const reason = prompt(`Stok ${type === 'in' ? 'giriş' : 'çıkış'} nedeni:`, type === 'in' ? 'Satın alma' : 'Satış');
      
      if (reason) {
        addStockMovement({
          productId,
          type,
          quantity,
          reason,
          notes: prompt("Notlar (opsiyonel):", "") || undefined
        });
      }
    }
  };

  // Kategori ve tedarikçi isimlerini getir
  const getCategoryName = (categoryId: string) => {
    return categories.find(cat => cat.id === categoryId)?.name || 'Bilinmiyor';
  };

  const getSupplierName = (supplierId: string) => {
    return suppliers.find(sup => sup.id === supplierId)?.name || 'Bilinmiyor';
  };

  return (
    <Card className="overflow-hidden">
      <div className="p-4 flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Ürün ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button size="sm" className="flex items-center gap-1">
          <Plus className="h-4 w-4" />
          Yeni Ürün
        </Button>
      </div>

      <div className="border-t">
        <div className="relative w-full overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="w-[180px] cursor-pointer"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center gap-1">
                    Ürün Adı
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("sku")}
                >
                  <div className="flex items-center gap-1">
                    SKU
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="hidden md:table-cell">Kategori</TableHead>
                <TableHead className="hidden lg:table-cell">Tedarikçi</TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => handleSort("price")}
                >
                  <div className="flex items-center gap-1 justify-end">
                    Satış Fiyatı
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => handleSort("currentStock")}
                >
                  <div className="flex items-center gap-1 justify-end">
                    Stok
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="w-[100px]">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    Ürün bulunamadı
                  </TableCell>
                </TableRow>
              ) : (
                sortedProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.sku}</TableCell>
                    <TableCell className="hidden md:table-cell">{getCategoryName(product.categoryId)}</TableCell>
                    <TableCell className="hidden lg:table-cell">{getSupplierName(product.supplierId)}</TableCell>
                    <TableCell className="text-right">₺{product.price.toLocaleString('tr-TR')}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span>{product.currentStock} {product.unit}</span>
                        {product.currentStock <= product.minStockLevel && (
                          <Badge variant="destructive" className="ml-1 text-xs">
                            Düşük Stok
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleStockMovement(product.id, 'in')}
                          title="Stok Ekle"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleStockMovement(product.id, 'out')}
                          title="Stok Çıkar"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem className="flex gap-2 items-center">
                              <Edit className="h-4 w-4" />
                              Düzenle
                            </DropdownMenuItem>
                            <DropdownMenuItem className="flex gap-2 items-center text-destructive">
                              <Trash2 className="h-4 w-4" />
                              Sil
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </Card>
  );
};

export default InventoryTable;


import MainLayout from "@/components/layout/MainLayout";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useNavigate } from "react-router-dom";
import { 
  FileText, Receipt, Wallet, BarChart3, Users, 
  Building, Package, CreditCard, DollarSign, 
  Calculator, ClipboardList, BookOpen 
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";

const Finans = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Define the finance menu structure with categories and their sub-items
  const financeMenuStructure = [
    {
      id: "cari",
      title: "Cari Hesap Yönetimi",
      icon: Users,
      color: "text-blue-500",
      subItems: [
        { id: "cari-hesaplar", title: "Car<PERSON> Hesaplar", path: "/finans/cari" },
        { id: "mü<PERSON>teriler", title: "Müşteri Yönetimi", path: "/finans/cari?tab=customers" },
        { id: "tedarikçiler", title: "Tedarikçi Yönetimi", path: "/finans/cari?tab=suppliers" },
      ]
    },
    {
      id: "faturalar",
      title: "Fatura Yönetimi",
      icon: FileText,
      color: "text-purple-500",
      subItems: [
        { id: "satis-faturalar", title: "Satış Faturaları", path: "/finans/faturalar?type=sales" },
        { id: "alis-faturalar", title: "Alış Faturaları", path: "/finans/faturalar?type=purchase" },
        { id: "e-fatura", title: "E-Fatura/E-Arşiv", path: "/finans/e-devlet" },
      ]
    },
    {
      id: "nakit",
      title: "Nakit Yönetimi",
      icon: Wallet,
      color: "text-green-500",
      subItems: [
        { id: "kasa-islemler", title: "Kasa İşlemleri", path: "/finans/nakit" },
        { id: "gelir-gider", title: "Gelir/Gider", path: "/finans/nakit?tab=income-expense" },
      ]
    },
    {
      id: "banka",
      title: "Banka İşlemleri",
      icon: Building,
      color: "text-cyan-500",
      subItems: [
        { id: "hesaplar", title: "Banka Hesapları", path: "/finans/banka-kasa" },
        { id: "havale-eft", title: "Havale/EFT", path: "/finans/banka-kasa?tab=transfers" },
      ]
    },
    {
      id: "alacak-borc",
      title: "Alacak ve Borç Takibi",
      icon: DollarSign,
      color: "text-pink-500",
      subItems: [
        { id: "alacaklar", title: "Alacak Takibi", path: "/finans/alacak-borc?tab=receivables" },
        { id: "borclar", title: "Borç Takibi", path: "/finans/alacak-borc?tab=payables" },
        { id: "vade-analizi", title: "Vade Analizi", path: "/finans/alacak-borc?tab=maturity" },
      ]
    },
    {
      id: "cekSenet",
      title: "Çek ve Senet Takibi",
      icon: Receipt,
      color: "text-amber-500",
      subItems: [
        { id: "cekler", title: "Çek Yönetimi", path: "/finans/cek-senet?tab=cheques" },
        { id: "senetler", title: "Senet Yönetimi", path: "/finans/cek-senet?tab=promissory" },
      ]
    },
    {
      id: "stok",
      title: "Stok Yönetimi",
      icon: Package,
      color: "text-orange-500",
      subItems: [
        { id: "urunler", title: "Ürünler", path: "/finans/stok?tab=products" },
        { id: "stok-hareketleri", title: "Stok Hareketleri", path: "/finans/stok?tab=movements" },
        { id: "envanter", title: "Envanter Yönetimi", path: "/finans/envanter" },
      ]
    },
    {
      id: "muhasebe",
      title: "Muhasebe İşlemleri",
      icon: Calculator,
      color: "text-red-500",
      subItems: [
        { id: "genel-muhasebe", title: "Genel Muhasebe", path: "/finans/muhasebe" },
        { id: "defter-beyan", title: "Defter ve Beyannameler", path: "/finans/muhasebe?tab=declarations" },
        { id: "demirbas", title: "Demirbaş Yönetimi", path: "/finans/demirbas" },
      ]
    },
    {
      id: "raporlar",
      title: "Raporlama",
      icon: BarChart3,
      color: "text-indigo-500",
      subItems: [
        { id: "finansal-raporlar", title: "Finansal Raporlar", path: "/finans/raporlar" },
        { id: "kar-zarar", title: "Kâr/Zarar Analizi", path: "/finans/raporlar?tab=profit-loss" },
        { id: "bilanço", title: "Bilanço", path: "/finans/raporlar?tab=balance-sheet" },
      ]
    },
  ];

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-1/4">
          <div className="space-y-2">
            <h2 className="text-2xl font-bold tracking-tight mb-4">Finans Yönetimi</h2>
            <Accordion type="multiple" className="w-full">
              {financeMenuStructure.map((category) => (
                <AccordionItem key={category.id} value={category.id} className="border">
                  <AccordionTrigger className="px-2 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md">
                    <div className="flex items-center gap-3">
                      <div className={`p-1.5 rounded-full ${category.color === 'text-blue-500' ? 'bg-blue-100' : category.color === 'text-purple-500' ? 'bg-purple-100' : category.color === 'text-green-500' ? 'bg-green-100' : category.color === 'text-cyan-500' ? 'bg-cyan-100' : category.color === 'text-amber-500' ? 'bg-amber-100' : category.color === 'text-orange-500' ? 'bg-orange-100' : category.color === 'text-indigo-500' ? 'bg-indigo-100' : category.color === 'text-pink-500' ? 'bg-pink-100' : 'bg-red-100'}`}>
                        <category.icon className={`h-5 w-5 ${category.color}`} />
                      </div>
                      <span className="text-base font-medium">{category.title}</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="pl-11">
                    <div className="flex flex-col gap-1 py-1">
                      {category.subItems.map((subItem) => (
                        <Button 
                          key={subItem.id}
                          variant="ghost" 
                          className="w-full justify-start text-sm px-2 py-1.5 h-auto"
                          onClick={() => navigate(subItem.path)}
                        >
                          {subItem.title}
                        </Button>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>

        <div className="w-full md:w-3/4">
          <Card className="border">
            <CardHeader className="pb-2">
              <CardTitle>Finans Yönetimi</CardTitle>
              <CardDescription>
                Finansal işlemlerinizi yönetin ve takip edin
              </CardDescription>
            </CardHeader>
            <CardContent className="prose dark:prose-invert max-w-none">
              <p>
                Finans modülümüz ile tüm finansal işlemlerinizi tek bir yerden yönetebilirsiniz. 
                Sol menüden ilgili kategorilere tıklayarak detaylı işlemlere erişebilirsiniz.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <h3 className="text-lg font-semibold">Özellikler</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Cari hesap ve müşteri yönetimi</li>
                    <li>Satış ve alış faturaları</li>
                    <li>Nakit ve banka işlemleri</li>
                    <li>Çek ve senet takibi</li>
                    <li>Stok ve envanter yönetimi</li>
                    <li>Profesyonel raporlama araçları</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Hızlı Linkler</h3>
                  <div className="flex flex-col gap-2">
                    <Button 
                      variant="outline" 
                      className="justify-start"
                      onClick={() => navigate("/finans/faturalar?type=new")}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Yeni Fatura Oluştur
                    </Button>
                    <Button 
                      variant="outline" 
                      className="justify-start"
                      onClick={() => navigate("/finans/cari?action=new")}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Yeni Cari Hesap Ekle
                    </Button>
                    <Button 
                      variant="outline" 
                      className="justify-start"
                      onClick={() => navigate("/finans/raporlar")}
                    >
                      <ClipboardList className="h-4 w-4 mr-2" />
                      Finansal Raporlar
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Finans;

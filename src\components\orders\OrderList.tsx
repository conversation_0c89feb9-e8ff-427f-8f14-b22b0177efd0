
import { useState } from "react";
import { useOrderStore, Order, OrderStatus } from "@/stores/orderStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  MoreHorizontal,
  Truck,
  PackageCheck,
  FileText,
  RefreshCcw,
  X,
  ArrowUpDown,
  Clock,
  Package2,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { ShippingModal } from "./ShippingModal";
import { ReturnModal } from "./ReturnModal";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export const OrderList = () => {
  const { orders } = useOrderStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<keyof Order>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [isShippingModalOpen, setIsShippingModalOpen] = useState(false);
  const [isReturnModalOpen, setIsReturnModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  // Filter orders based on search term and active tab
  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by tab
    if (activeTab === "all") {
      return matchesSearch;
    }
    
    if (activeTab === "pending") {
      return matchesSearch && (order.status === "pending" || order.status === "processing");
    }
    
    if (activeTab === "shipped") {
      return matchesSearch && order.status === "shipped";
    }
    
    if (activeTab === "delivered") {
      return matchesSearch && order.status === "delivered";
    }
    
    if (activeTab === "problems") {
      return matchesSearch && (order.status === "cancelled" || order.status === "returned" || !!order.returnRequest);
    }

    return matchesSearch;
  });

  // Sort orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue instanceof Date && bValue instanceof Date) {
      return sortDirection === "asc"
        ? aValue.getTime() - bValue.getTime()
        : bValue.getTime() - aValue.getTime();
    }

    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  // Handler for sorting
  const handleSort = (field: keyof Order) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Open shipping modal
  const handleShippingClick = (orderId: string) => {
    setSelectedOrderId(orderId);
    setIsShippingModalOpen(true);
  };

  // Open return modal
  const handleReturnClick = (orderId: string) => {
    setSelectedOrderId(orderId);
    setIsReturnModalOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">Beklemede</Badge>;
      case "processing":
        return <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">Hazırlanıyor</Badge>;
      case "shipped":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">Kargoda</Badge>;
      case "delivered":
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Teslim Edildi</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">İptal</Badge>;
      case "returned":
        return <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">İade</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };

  // Get shipping status icon
  const getShippingStatusIcon = (order: Order) => {
    if (!order.shipping) {
      return null;
    }

    switch (order.shipping.status) {
      case "pending":
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Clock className="h-4 w-4 text-blue-500" />
            </TooltipTrigger>
            <TooltipContent>Kargo Beklemede</TooltipContent>
          </Tooltip>
        );
      case "in_transit":
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <Truck className="h-4 w-4 text-yellow-500" />
            </TooltipTrigger>
            <TooltipContent>Taşınıyor</TooltipContent>
          </Tooltip>
        );
      case "delivered":
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <PackageCheck className="h-4 w-4 text-green-500" />
            </TooltipTrigger>
            <TooltipContent>Teslim Edildi</TooltipContent>
          </Tooltip>
        );
      case "failed":
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <X className="h-4 w-4 text-red-500" />
            </TooltipTrigger>
            <TooltipContent>Teslimat Başarısız</TooltipContent>
          </Tooltip>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="p-4 space-y-4">
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Sipariş veya müşteri ara..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all" className="flex items-center gap-1">
              <Package2 className="h-4 w-4" />
              <span>Tümü</span>
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>Bekleyen</span>
            </TabsTrigger>
            <TabsTrigger value="shipped" className="flex items-center gap-1">
              <Truck className="h-4 w-4" />
              <span>Kargoda</span>
            </TabsTrigger>
            <TabsTrigger value="delivered" className="flex items-center gap-1">
              <PackageCheck className="h-4 w-4" />
              <span>Teslim Edilen</span>
            </TabsTrigger>
            <TabsTrigger value="problems" className="flex items-center gap-1">
              <RefreshCcw className="h-4 w-4" />
              <span>İptal/İade</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab}>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead 
                      className="w-[120px] cursor-pointer"
                      onClick={() => handleSort("number")}
                    >
                      <div className="flex items-center gap-1">
                        Sipariş No
                        <ArrowUpDown className="h-3 w-3" />
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort("customerName")}
                    >
                      <div className="flex items-center gap-1">
                        Müşteri
                        <ArrowUpDown className="h-3 w-3" />
                      </div>
                    </TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead className="hidden md:table-cell">Kargo</TableHead>
                    <TableHead 
                      className="text-right cursor-pointer"
                      onClick={() => handleSort("totalAmount")}
                    >
                      <div className="flex items-center gap-1 justify-end">
                        Tutar
                        <ArrowUpDown className="h-3 w-3" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="text-right cursor-pointer hidden lg:table-cell"
                      onClick={() => handleSort("createdAt")}
                    >
                      <div className="flex items-center gap-1 justify-end">
                        Tarih
                        <ArrowUpDown className="h-3 w-3" />
                      </div>
                    </TableHead>
                    <TableHead className="w-[80px]">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedOrders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        Sipariş bulunamadı
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.number}</TableCell>
                        <TableCell>{order.customerName}</TableCell>
                        <TableCell>{getStatusBadge(order.status)}</TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="flex items-center gap-2">
                            {getShippingStatusIcon(order)}
                            {order.shipping?.trackingNumber && (
                              <span className="text-xs text-muted-foreground truncate max-w-[100px]">
                                {order.shipping.trackingNumber}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">₺{order.totalAmount.toLocaleString('tr-TR')}</TableCell>
                        <TableCell className="text-right text-muted-foreground text-xs hidden lg:table-cell">
                          {format(order.createdAt, 'dd MMM yyyy', { locale: tr })}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              title="Kargo Detayları"
                              onClick={() => handleShippingClick(order.id)}
                              disabled={order.status === 'cancelled'}
                            >
                              <Truck className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem className="flex gap-2 items-center">
                                  <FileText className="h-4 w-4" />
                                  Detaylar
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  className="flex gap-2 items-center"
                                  onClick={() => handleReturnClick(order.id)}
                                  disabled={order.status === 'cancelled' || order.status === 'returned'}
                                >
                                  <RefreshCcw className="h-4 w-4" />
                                  İptal/İade İşlemi
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Shipping Modal */}
      {isShippingModalOpen && selectedOrderId && (
        <ShippingModal
          orderId={selectedOrderId}
          isOpen={isShippingModalOpen}
          onClose={() => {
            setIsShippingModalOpen(false);
            setSelectedOrderId(null);
          }}
        />
      )}

      {/* Return Modal */}
      {isReturnModalOpen && selectedOrderId && (
        <ReturnModal
          orderId={selectedOrderId}
          isOpen={isReturnModalOpen}
          onClose={() => {
            setIsReturnModalOpen(false);
            setSelectedOrderId(null);
          }}
        />
      )}
    </Card>
  );
};


import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText, ExternalLink, CheckCircle2, AlertCircle, Clock, ArrowUpCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export const EGovernmentIntegration = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <FileText className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">E-Beyannameler</p>
              <h3 className="text-2xl font-semibold">12</h3>
              <p className="text-sm text-muted-foreground">Bu Ay</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <CheckCircle2 className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Onaylanan</p>
              <h3 className="text-2xl font-semibold">8</h3>
              <p className="text-sm text-success">Tamamlandı</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Clock className="h-8 w-8 text-amber-500" />
            <div>
              <p className="text-sm text-muted-foreground">Bekleyen</p>
              <h3 className="text-2xl font-semibold">3</h3>
              <p className="text-sm text-amber-500">İşlem Bekliyor</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <AlertCircle className="h-8 w-8 text-red-500" />
            <div>
              <p className="text-sm text-muted-foreground">Hatalı</p>
              <h3 className="text-2xl font-semibold">1</h3>
              <p className="text-sm text-destructive">Düzeltme Gerekli</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>E-Beyanname İşlemleri</CardTitle>
            <CardDescription>Vergi beyannamelerini hazırlayın ve gönderin</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Yeni Beyanname
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <ArrowUpCircle className="h-4 w-4" />
                  Bekleyen Gönderimler
                </Button>
              </div>
              
              <div className="space-y-3 mt-4">
                <div className="border rounded-md p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">KDV Beyannamesi</h4>
                      <p className="text-xs text-muted-foreground">Mart 2024</p>
                    </div>
                    <Badge className="bg-amber-500">Bekliyor</Badge>
                  </div>
                  <div className="text-sm">
                    <p>Dönem: 01/03/2024 - 31/03/2024</p>
                    <p>Son Gönderim: 26/04/2024</p>
                  </div>
                  <div className="flex items-center justify-end gap-2 mt-3">
                    <Button variant="outline" size="sm">Önizle</Button>
                    <Button size="sm">Gönder</Button>
                  </div>
                </div>
                
                <div className="border rounded-md p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Geçici Vergi Beyannamesi</h4>
                      <p className="text-xs text-muted-foreground">1. Dönem 2024</p>
                    </div>
                    <Badge className="bg-green-500">Gönderildi</Badge>
                  </div>
                  <div className="text-sm">
                    <p>Dönem: 01/01/2024 - 31/03/2024</p>
                    <p>Gönderim Tarihi: 15/05/2024</p>
                  </div>
                  <div className="flex items-center justify-end gap-2 mt-3">
                    <Button variant="outline" size="sm">Görüntüle</Button>
                    <Button variant="outline" size="sm">İndir</Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>SGK İşlemleri</CardTitle>
            <CardDescription>Sosyal güvenlik bildirimlerini yönetin</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Yeni Bildirim
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  SGK Portalına Git
                </Button>
              </div>
              
              <div className="space-y-3 mt-4">
                <div className="border rounded-md p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">Aylık Prim Hizmet Bildirimi</h4>
                      <p className="text-xs text-muted-foreground">Mart 2024</p>
                    </div>
                    <Badge className="bg-green-500">Gönderildi</Badge>
                  </div>
                  <div className="text-sm">
                    <p>Dönem: Mart 2024</p>
                    <p>Gönderim Tarihi: 23/04/2024</p>
                  </div>
                  <div className="flex items-center justify-end gap-2 mt-3">
                    <Button variant="outline" size="sm">Görüntüle</Button>
                    <Button variant="outline" size="sm">İndir</Button>
                  </div>
                </div>
                
                <div className="border rounded-md p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium">İşten Ayrılış Bildirimi</h4>
                      <p className="text-xs text-muted-foreground">Çalışan: Ahmet Yılmaz</p>
                    </div>
                    <Badge className="bg-amber-500">Hazırlanıyor</Badge>
                  </div>
                  <div className="text-sm">
                    <p>Çıkış Tarihi: 15/04/2024</p>
                    <p>Son Gönderim: 25/04/2024</p>
                  </div>
                  <div className="flex items-center justify-end gap-2 mt-3">
                    <Button variant="outline" size="sm">Düzenle</Button>
                    <Button size="sm">Tamamla</Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};


// Define maintenance-related type definitions

declare interface MaintenanceType {
  REPAIR: 'repair';
  INSPECTION: 'inspection';
  OIL_CHANGE: 'oil_change';
  TIRE_CHANGE: 'tire_change';
  OTHER: 'other';
}

declare interface MaintenanceStatus {
  SCHEDULED: 'scheduled';
  IN_PROGRESS: 'in_progress';
  COMPLETED: 'completed';
  CANCELLED: 'cancelled';
}

declare interface MaintenancePriority {
  LOW: 'low';
  MEDIUM: 'medium';
  HIGH: 'high';
  URGENT: 'urgent';
}

declare interface MaintenanceItem {
  id: string;
  title: string;
  description: string;
  vehicleId: string;
  status: MaintenanceStatus;
  priority: MaintenancePriority;
  type: MaintenanceType;
  cost?: number;
  technician?: string;
  notes?: string;
  dueDate: string | Date; // This property is needed for the calendar
  completedAt?: string;
  updatedAt: string;
  createdAt: string;
}

declare interface MaintenanceSchedule {
  id: string;
  name: string;
  description: string;
  vehicleId: string;
  interval: number; // in days
  intervalUnit: 'days' | 'weeks' | 'months' | 'years';
  lastPerformed?: string;
  nextDue?: string;
  isActive: boolean;
  scheduledDate: string | Date; // This property is needed for the calendar
  isCompleted: boolean; // This property is needed for the calendar
  createdAt: string;
  updatedAt: string;
}

declare interface DayContentProps {
  date: Date;
  day: number; // This property is needed for the calendar
}

// Additional interfaces for MaintenanceCalendar component
declare interface CalendarMaintenanceItem {
  type: "item";
  date: Date;
  isCompleted: boolean;
}

declare interface CalendarMaintenanceSchedule {
  type: "schedule";
  date: Date;
  isCompleted: boolean;
}

declare type CalendarItem = 
  | (MaintenanceItem & CalendarMaintenanceItem)
  | (MaintenanceSchedule & CalendarMaintenanceSchedule);

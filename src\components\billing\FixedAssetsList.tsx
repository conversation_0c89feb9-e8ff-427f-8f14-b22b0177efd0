
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle, FileDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Demo data
const fixedAssetsData = [
  { id: "1", code: "D001", name: "<PERSON><PERSON> Bilgisayarları", category: "IT Equipment", acquisitionDate: "2023-03-15", acquisitionCost: 85000.00, currentValue: 68000.00, depreciationRate: 20, location: "Merkez Ofis", status: "active", description: "İşletme bilgisayarları" },
  { id: "2", code: "D002", name: "Ofis Mobilyaları", category: "Furniture", acquisitionDate: "2023-04-10", acquisitionCost: 35000.00, currentValue: 31500.00, depreciationRate: 10, location: "<PERSON><PERSON><PERSON>", status: "active", description: "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ve dolaplar" },
  { id: "3", code: "D003", name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Lisansları", category: "Software", acquisitionDate: "2023-05-20", acquisitionCost: 45000.00, currentValue: 36000.00, depreciationRate: 20, location: "Merkez Ofis", status: "active", description: "ERP ve ofis yazılımları" },
  { id: "4", code: "D004", name: "Şirket Araçları", category: "Vehicles", acquisitionDate: "2022-08-15", acquisitionCost: 750000.00, currentValue: 600000.00, depreciationRate: 20, location: "Garaj", status: "active", description: "Şirket araç filosu" },
  { id: "5", code: "D005", name: "Depo Rafları", category: "Storage", acquisitionDate: "2022-10-25", acquisitionCost: 28000.00, currentValue: 23800.00, depreciationRate: 15, location: "Depo", status: "active", description: "Depo raf sistemleri" },
  { id: "6", code: "D006", name: "Klima Sistemleri", category: "HVAC", acquisitionDate: "2022-11-30", acquisitionCost: 42000.00, currentValue: 35700.00, depreciationRate: 15, location: "Merkez Ofis", status: "active", description: "Ofis klima sistemleri" },
  { id: "7", code: "D007", name: "Eski Sunucular", category: "IT Equipment", acquisitionDate: "2019-06-15", acquisitionCost: 120000.00, currentValue: 0.00, depreciationRate: 20, location: "Arşiv", status: "disposed", description: "Kullanımdan kaldırılmış sunucular" }
];

// Column definitions
const fixedAssetsColumns = [
  {
    header: "Demirbaş Kodu",
    accessorKey: "code"
  },
  {
    header: "Demirbaş Adı",
    accessorKey: "name"
  },
  {
    header: "Kategori",
    accessorKey: "category"
  },
  {
    header: "Alım Tarihi",
    accessorKey: "acquisitionDate"
  },
  {
    header: "Alım Bedeli",
    accessorKey: "acquisitionCost",
    cell: ({ row }) => {
      return <span>₺ {row.original.acquisitionCost.toLocaleString()}</span>;
    }
  },
  {
    header: "Güncel Değer",
    accessorKey: "currentValue",
    cell: ({ row }) => {
      return <span className="font-medium">₺ {row.original.currentValue.toLocaleString()}</span>;
    }
  },
  {
    header: "Amortisman Oranı",
    accessorKey: "depreciationRate",
    cell: ({ row }) => {
      return <span>%{row.original.depreciationRate}</span>;
    }
  },
  {
    header: "Konum",
    accessorKey: "location"
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant: "default" | "destructive" | "success" = "default";
      let statusLabel = "";
      
      if (status === "active") {
        badgeVariant = "success";
        statusLabel = "Aktif";
      } else if (status === "disposed") {
        badgeVariant = "destructive";
        statusLabel = "Çıkış Yapıldı";
      }
      
      return <Badge variant={badgeVariant}>{statusLabel}</Badge>;
    }
  }
];

export const FixedAssetsList = () => {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  
  // Filter data based on selected status
  const filteredData = statusFilter 
    ? fixedAssetsData.filter(asset => asset.status === statusFilter)
    : fixedAssetsData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={statusFilter === null ? "default" : "outline"}
            onClick={() => setStatusFilter(null)}
          >
            Tüm Demirbaşlar
          </Button>
          <Button 
            variant={statusFilter === "active" ? "default" : "outline"}
            onClick={() => setStatusFilter("active")}
          >
            Aktif Demirbaşlar
          </Button>
          <Button 
            variant={statusFilter === "disposed" ? "default" : "outline"}
            onClick={() => setStatusFilter("disposed")}
          >
            Çıkışı Yapılan Demirbaşlar
          </Button>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="outline">
            <FileDown className="mr-2 h-4 w-4" />
            Rapor Al
          </Button>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Yeni Demirbaş Ekle
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Demirbaş Listesi</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={fixedAssetsColumns} data={filteredData} />
        </CardContent>
      </Card>
    </div>
  );
};

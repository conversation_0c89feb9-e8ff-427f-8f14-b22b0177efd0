
import { Button } from "@/components/ui/button";
import { Building } from "lucide-react";

interface CompanyListHeaderProps {
  onAddCompany: () => void;
}

export const CompanyListHeader = ({ onAddCompany }: CompanyListHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <h3 className="text-lg font-medium">Firma Listesi</h3>
      <Button className="flex items-center gap-2" onClick={onAddCompany}>
        <Building className="h-4 w-4" />
        Firma Ekle
      </Button>
    </div>
  );
};

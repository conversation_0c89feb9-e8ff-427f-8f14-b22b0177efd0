
import { Route, Routes } from "react-router-dom";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import Finans from "@/pages/Finans";
import Cari from "@/pages/finans/Cari";
import AlacakBorc from "@/pages/finans/AlacakBorc";
import Envanter from "@/pages/finans/Envanter";
import Nakit from "@/pages/finans/Nakit";
import FinansRaporlar from "@/pages/finans/FinansRaporlar";
import EDevlet from "@/pages/finans/EDevlet";
import BankaKasa from "@/pages/finans/BankaKasa";
import Faturalar from "@/pages/finans/Faturalar";
import Muhasebe from "@/pages/finans/Muhasebe";
import CekSenet from "@/pages/finans/CekSenet";
import Demirbas from "@/pages/finans/Demirbas";
import Stok from "@/pages/Stok";
import { FinancialManagementTabs } from "@/components/billing/FinancialManagementTabs";

export const FinanceRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={
        <ProtectedRoute requiredModule="finans">
          <Finans />
        </ProtectedRoute>
      } />
      <Route path="/cari" element={
        <ProtectedRoute requiredModule="finans">
          <Cari />
        </ProtectedRoute>
      } />
      <Route path="/alacak-borc" element={
        <ProtectedRoute requiredModule="finans">
          <AlacakBorc />
        </ProtectedRoute>
      } />
      <Route path="/envanter" element={
        <ProtectedRoute requiredModule="finans">
          <Envanter />
        </ProtectedRoute>
      } />
      <Route path="/nakit" element={
        <ProtectedRoute requiredModule="finans">
          <Nakit />
        </ProtectedRoute>
      } />
      <Route path="/raporlar" element={
        <ProtectedRoute requiredModule="finans">
          <FinansRaporlar />
        </ProtectedRoute>
      } />
      <Route path="/e-devlet" element={
        <ProtectedRoute requiredModule="finans">
          <EDevlet />
        </ProtectedRoute>
      } />
      <Route path="/banka-kasa" element={
        <ProtectedRoute requiredModule="finans">
          <BankaKasa />
        </ProtectedRoute>
      } />
      <Route path="/faturalar" element={
        <ProtectedRoute requiredModule="finans">
          <Faturalar />
        </ProtectedRoute>
      } />
      <Route path="/muhasebe" element={
        <ProtectedRoute requiredModule="finans">
          <Muhasebe />
        </ProtectedRoute>
      } />
      <Route path="/cek-senet" element={
        <ProtectedRoute requiredModule="finans">
          <CekSenet />
        </ProtectedRoute>
      } />
      <Route path="/demirbas" element={
        <ProtectedRoute requiredModule="finans">
          <Demirbas />
        </ProtectedRoute>
      } />
      <Route path="/stok" element={
        <ProtectedRoute requiredModule="finans">
          <Stok />
        </ProtectedRoute>
      } />
      <Route path="/tabs" element={
        <ProtectedRoute requiredModule="finans">
          <FinancialManagementTabs />
        </ProtectedRoute>
      } />
    </Routes>
  );
};

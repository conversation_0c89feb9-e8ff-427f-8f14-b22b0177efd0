
import { LucideIcon } from "lucide-react";

export interface User {
  id: number;
  name: string;
  role: string;
  email: string;
  avatar: string;
  permissions: string[];
  companyId?: number;
}

export interface Company {
  id: number;
  name: string;
  code: string;
  active: boolean;
  modules: string[];
  optionalModules: string[];
  type: string;
  contactInfo?: {
    phone?: string;
    email?: string;
    address?: string;
  };
  taxInfo?: {
    taxOffice?: string;
    taxNumber?: string;
  };
  subscription?: {
    plan?: string;
    status?: string;
    nextBillingDate?: string;
    amount?: number;
  };
}

export interface ModuleInfo {
  id: string;
  name: string;
  icon: LucideIcon;
  type: string;
  color: string;
  bgColor: string;
  features?: string[];
}

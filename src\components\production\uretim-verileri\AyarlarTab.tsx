import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pencil, Trash, Info, Calendar as CalendarIcon, Save } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { ApiService } from '@/services/apiService';
import { UrunModel } from '@/models/UrunModel';
import { SiloData } from '@/models/SiloData';
import UrunSecimi from '../../../../settings/UrunSecimi';
import UrunSecimiHat4 from './UrunSecimiHat4';
import UrunSecimiHat5 from './UrunSecimiHat5';
import SiloDataTable from './SiloDataTable';
import SiloDataTable2 from './SiloDataTable2';
import SiloDataTable3 from './SiloDataTable3';
import SiloDataTable4 from './SiloDataTable4';
import SiloDataTable5 from './SiloDataTable5';


// UrunSecimi bileşenindeki StaticUrunData buraya taşındı - Hat 1, 2, 3 için güncellenmiş
const StaticUrunData = {
  boruTipleri: ["Ø 80", "Ø 100", "Ø 125", "Ø 160", "Ø 200"],
  altUrunler: {
    "Ø 80": [
      { ad: "Ø 80 DELİKSİZ BORU", stokKodu: "MRZ-ST00662" },
      { ad: "Ø 80 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST00663" },
      { ad: "Ø 80 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST10663" }
    ],
    "Ø 100": [
      { ad: "Ø 100 DELİKSİZ BORU", stokKodu: "MRZ-ST00659" },
      { ad: "Ø 100 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02549" },
      { ad: "Ø 100 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12549" }
    ],
    "Ø 125": [
      { ad: "Ø 125 DELİKSİZ BORU", stokKodu: "MRZ-ST00664" },
      { ad: "Ø 125 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST00666" },
      { ad: "Ø 125 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST10666" }
    ],
    "Ø 160": [
      { ad: "Ø 160 DELİKSİZ BORU", stokKodu: "MRZ-ST00658" },
      { ad: "Ø 160 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02550" },
      { ad: "Ø 160 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12550" }
    ],
    "Ø 200": [
      { ad: "Ø 200 DELİKSİZ BORU", stokKodu: "MRZ-ST00660" },
      { ad: "Ø 200 KEÇELİ BORU (DELİKLİ-KEÇELİ)", stokKodu: "MRZ-ST02551" },
      { ad: "Ø 200 KEÇELİ DÖNÜŞÜM", stokKodu: "MRZ-ST12551" }
    ]
  }
};

// Hat 4 için özel ürün listesi - Sadece TÜNELLİ borular
const Hat4UrunData = {
  boruTipleri: ["Ø 160", "Ø 200"],
  altUrunler: {
    "Ø 160": [
      { ad: "Ø 160 TÜNELLİ", stokKodu: "MRZ-ST667" }
    ],
    "Ø 200": [
      { ad: "Ø 200 TÜNELLİ", stokKodu: "MRZ-ST668" }
    ]
  }
};

// Hat 5 için özel ürün listesi - Sadece KEÇE ürünleri
const Hat5UrunData = {
  boruTipleri: ["Ø 80", "Ø 100", "Ø 125", "Ø 160", "Ø 200"],
  altUrunler: {
    "Ø 80": [
      { ad: "Ø 80 KEÇE", stokKodu: "KC-STK080" }
    ],
    "Ø 100": [
      { ad: "Ø 100 KEÇE", stokKodu: "KC-STK100" }
    ],
    "Ø 125": [
      { ad: "Ø 125 KEÇE", stokKodu: "KC-STK125" }
    ],
    "Ø 160": [
      { ad: "Ø 160 KEÇE", stokKodu: "KC-STK160" }
    ],
    "Ø 200": [
      { ad: "Ø 200 KEÇE", stokKodu: "KC-STK200" }
    ]
  }
};

interface AyarlarTabProps {
  refreshInterval: number;
  onRefreshIntervalChange: (value: string) => void;
  autoRefresh: boolean;
  onAutoRefreshChange: (checked: boolean) => void;
  onRefresh: () => void;
  siloDataList: SiloData[];
  urunler: UrunModel[];
  getUrunAdi: (stokKodu: string) => string;
  getUrunCap: (stokKodu: string) => string;
  currentLine: string;
}

const AyarlarTab: React.FC<AyarlarTabProps> = ({
  onRefresh,
  siloDataList,
  urunler,
  getUrunAdi,
  getUrunCap,
  currentLine
}) => {
  const { toast } = useToast();
  const [date, setDate] = useState<Date>(new Date());
  const [selectedDateType, setSelectedDateType] = useState<'tekGun' | 'aralik'>('tekGun');
  const [selectedDiameters, setSelectedDiameters] = useState<Record<string, boolean>>({
    "80": false,
    "100": false,
    "125": false,
    "160": true,
    "200": false
  });
  const [selectedBoruTipi, setSelectedBoruTipi] = useState<string>("");
  const [selectedAltUrun, setSelectedAltUrun] = useState<string>("");
  const [editingItem, setEditingItem] = useState<number | null>(null);
  const [editedStokKodu, setEditedStokKodu] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  // Düzenleme modunda kullanılan ürün tipi ve alt ürün state'leri
  const [editBoruTipi, setEditBoruTipi] = useState<string>("");
  const [editAltUrun, setEditAltUrun] = useState<string>("");

  // Display the first few items for the demo, but implement filtering logic
  const filteredData = siloDataList.slice(0, 10);

  // Veritabanından gelen saati doğru gösterme fonksiyonu
  const formatSaat = (item: SiloData): string => {
    try {
      // Saat string olarak geldiyse doğrudan göster
      if (item.saat && typeof item.saat === 'string') {
        return item.saat;
      }

      // Tarih nesnesiyse
      let date: Date;
      if (item.tarih instanceof Date) {
        date = new Date(item.tarih);
      } else {
        const tarihStr = String(item.tarih || '');
        date = new Date(tarihStr);
        if (isNaN(date.getTime())) {
          return '';
        }
      }

      // Doğrudan tarih formatı kullan (saat ayarlamadan)
      return format(new Date(date), 'HH:mm');
    } catch (e) {
      console.error('Tarih biçimlendirme hatası:', e);
      return '';
    }
  };

  const resetFilters = () => {
    setSelectedDiameters({
      "80": false,
      "100": false,
      "125": false,
      "160": true,
      "200": false
    });
    setDate(new Date());
    setSelectedDateType('tekGun');
    setSelectedBoruTipi("");
    setSelectedAltUrun("");
  };

  const handleSaveStokKodu = async () => {
    if (!selectedAltUrun) {
      toast({
        title: "Hata",
        description: "Lütfen bir stok kodu seçiniz",
        variant: "destructive"
      });
      return;
    }
    setLoading(true);
    try {
      let saveFunc;
      switch (currentLine) {
        case "line1":
          saveFunc = ApiService.saveStokKodu;
          break;
        case "line2":
          saveFunc = ApiService.saveStokKodu2;
          break;
        case "line3":
          saveFunc = ApiService.saveStokKodu3;
          break;
        case "line4":
          saveFunc = ApiService.saveStokKodu4;
          break;
        case "line5":
          saveFunc = ApiService.saveStokKodu5;
          break;
        default:
          saveFunc = ApiService.saveStokKodu;
      }
      await saveFunc(selectedAltUrun);
      toast({
        title: "Başarılı",
        description: "Stok kodu başarıyla kaydedildi",
      });
      onRefresh();
      setSelectedBoruTipi("");
      setSelectedAltUrun("");
    } catch (error) {
      console.error("Stok kodu kaydedilirken hata oluştu:", error);
      let errorMessage = "Stok kodu kaydedilemedi";
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
      }
      toast({
        title: "Hata",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Düzenleme modunda stok kodunu bularak boru tipi ve alt ürün değerlerini ayarla
  useEffect(() => {
    if (editingItem) {
      const stokKodu = editedStokKodu;

      // Hat 4 ve Hat 5 için özel kontrol
      const urunData = currentLine === "line4" ? Hat4UrunData :
                       currentLine === "line5" ? Hat5UrunData : StaticUrunData;

      // Stok kodundan boru tipini ve alt ürünü bul
      for (const boruTipi of urunData.boruTipleri) {
        const altUrunler = urunData.altUrunler[boruTipi];
        const altUrun = altUrunler.find(urun => urun.stokKodu === stokKodu);

        if (altUrun) {
          setEditBoruTipi(boruTipi);
          setEditAltUrun(stokKodu);
          break;
        }
      }
    } else {
      // Düzenleme modu kapatıldığında temizle
      setEditBoruTipi("");
      setEditAltUrun("");
    }
  }, [editingItem, editedStokKodu, currentLine]);

  const handleEditItem = (item: SiloData) => {
    setEditingItem(item.id);
    setEditedStokKodu(item.stokKodu);
  };

  const handleUpdateItem = async (itemId: number) => {
    if (!editAltUrun) {
      toast({
        title: "Hata",
        description: "Lütfen bir ürün seçiniz",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      let updateFunc;
      switch (currentLine) {
        case "line1":
          updateFunc = ApiService.updateSiloData;
          break;
        case "line2":
          updateFunc = ApiService.updateSiloData2;
          break;
        case "line3":
          updateFunc = ApiService.updateSiloData3;
          break;
        case "line4":
          updateFunc = ApiService.updateSiloData4;
          break;
        case "line5":
          updateFunc = ApiService.updateSiloData5;
          break;
        default:
          updateFunc = ApiService.updateSiloData;
      }
      await updateFunc(itemId, editAltUrun);
      toast({
        title: "Başarılı",
        description: "Kayıt başarıyla güncellendi",
      });
      onRefresh();
      setEditingItem(null);
      setEditedStokKodu("");
      setEditBoruTipi("");
      setEditAltUrun("");
    } catch (error) {
      console.error("Kayıt güncellenirken hata oluştu:", error);
      toast({
        title: "Hata",
        description: "Kayıt güncellenemedi",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (itemId: number) => {
    if (!confirm("Bu kaydı silmek istediğinize emin misiniz?")) {
      return;
    }
    setLoading(true);
    try {
      let deleteFunc;
      switch (currentLine) {
        case "line1":
          deleteFunc = ApiService.deleteSiloData;
          break;
        case "line2":
          deleteFunc = ApiService.deleteSiloData2;
          break;
        case "line3":
          deleteFunc = ApiService.deleteSiloData3;
          break;
        case "line4":
          deleteFunc = ApiService.deleteSiloData4;
          break;
        case "line5":
          deleteFunc = ApiService.deleteSiloData5;
          break;
        default:
          deleteFunc = ApiService.deleteSiloData;
      }
      await deleteFunc(itemId);
      toast({
        title: "Başarılı",
        description: "Kayıt başarıyla silindi",
      });
      onRefresh();
    } catch (error) {
      console.error("Kayıt silinirken hata oluştu:", error);
      toast({
        title: "Hata",
        description: "Kayıt silinemedi",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const cancelEditing = () => {
    setEditingItem(null);
    setEditedStokKodu("");
    setEditBoruTipi("");
    setEditAltUrun("");
  };

  const handleUrunTipiChange = (value: string) => {
    setSelectedBoruTipi(value);
    setSelectedAltUrun("");
  };

  const handleAltUrunChange = (value: string) => {
    setSelectedAltUrun(value);
  };

  const handleEditBoruTipiChange = (value: string) => {
    setEditBoruTipi(value);
    setEditAltUrun("");
  };

  const handleEditAltUrunChange = (value: string) => {
    setEditAltUrun(value);
  };

  // Düzenleme modunda seçilen boru tipine göre alt ürünleri getir
  const getEditAltUrunler = () => {
    const urunData = currentLine === "line4" ? Hat4UrunData :
                     currentLine === "line5" ? Hat5UrunData : StaticUrunData;
    if (!editBoruTipi || !urunData.altUrunler[editBoruTipi]) {
      return [];
    }
    return urunData.altUrunler[editBoruTipi];
  };

  return (
    <div className="w-full max-w-full mx-0 px-0" style={{ width: '100%', maxWidth: '100%' }}>
      {/* İki sütunlu layout - tam genişlikte ve responsive */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 w-full" style={{ width: '100%' }}>
        {/* Sol sütun: Tarih filtreleri ve boru çapı */}
        <Card className="h-full w-full">
          <CardContent className="p-4 sm:p-6">
            <div className="space-y-5">
              {/* Tarih Filtreleri */}
              <div>
                <div className="flex items-center mb-3">
                  <CalendarIcon className="mr-2 h-5 w-5 text-blue-500" />
                  <h3 className="font-medium">Tarih Filtreleri</h3>
                </div>

                <div className="flex space-x-2 mb-3">
                  <Button
                    variant={selectedDateType === 'tekGun' ? 'default' : 'outline'}
                    onClick={() => setSelectedDateType('tekGun')}
                    className="text-sm"
                  >
                    Tek Gün
                  </Button>
                  <Button
                    variant={selectedDateType === 'aralik' ? 'default' : 'outline'}
                    onClick={() => setSelectedDateType('aralik')}
                    className="text-sm"
                  >
                    Aralık
                  </Button>
                </div>

                <div className="mb-3">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, 'dd.MM.yyyy') : 'Tarih seçin'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={(date) => date && setDate(date)}
                        initialFocus
                        className="rounded-md border p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Boru Çapı */}
              <div>
                <div className="flex items-center mb-3">
                  <svg viewBox="0 0 24 24" className="mr-2 h-5 w-5 text-blue-500" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="12" r="6" />
                    <circle cx="12" cy="12" r="2" />
                  </svg>
                  <h3 className="font-medium">Boru Çapı</h3>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {Object.keys(selectedDiameters).map((diameter) => (
                    <div key={diameter} className="flex items-center space-x-2">
                      <Checkbox
                        id={`diameter-${diameter}`}
                        checked={selectedDiameters[diameter]}
                        onCheckedChange={(checked) => {
                          setSelectedDiameters(prev => ({
                            ...prev,
                            [diameter]: checked as boolean
                          }));
                        }}
                      />
                      <label htmlFor={`diameter-${diameter}`} className="text-sm">
                        Ø {diameter}
                      </label>
                    </div>
                  ))}
                </div>

                <div className="mt-4">
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="text-sm w-full"
                  >
                    Temizle
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sağ sütun: Ürün seçimi - Hat 4 ve Hat 5 için özel bileşenler, diğerleri için normal bileşen */}
        {currentLine === "line4" ? (
          <UrunSecimiHat4
            urunler={urunler}
            secilenUrunTipi={selectedBoruTipi}
            secilenAltUrun={selectedAltUrun}
            handleUrunTipiChange={handleUrunTipiChange}
            handleAltUrunChange={handleAltUrunChange}
            handleSaveStokKodu={handleSaveStokKodu}
          />
        ) : currentLine === "line5" ? (
          <UrunSecimiHat5
            urunler={urunler}
            secilenUrunTipi={selectedBoruTipi}
            secilenAltUrun={selectedAltUrun}
            handleUrunTipiChange={handleUrunTipiChange}
            handleAltUrunChange={handleAltUrunChange}
            handleSaveStokKodu={handleSaveStokKodu}
          />
        ) : (
          <UrunSecimi
            urunler={urunler}
            secilenUrunTipi={selectedBoruTipi}
            secilenAltUrun={selectedAltUrun}
            handleUrunTipiChange={handleUrunTipiChange}
            handleAltUrunChange={handleAltUrunChange}
            handleSaveStokKodu={handleSaveStokKodu}
          />
        )}
      </div>

      {/* Veri Düzenleme - Tam genişlikte */}
      {currentLine === "line2" ? (
        <SiloDataTable2
          siloDataList={siloDataList}
          urunAdlari={Object.fromEntries(urunler.map(u => [u.stokKodu, u.stokAdi]))}
          urunler={urunler}
          handleUpdateSiloData={handleUpdateItem}
          handleDeleteSiloData={handleDeleteItem}
        />
      ) : currentLine === "line3" ? (
        <SiloDataTable3
          siloDataList={siloDataList}
          urunAdlari={Object.fromEntries(urunler.map(u => [u.stokKodu, u.stokAdi]))}
          urunler={urunler}
          handleUpdateSiloData={handleUpdateItem}
          handleDeleteSiloData={handleDeleteItem}
        />
      ) : currentLine === "line4" ? (
        <SiloDataTable4
          siloDataList={siloDataList}
          urunAdlari={Object.fromEntries(urunler.map(u => [u.stokKodu, u.stokAdi]))}
          urunler={urunler}
          handleUpdateSiloData={handleUpdateItem}
          handleDeleteSiloData={handleDeleteItem}
        />
      ) : currentLine === "line5" ? (
        <SiloDataTable5
          siloDataList={siloDataList}
          urunAdlari={Object.fromEntries(urunler.map(u => [u.stokKodu, u.stokAdi]))}
          urunler={urunler}
          handleUpdateSiloData={handleUpdateItem}
          handleDeleteSiloData={handleDeleteItem}
        />
      ) : (
        <SiloDataTable
          siloDataList={siloDataList}
          urunAdlari={Object.fromEntries(urunler.map(u => [u.stokKodu, u.stokAdi]))}
          urunler={urunler}
          handleUpdateSiloData={handleUpdateItem}
          handleDeleteSiloData={handleDeleteItem}
        />
      )}
    </div>
  );
};

export default AyarlarTab;

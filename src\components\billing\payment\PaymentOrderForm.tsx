
import React from "react";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
  paymentOrderSchema, 
  PaymentOrderFormValues 
} from "./PaymentOrderFormTypes";
import {
  DocumentNumberField,
  RequestingUserField,
  DateField,
  DueDateField,
  AmountField,
  SourceTypeField,
  PriorityField,
  DetailsField,
  SupplierInfo
} from "./form/FormFields";
import { ApprovalSection } from "./form/ApprovalSection";
import { FormActions } from "./form/FormActions";

type PaymentOrderFormProps = {
  invoice: any | null;
  defaultValues: Partial<PaymentOrderFormValues>;
  onSubmit: (data: PaymentOrderFormValues) => Promise<void>;
  onCancel: () => void;
  loading: boolean;
};

export const PaymentOrderForm = ({ 
  invoice, 
  defaultValues, 
  onSubmit, 
  onCancel, 
  loading 
}: PaymentOrderFormProps) => {
  // Initialize the form
  const form = useForm<PaymentOrderFormValues>({
    resolver: zodResolver(paymentOrderSchema),
    defaultValues,
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-4 gap-3">
          {/* İlk satır */}
          <DocumentNumberField form={form} />
          <DateField form={form} />
          <DueDateField form={form} />
          <AmountField form={form} />
        </div>

        <div className="grid grid-cols-3 gap-3">
          {/* İkinci satır */}
          <RequestingUserField form={form} />
          <SourceTypeField form={form} />
          <PriorityField form={form} />
        </div>

        {/* Cari hesap bilgisi (varsa) */}
        {invoice && (
          <div className="grid grid-cols-1 gap-3">
            <SupplierInfo invoice={invoice} />
          </div>
        )}

        {/* Açıklama alanı */}
        <DetailsField form={form} />

        {/* Onay bilgileri bölümü */}
        <ApprovalSection />

        {/* Form düğmeleri */}
        <FormActions onCancel={onCancel} loading={loading} />
      </form>
    </Form>
  );
};

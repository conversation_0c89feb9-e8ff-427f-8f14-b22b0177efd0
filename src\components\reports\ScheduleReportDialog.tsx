
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useReportsStore } from "@/stores/reportsStore";
import { useToast } from "@/hooks/use-toast";

interface ScheduleReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ScheduleReportDialog = ({ open, onOpenChange }: ScheduleReportDialogProps) => {
  const [selectedReportId, setSelectedReportId] = useState("");
  const [frequency, setFrequency] = useState<"daily" | "weekly" | "monthly">("weekly");
  const [recipientEmail, setRecipientEmail] = useState("");
  const [recipients, setRecipients] = useState<string[]>([]);

  const { savedReports, scheduleReport } = useReportsStore();
  const { toast } = useToast();

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedReportId(savedReports.length > 0 ? savedReports[0].id : "");
      setFrequency("weekly");
      setRecipientEmail("");
      setRecipients([]);
    }
  }, [open, savedReports]);

  const handleAddRecipient = () => {
    if (!recipientEmail) return;
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      toast({
        title: "Geçersiz E-posta",
        description: "Lütfen geçerli bir e-posta adresi girin.",
        variant: "destructive",
      });
      return;
    }

    if (!recipients.includes(recipientEmail)) {
      setRecipients([...recipients, recipientEmail]);
      setRecipientEmail("");
    }
  };

  const handleRemoveRecipient = (email: string) => {
    setRecipients(recipients.filter(r => r !== email));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedReportId) {
      toast({
        title: "Hata",
        description: "Lütfen bir rapor seçin.",
        variant: "destructive",
      });
      return;
    }

    if (recipients.length === 0) {
      toast({
        title: "Hata",
        description: "En az bir alıcı eklemelisiniz.",
        variant: "destructive",
      });
      return;
    }

    // Schedule the report
    scheduleReport(selectedReportId, frequency, recipients);

    toast({
      title: "Rapor Zamanlandı",
      description: "Rapor başarıyla zamanlandı.",
    });

    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Rapor Zamanla</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="report" className="text-right">
                Rapor
              </Label>
              <Select 
                value={selectedReportId} 
                onValueChange={setSelectedReportId}
                disabled={savedReports.length === 0}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Rapor seçin" />
                </SelectTrigger>
                <SelectContent>
                  {savedReports.map(report => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="frequency" className="text-right">
                Sıklık
              </Label>
              <Select 
                value={frequency} 
                onValueChange={(value: "daily" | "weekly" | "monthly") => setFrequency(value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Rapor sıklığı seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Günlük</SelectItem>
                  <SelectItem value="weekly">Haftalık</SelectItem>
                  <SelectItem value="monthly">Aylık</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Alıcılar
              </Label>
              <div className="col-span-3 space-y-2">
                <div className="flex space-x-2">
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                  />
                  <Button type="button" variant="outline" onClick={handleAddRecipient}>
                    Ekle
                  </Button>
                </div>
                <div className="space-y-1">
                  {recipients.map((email) => (
                    <div key={email} className="flex items-center justify-between rounded bg-muted p-2 text-sm">
                      <span>{email}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveRecipient(email)}
                      >
                        Kaldır
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Zamanla</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};


import { 
  Card, 
  CardContent
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  MaintenanceSchedule
} from "@/stores/maintenanceStore";
import { Vehicle } from "@/stores/vehicleStore";
import { Badge } from "@/components/ui/badge";
import { format, addDays, isBefore } from "date-fns";
import { tr } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Edit, Trash, Bell, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MaintenanceSchedulesListProps {
  schedules: MaintenanceSchedule[];
  vehicles: Vehicle[];
  compact?: boolean;
}

export const MaintenanceSchedulesList = ({ schedules, vehicles, compact = false }: MaintenanceSchedulesListProps) => {
  const { toast } = useToast();
  
  const getVehicleName = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    return vehicle ? `${vehicle.name} (${vehicle.plate})` : 'Bilinmiyor';
  };
  
  const getFrequencyText = (schedule: MaintenanceSchedule) => {
    switch (schedule.frequency) {
      case 'daily':
        return 'Günlük';
      case 'weekly':
        return 'Haftalık';
      case 'monthly':
        return 'Aylık';
      case 'quarterly':
        return 'Üç Aylık';
      case 'yearly':
        return 'Yıllık';
      case 'custom':
        return `${schedule.intervalDays} günde bir`;
      default:
        return 'Belirsiz';
    }
  };
  
  const isUpcoming = (date: string, reminderDays: number) => {
    const today = new Date();
    const reminderDate = addDays(new Date(date), -reminderDays);
    return isBefore(today, new Date(date)) && isBefore(reminderDate, today);
  };
  
  const getStatusBadge = (schedule: MaintenanceSchedule) => {
    if (!schedule.isActive) {
      return <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-300">Pasif</Badge>;
    }
    
    if (isUpcoming(schedule.nextDate, schedule.reminderDays)) {
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-300">Yaklaşıyor</Badge>;
    }
    
    if (isBefore(new Date(schedule.nextDate), new Date())) {
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">Geçti</Badge>;
    }
    
    return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">Aktif</Badge>;
  };
  
  const handleComplete = (id: string) => {
    toast({
      title: "Tamamlandı Olarak İşaretle",
      description: "Bakım tamamlandı olarak işaretlendi.",
    });
  };
  
  const handleEdit = (id: string) => {
    toast({
      title: "Düzenleme",
      description: "Bakım takvimi düzenleme özelliği henüz geliştiriliyor.",
    });
  };
  
  const handleDelete = (id: string) => {
    toast({
      title: "Silme",
      description: "Bakım takvimi silme özelliği henüz geliştiriliyor.",
    });
  };
  
  if (schedules.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Henüz bakım takvimi bulunmuyor.</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Başlık</TableHead>
              <TableHead>Araç</TableHead>
              {!compact && <TableHead>Sıklık</TableHead>}
              <TableHead>Sonraki Tarih</TableHead>
              <TableHead>Durum</TableHead>
              {!compact && <TableHead>Tahmini Maliyet</TableHead>}
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {schedules.map((schedule) => (
              <TableRow key={schedule.id}>
                <TableCell className="font-medium">{schedule.title}</TableCell>
                <TableCell>{getVehicleName(schedule.vehicleId)}</TableCell>
                {!compact && <TableCell>{getFrequencyText(schedule)}</TableCell>}
                <TableCell>{format(new Date(schedule.nextDate), 'dd MMM yyyy', { locale: tr })}</TableCell>
                <TableCell>{getStatusBadge(schedule)}</TableCell>
                {!compact && <TableCell>{schedule.estimatedCost.toLocaleString('tr-TR')} ₺</TableCell>}
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {isUpcoming(schedule.nextDate, schedule.reminderDays) && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="text-amber-600"
                      >
                        <Bell className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="text-green-600"
                      onClick={() => handleComplete(schedule.id)}
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                    {!compact && (
                      <>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleEdit(schedule.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-red-600"
                          onClick={() => handleDelete(schedule.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};


import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ShieldCheck } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { CompaniesTab } from "./tabs/CompaniesTab";
import { UsersTab } from "./tabs/UsersTab";
import { ModulesTab } from "./tabs/ModulesTab";
import { UserPermissionsTab } from "./tabs/UserPermissionsTab";
import { basicModules, optionalModules } from "./data/moduleData";
import { User, Company } from "./types";

export const AdminPanel = () => {
  const { user } = useAuth();
  
  const [companies, setCompanies] = useState<Company[]>([
    { 
      id: 1, 
      name: "<PERSON><PERSON>", 
      code: "12628313332", 
      active: true, 
      modules: ["finans", "stok", "insan_kaynaklari", "musteri"], 
      optionalModules: ["uretim", "proje"], 
      type: "Admin",
      contactInfo: {
        email: "<EMAIL>",
        phone: "0212 555 1234"
      }
    },
    { 
      id: 2, 
      name: "Teknoloji A.Ş.", 
      code: "10001", 
      active: true, 
      modules: ["finans", "stok", "insan_kaynaklari", "musteri"], 
      optionalModules: ["uretim", "proje"], 
      type: "Kurumsal",
      contactInfo: {
        email: "<EMAIL>",
        phone: "0216 444 5678",
        address: "Levent, İstanbul"
      },
      taxInfo: {
        taxOffice: "Mecidiyeköy",
        taxNumber: "1234567890"
      },
      subscription: {
        plan: "Kurumsal",
        status: "Aktif",
        nextBillingDate: "2024-12-31",
        amount: 1299
      }
    },
    { 
      id: 3, 
      name: "Yazılım Ltd. Şti.", 
      code: "10002", 
      active: true, 
      modules: ["finans", "stok", "musteri"], 
      optionalModules: ["proje", "satis"], 
      type: "KOBİ",
      contactInfo: {
        email: "<EMAIL>",
        phone: "0312 333 4455"
      },
      subscription: {
        plan: "Profesyonel",
        status: "Aktif",
        nextBillingDate: "2025-01-15",
        amount: 799
      }
    },
    { 
      id: 4, 
      name: "Üretim Sistemleri", 
      code: "10003", 
      active: false, 
      modules: ["finans", "stok", "musteri"], 
      optionalModules: ["uretim", "tedarik"], 
      type: "Üretim",
      contactInfo: {
        email: "<EMAIL>"
      },
      subscription: {
        plan: "Enterprise",
        status: "Süresi Dolmuş",
        nextBillingDate: "2023-11-30",
        amount: 2499
      }
    }
  ]);

  const [users, setUsers] = useState<User[]>([
    { id: 1, name: "Mehmet Aşık", role: "Admin", email: "<EMAIL>", avatar: "MA", permissions: ["all"] },
    { id: 2, name: "Ayşe Demir", role: "Müdür", email: "<EMAIL>", avatar: "AD", companyId: 2, permissions: ["finans", "stok", "insan_kaynaklari", "uretim"] },
    { id: 3, name: "Can Yılmaz", role: "Muhasebeci", email: "<EMAIL>", avatar: "CY", companyId: 2, permissions: ["finans", "stok"] },
    { id: 4, name: "Ali Öztürk", role: "Müdür", email: "<EMAIL>", avatar: "AÖ", companyId: 3, permissions: ["finans", "stok", "musteri", "proje"] }
  ]);

  // Admin check
  if (user?.role !== "Admin") {
    return (
      <div className="p-8 text-center">
        <ShieldCheck className="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Yetkisiz Erişim</h2>
        <p className="text-muted-foreground">Bu sayfaya erişmek için admin yetkisine sahip olmanız gerekmektedir.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Yönetim Paneli</CardTitle>
              <CardDescription>Firma ve kullanıcı yönetimini buradan yapabilirsiniz</CardDescription>
            </div>
            <Badge className="bg-blue-500">Yönetici</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="companies" className="space-y-4">
            <TabsList>
              <TabsTrigger value="companies">Firmalar</TabsTrigger>
              <TabsTrigger value="users">Kullanıcılar</TabsTrigger>
              <TabsTrigger value="modules">Modül Yönetimi</TabsTrigger>
              <TabsTrigger value="user-permissions">Kullanıcı Yetkileri</TabsTrigger>
            </TabsList>
            
            <TabsContent value="companies" className="space-y-4">
              <CompaniesTab 
                companies={companies} 
                setCompanies={setCompanies} 
              />
            </TabsContent>
            
            <TabsContent value="users" className="space-y-4">
              <UsersTab 
                users={users} 
                setUsers={setUsers} 
                companies={companies}
              />
            </TabsContent>
            
            <TabsContent value="modules" className="space-y-4">
              <ModulesTab 
                companies={companies}
                setCompanies={setCompanies}
                basicModules={basicModules}
                optionalModules={optionalModules}
              />
            </TabsContent>
            
            <TabsContent value="user-permissions" className="space-y-4">
              <UserPermissionsTab 
                users={users}
                setUsers={setUsers}
                companies={companies}
                basicModules={basicModules}
                optionalModules={optionalModules}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};


import { CariHesap } from "@/types/cari";

// Base API URL is handled by the proxy in development
const API_URL = '/api/musteri';

/**
 * Cari hesap servisi - müşteri ve tedarikçi verilerini yönetir
 */
export const cariService = {
  /**
   * Tüm cari hesapları getirir
   */
  getAll: async (): Promise<CariHesap[]> => {
    try {
      const response = await fetch(API_URL);
      
      if (!response.ok) {
        const text = await response.text();
        console.error('API Error Response:', text);
        throw new Error(`API yanıt vermedi: ${response.status} - ${response.statusText}`);
      }
      
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error('Invalid Content Type:', contentType);
        console.error('Response Text:', text);
        throw new Error(`API geçersiz yanıt türü döndürdü: ${contentType}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Cari hesapları getirme hatası:', error);
      throw error;
    }
  },

  /**
   * ID'ye göre cari hesap getirir
   */
  getById: async (id: string): Promise<CariHesap> => {
    try {
      const response = await fetch(`${API_URL}/${id}`);
      
      if (!response.ok) {
        const text = await response.text();
        console.error('API Error Response:', text);
        throw new Error(`API yanıt vermedi: ${response.status} - ${response.statusText}`);
      }
      
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error('Invalid Content Type:', contentType);
        console.error('Response Text:', text);
        throw new Error(`API geçersiz yanıt türü döndürdü: ${contentType}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Cari hesap getirme hatası:', error);
      throw error;
    }
  },

  /**
   * Yeni cari hesap oluşturur
   */
  create: async (cariHesap: Omit<CariHesap, 'id'>): Promise<{ id: string }> => {
    try {
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cariHesap),
      });
      
      if (!response.ok) {
        throw new Error(`API yanıt vermedi: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Cari hesap oluşturma hatası:', error);
      throw error;
    }
  },

  /**
   * Cari hesap günceller
   */
  update: async (id: string, cariHesap: Partial<CariHesap>): Promise<void> => {
    try {
      const response = await fetch(`${API_URL}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cariHesap),
      });
      
      if (!response.ok) {
        throw new Error(`API yanıt vermedi: ${response.status}`);
      }
    } catch (error) {
      console.error('Cari hesap güncelleme hatası:', error);
      throw error;
    }
  },

  /**
   * Cari hesap siler
   */
  delete: async (id: string): Promise<void> => {
    try {
      const response = await fetch(`${API_URL}/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`API yanıt vermedi: ${response.status}`);
      }
    } catch (error) {
      console.error('Cari hesap silme hatası:', error);
      throw error;
    }
  }
};


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { Calculator, Download, RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Demo data
const depreciationData = [
  { id: "1", assetCode: "D001", assetName: "Ofis Bilgisayarları", acquisitionCost: 85000.00, acquisitionDate: "2023-03-15", depreciationRate: 20, depreciationTerm: 5, method: "straight-line", annualDepreciation: 17000.00, accumulatedDepreciation: 17000.00, currentValue: 68000.00, period: "2023" },
  { id: "2", assetCode: "D002", assetName: "Of<PERSON>yaları", acquisitionCost: 35000.00, acquisitionDate: "2023-04-10", depreciationRate: 10, depreciationTerm: 10, method: "straight-line", annualDepreciation: 3500.00, accumulatedDepreciation: 3500.00, currentValue: 31500.00, period: "2023" },
  { id: "3", assetCode: "D003", assetName: "Yazılım Lisansları", acquisitionCost: 45000.00, acquisitionDate: "2023-05-20", depreciationRate: 20, depreciationTerm: 5, method: "straight-line", annualDepreciation: 9000.00, accumulatedDepreciation: 9000.00, currentValue: 36000.00, period: "2023" },
  { id: "4", assetCode: "D004", assetName: "Şirket Araçları", acquisitionCost: 750000.00, acquisitionDate: "2022-08-15", depreciationRate: 20, depreciationTerm: 5, method: "straight-line", annualDepreciation: 150000.00, accumulatedDepreciation: 150000.00, currentValue: 600000.00, period: "2023" },
  { id: "5", assetCode: "D005", assetName: "Depo Rafları", acquisitionCost: 28000.00, acquisitionDate: "2022-10-25", depreciationRate: 15, depreciationTerm: 7, method: "straight-line", annualDepreciation: 4200.00, accumulatedDepreciation: 4200.00, currentValue: 23800.00, period: "2023" },
  { id: "6", assetCode: "D006", assetName: "Klima Sistemleri", acquisitionCost: 42000.00, acquisitionDate: "2022-11-30", depreciationRate: 15, depreciationTerm: 7, method: "straight-line", annualDepreciation: 6300.00, accumulatedDepreciation: 6300.00, currentValue: 35700.00, period: "2023" }
];

// Column definitions
const depreciationColumns = [
  {
    header: "Demirbaş Kodu",
    accessorKey: "assetCode"
  },
  {
    header: "Demirbaş Adı",
    accessorKey: "assetName"
  },
  {
    header: "Alım Bedeli",
    accessorKey: "acquisitionCost",
    cell: ({ row }) => {
      return <span>₺ {row.original.acquisitionCost.toLocaleString()}</span>;
    }
  },
  {
    header: "Amortisman Oranı",
    accessorKey: "depreciationRate",
    cell: ({ row }) => {
      return <span>%{row.original.depreciationRate}</span>;
    }
  },
  {
    header: "Amortisman Süresi",
    accessorKey: "depreciationTerm",
    cell: ({ row }) => {
      return <span>{row.original.depreciationTerm} yıl</span>;
    }
  },
  {
    header: "Yöntem",
    accessorKey: "method",
    cell: ({ row }) => {
      const methods: { [key: string]: string } = {
        "straight-line": "Normal Amortisman",
        "declining-balance": "Azalan Bakiyeler",
        "units-of-production": "Üretim Miktarı"
      };
      
      return <span>{methods[row.original.method] || row.original.method}</span>;
    }
  },
  {
    header: "Yıllık Amortisman",
    accessorKey: "annualDepreciation",
    cell: ({ row }) => {
      return <span>₺ {row.original.annualDepreciation.toLocaleString()}</span>;
    }
  },
  {
    header: "Birikmiş Amortisman",
    accessorKey: "accumulatedDepreciation",
    cell: ({ row }) => {
      return <span>₺ {row.original.accumulatedDepreciation.toLocaleString()}</span>;
    }
  },
  {
    header: "Güncel Değer",
    accessorKey: "currentValue",
    cell: ({ row }) => {
      return <span className="font-medium">₺ {row.original.currentValue.toLocaleString()}</span>;
    }
  },
  {
    header: "Dönem",
    accessorKey: "period"
  }
];

export const DepreciationCalculation = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>("2023");
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  
  // Calculate totals
  const totalAcquisitionCost = depreciationData.reduce((sum, asset) => sum + asset.acquisitionCost, 0);
  const totalAnnualDepreciation = depreciationData.reduce((sum, asset) => sum + asset.annualDepreciation, 0);
  const totalAccumulatedDepreciation = depreciationData.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);
  const totalCurrentValue = depreciationData.reduce((sum, asset) => sum + asset.currentValue, 0);
  
  // Filter data based on selected method
  const filteredData = selectedMethod 
    ? depreciationData.filter(asset => asset.method === selectedMethod)
    : depreciationData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4 items-center">
          <div className="w-36">
            <Label htmlFor="period" className="mb-1 block">Dönem</Label>
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger id="period">
                <SelectValue placeholder="Dönem seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2023">2023</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
                <SelectItem value="2025">2025</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-64">
            <Label htmlFor="method" className="mb-1 block">Amortisman Yöntemi</Label>
            <Select value={selectedMethod || ""} onValueChange={value => setSelectedMethod(value === "" ? null : value)}>
              <SelectTrigger id="method">
                <SelectValue placeholder="Tüm Yöntemler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tüm Yöntemler</SelectItem>
                <SelectItem value="straight-line">Normal Amortisman</SelectItem>
                <SelectItem value="declining-balance">Azalan Bakiyeler</SelectItem>
                <SelectItem value="units-of-production">Üretim Miktarı</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Rapor Al
          </Button>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Yeniden Hesapla
          </Button>
          <Button>
            <Calculator className="mr-2 h-4 w-4" />
            Amortisman Hesapla
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Amortisman Hesaplamaları</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={depreciationColumns} data={filteredData} />
          
          {/* Summary */}
          <div className="mt-6 border-t pt-4">
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Toplam Maliyet Değeri:</span>
                <p className="font-medium">₺ {totalAcquisitionCost.toLocaleString()}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Yıllık Toplam Amortisman:</span>
                <p className="font-medium">₺ {totalAnnualDepreciation.toLocaleString()}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Birikmiş Amortisman:</span>
                <p className="font-medium">₺ {totalAccumulatedDepreciation.toLocaleString()}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Net Defter Değeri:</span>
                <p className="font-medium">₺ {totalCurrentValue.toLocaleString()}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

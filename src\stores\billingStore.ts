
import { create } from 'zustand';

export type Currency = 'TRY' | 'USD' | 'EUR' | 'GBP';

export type PaymentMethod = 'credit_card' | 'bank_transfer' | 'online_payment';

export type PaymentStatus = 'pending' | 'paid' | 'cancelled' | 'refunded';

export type InvoiceItem = {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  discountRate: number;
};

export type Invoice = {
  id: string;
  number: string;
  customerId: string;
  items: InvoiceItem[];
  currency: Currency;
  subtotal: number;
  taxTotal: number;
  discountTotal: number;
  total: number;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  installments?: number;
  dueDate: Date;
  createdAt: Date;
  updatedAt: Date;
};

type BillingState = {
  invoices: Invoice[];
  exchangeRates: Record<Currency, number>;
  isLoading: boolean;
  error: string | null;
  
  // Invoice operations
  createInvoice: (invoice: Omit<Invoice, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateInvoice: (id: string, updates: Partial<Invoice>) => void;
  deleteInvoice: (id: string) => void;
  
  // Exchange rate operations
  updateExchangeRates: (rates: Record<Currency, number>) => void;
  
  // Demo data
  loadDemoData: () => void;
};

export const useBillingStore = create<BillingState>((set) => ({
  invoices: [],
  exchangeRates: {
    TRY: 1,
    USD: 31.85,
    EUR: 34.52,
    GBP: 40.28,
  },
  isLoading: false,
  error: null,

  createInvoice: (invoiceData) => {
    const invoice: Invoice = {
      id: crypto.randomUUID(),
      ...invoiceData,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    set((state) => ({
      invoices: [...state.invoices, invoice],
    }));
  },

  updateInvoice: (id, updates) => {
    set((state) => ({
      invoices: state.invoices.map((invoice) =>
        invoice.id === id
          ? { ...invoice, ...updates, updatedAt: new Date() }
          : invoice
      ),
    }));
  },

  deleteInvoice: (id) => {
    set((state) => ({
      invoices: state.invoices.filter((invoice) => invoice.id !== id),
    }));
  },

  updateExchangeRates: (rates) => {
    set((state) => ({
      exchangeRates: { ...state.exchangeRates, ...rates },
    }));
  },

  loadDemoData: () => {
    const demoInvoices: Invoice[] = [
      {
        id: '1',
        number: 'INV-2024-001',
        customerId: '1',
        items: [
          {
            id: '1',
            description: 'Laptop X500',
            quantity: 2,
            unitPrice: 15000,
            taxRate: 0.18,
            discountRate: 0.1,
          },
        ],
        currency: 'TRY',
        subtotal: 30000,
        taxTotal: 5400,
        discountTotal: 3000,
        total: 32400,
        status: 'pending',
        paymentMethod: 'credit_card',
        installments: 6,
        dueDate: new Date(2024, 3, 15),
        createdAt: new Date(2024, 2, 15),
        updatedAt: new Date(2024, 2, 15),
      },
    ];

    set({ invoices: demoInvoices });
  },
}));

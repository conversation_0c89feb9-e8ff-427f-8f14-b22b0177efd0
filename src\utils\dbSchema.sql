
-- <PERSON><PERSON><PERSON><PERSON> tablolar için SQL şeması

-- <PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS urun_kategorileri (
  id VARCHAR(36) PRIMARY KEY,
  isim VARCHAR(100) NOT NULL,
  aciklama TEXT
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE IF NOT EXISTS tedarikci_firmalar (
  id VARCHAR(36) PRIMARY KEY,
  unvan VARCHAR(100) NOT NULL,
  yetkili_kisi VARCHAR(100),
  email VARCHAR(100),
  telefon VARCHAR(20),
  adres TEXT
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> için ek alanlar
ALTER TABLE urunler
ADD COLUMN IF NOT EXISTS maliyet DECIMAL(15,2) AFTER fiyat,
ADD COLUMN IF NOT EXISTS minimum_stok INT AFTER stok,
ADD COLUMN IF NOT EXISTS kategori_id VARCHAR(36) AFTER minimum_stok,
ADD COLUMN IF NOT EXISTS tedarikci_id VARCHAR(36) AFTER kategori_id,
ADD COLUMN IF NOT EXISTS barkod VARCHAR(50) AFTER tedarikci_id,
ADD COLUMN IF NOT EXISTS konum VARCHAR(50) AFTER barkod;

-- Raporlar tablosu
CREATE TABLE IF NOT EXISTS raporlar (
  id VARCHAR(36) PRIMARY KEY,
  isim VARCHAR(100) NOT NULL,
  aciklama TEXT,
  rapor_tipi VARCHAR(20) NOT NULL,
  zaman_dilimi VARCHAR(20) NOT NULL,
  grafik_tipi VARCHAR(20) NOT NULL,
  filtreler JSON,
  veri JSON,
  olusturma_tarihi TIMESTAMP NOT NULL,
  son_calisma_tarihi TIMESTAMP,
  zamanlama_aktif BOOLEAN DEFAULT false,
  zamanlama_siklik VARCHAR(20),
  zamanlama_alicilar JSON,
  zamanlama_son_gonderim TIMESTAMP
);

-- Bakım kayıtları tablosu
CREATE TABLE IF NOT EXISTS bakim_kayitlari (
  id VARCHAR(36) PRIMARY KEY,
  baslik VARCHAR(100) NOT NULL,
  aciklama TEXT,
  arac_id VARCHAR(36) NOT NULL,
  tip VARCHAR(20) NOT NULL,
  durum VARCHAR(20) NOT NULL,
  oncelik VARCHAR(20) NOT NULL,
  planlanan_tarih DATE NOT NULL,
  tamamlanan_tarih DATE,
  maliyet DECIMAL(15,2) NOT NULL,
  teknisyen VARCHAR(100) NOT NULL,
  notlar TEXT,
  olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bakım parçaları tablosu
CREATE TABLE IF NOT EXISTS bakim_parcalari (
  id VARCHAR(36) PRIMARY KEY,
  bakim_id VARCHAR(36) NOT NULL,
  isim VARCHAR(100) NOT NULL,
  miktar INT NOT NULL,
  birim_fiyat DECIMAL(15,2) NOT NULL,
  toplam_fiyat DECIMAL(15,2) NOT NULL,
  FOREIGN KEY (bakim_id) REFERENCES bakim_kayitlari(id)
);

-- Bakım takvimi tablosu
CREATE TABLE IF NOT EXISTS bakim_takvimi (
  id VARCHAR(36) PRIMARY KEY,
  arac_id VARCHAR(36) NOT NULL,
  baslik VARCHAR(100) NOT NULL,
  aciklama TEXT,
  siklik VARCHAR(20) NOT NULL,
  aralik_gun INT,
  sonraki_tarih DATE NOT NULL,
  hatirlatma_gun INT NOT NULL,
  tahmini_maliyet DECIMAL(15,2) NOT NULL,
  aktif BOOLEAN NOT NULL DEFAULT true,
  olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

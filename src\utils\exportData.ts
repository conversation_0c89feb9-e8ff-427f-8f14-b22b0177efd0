//#!/usr/bin/env node
import axios from 'axios';
import { SiloData } from '@/models/SiloData';
import { UrunModel } from '@/models/UrunModel';

// G<PERSON><PERSON>i yollar kullanıyoruz, API_URL'yi kaldırıyoru<PERSON>

// Müşteri (Cari) hizmetleri
export const musteriService = {
  getAll: async () => {
    const response = await axios.get(`/api/musteri`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/musteri/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/musteri`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/musteri/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/musteri/${id}`);
    return response.data;
  }
};

// Fatura hizmetleri
export const faturaService = {
  getAll: async () => {
    const response = await axios.get(`/api/fatura`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/fatura/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/fatura`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/fatura/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/fatura/${id}`);
    return response.data;
  }
};

// Ürün hizmetleri
export const urunService = {
  getAll: async () => {
    const response = await axios.get(`/api/urun`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/urun/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/urun`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/urun/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/urun/${id}`);
    return response.data;
  }
};

// Ödeme hizmetleri
export const odemeService = {
  getAll: async () => {
    const response = await axios.get(`/api/odeme`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/odeme/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/odeme`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/odeme/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/odeme/${id}`);
    return response.data;
  }
};

export class ApiService {
  // Silo verilerini getiren metot
  static async fetchSiloData(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => ({
        ...item,
        id: item.ID,
        tarih: new Date(item.tarih),
        boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
        boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
        buyukSAg: item.buyuk_s_ag,
        stokKodu: item.stok_kodu,
        stokAdi: item.stok_adi,
        hedefAgirlik: item.hedef_agirlik,
        uzunluk: item.uzunluk,
        yeniStokKodu: item.yeni_stok_kodu,
        saat: item.saat || new Date(item.tarih).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
      }));
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Ürün modellerini getiren metot
  static async fetchUrunler(): Promise<UrunModel[]> {
    try {
      const response = await fetch(`/api/urun-modelleri`);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();

      return data.map((item: any) => ({
        stokKodu: item.stok_kodu,
        stokAdi: item.stok_adi,
        uzunluk: item.uzunluk,
        hedefAgirlik: item.hedef_agirlik,
        toleransYuzde: item.tolerans_yuzde,
        aktif: item.aktif === 1
      }));
    } catch (error) {
      console.error('Ürün modelleri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Aktif üretim bilgisini getiren metot
  static async fetchAktifUretim() {
    try {
      const response = await fetch(`/api/aktif-uretim`);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Aktif üretim bilgisi alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata kaydı silen metot
  static async deleteSiloData(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata kaydı güncelleyen metot
  static async updateSiloData(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Yeni stok kodu kaydetme metodu
  static async saveStokKodu(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/save-stok-kodu`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }
}

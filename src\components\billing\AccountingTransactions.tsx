
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { EnhancedDataTable } from "./EnhancedDataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Demo data
const accountingTransactionsData = [
  { id: "1", date: "2024-07-10", description: "Satış Faturası Kaydı", journalNo: "Y2024-100", debitAccount: "120 - Al<PERSON><PERSON>ılar", creditAccount: "600 - Yurti<PERSON><PERSON> Satışlar", amount: 18500.00, status: "approved" },
  { id: "2", date: "2024-07-09", description: "Satış KDV Kaydı", journalNo: "Y2024-100", debitAccount: "120 - Alıcılar", creditAccount: "391 - Hesaplanan KDV", amount: 3330.00, status: "approved" },
  { id: "3", date: "2024-07-08", description: "<PERSON>ış Faturası Kaydı", journalNo: "Y2024-099", debitAccount: "153 - <PERSON><PERSON><PERSON> Mallar", creditAccount: "320 - Satıcılar", amount: 12500.00, status: "approved" },
  { id: "4", date: "2024-07-08", description: "Alış KDV Kaydı", journalNo: "Y2024-099", debitAccount: "191 - İndirilecek KDV", creditAccount: "320 - Satıcılar", amount: 2250.00, status: "approved" },
  { id: "5", date: "2024-07-07", description: "Maaş Ödemesi", journalNo: "Y2024-098", debitAccount: "770 - Genel Yönetim Giderleri", creditAccount: "102 - Bankalar", amount: 78500.00, status: "approved" },
  { id: "6", date: "2024-07-06", description: "Kira Ödemesi", journalNo: "Y2024-097", debitAccount: "770 - Genel Yönetim Giderleri", creditAccount: "102 - Bankalar", amount: 12500.00, status: "approved" },
  { id: "7", date: "2024-07-05", description: "Müşteri Tahsilatı", journalNo: "Y2024-096", debitAccount: "102 - Bankalar", creditAccount: "120 - Alıcılar", amount: 28500.00, status: "approved" },
  { id: "8", date: "2024-07-12", description: "Demirbaş Alımı", journalNo: "Y2024-101", debitAccount: "255 - Demirbaşlar", creditAccount: "102 - Bankalar", amount: 35000.00, status: "pending" }
];

// Column definitions
const accountingTransactionsColumns = [
  {
    header: "Tarih",
    accessorKey: "date",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Yevmiye No",
    accessorKey: "journalNo",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Açıklama",
    accessorKey: "description",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Borç Hesabı",
    accessorKey: "debitAccount",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Alacak Hesabı",
    accessorKey: "creditAccount",
    enableSorting: true,
    enableFiltering: true,
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    enableSorting: true,
    enableFiltering: true,
    cell: ({ row }) => {
      return <span className="font-medium">₺ {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "Durum",
    accessorKey: "status",
    enableSorting: true,
    enableFiltering: true,
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant: "default" | "success" | "warning" = "default";
      
      if (status === "approved") {
        badgeVariant = "success";
      } else if (status === "pending") {
        badgeVariant = "warning";
      }
      
      return <Badge variant={badgeVariant}>{status === "approved" ? "Onaylı" : "Bekleyen"}</Badge>;
    }
  }
];

export const AccountingTransactions = () => {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  
  // Filter data based on selected status
  const filteredData = statusFilter 
    ? accountingTransactionsData.filter(transaction => transaction.status === statusFilter)
    : accountingTransactionsData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={statusFilter === null ? "default" : "outline"}
            onClick={() => setStatusFilter(null)}
          >
            Tüm İşlemler
          </Button>
          <Button 
            variant={statusFilter === "pending" ? "default" : "outline"}
            onClick={() => setStatusFilter("pending")}
          >
            Bekleyen İşlemler
          </Button>
          <Button 
            variant={statusFilter === "approved" ? "default" : "outline"}
            onClick={() => setStatusFilter("approved")}
          >
            Onaylı İşlemler
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Muhasebe Kaydı
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Muhasebe İşlemleri</CardTitle>
        </CardHeader>
        <CardContent>
          <EnhancedDataTable 
            columns={accountingTransactionsColumns} 
            data={filteredData} 
            title="Muhasebe İşlemleri"
          />
        </CardContent>
      </Card>
    </div>
  );
};

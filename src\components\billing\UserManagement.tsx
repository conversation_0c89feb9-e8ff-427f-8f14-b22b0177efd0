
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, UserPlus, Shield, Activity, Clock, Settings } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export const UserManagement = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Users className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">Toplam Kullanıcı</p>
              <h3 className="text-2xl font-semibold">8</h3>
              <p className="text-sm text-muted-foreground">Aktif</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Shield className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-muted-foreground">Admin</p>
              <h3 className="text-2xl font-semibold">2</h3>
              <p className="text-sm text-muted-foreground">Tam Yetki</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Activity className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Çevrimiçi</p>
              <h3 className="text-2xl font-semibold">5</h3>
              <p className="text-sm text-success">Şu anda</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Clock className="h-8 w-8 text-amber-500" />
            <div>
              <p className="text-sm text-muted-foreground">Son İşlem</p>
              <h3 className="text-2xl font-semibold">12 dk</h3>
              <p className="text-sm text-muted-foreground">Önce</p>
            </div>
          </div>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Kullanıcı Yönetimi</CardTitle>
              <CardDescription>Kullanıcıları ve erişim izinlerini yönetin</CardDescription>
            </div>
            <Button className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Kullanıcı Ekle
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md divide-y">
            <div className="grid grid-cols-5 gap-4 p-3 bg-muted/50 font-medium text-sm">
              <div className="col-span-2">Kullanıcı</div>
              <div>Rol</div>
              <div>Durum</div>
              <div>İşlemler</div>
            </div>
            
            <div className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
              <div className="col-span-2 flex items-center gap-3">
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-blue-100 text-blue-600">AY</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">Ahmet Yılmaz</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div>
                <Badge variant="outline" className="flex items-center gap-1 w-fit">
                  <Shield className="h-3 w-3" /> Admin
                </Badge>
              </div>
              <div>
                <Badge className="bg-green-500 flex items-center gap-1 w-fit">
                  <Activity className="h-3 w-3" /> Çevrimiçi
                </Badge>
              </div>
              <div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
              <div className="col-span-2 flex items-center gap-3">
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-purple-100 text-purple-600">AD</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">Ayşe Demir</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div>
                <Badge variant="outline" className="flex items-center gap-1 w-fit">
                  <Shield className="h-3 w-3" /> Admin
                </Badge>
              </div>
              <div>
                <Badge className="bg-green-500 flex items-center gap-1 w-fit">
                  <Activity className="h-3 w-3" /> Çevrimiçi
                </Badge>
              </div>
              <div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
              <div className="col-span-2 flex items-center gap-3">
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-green-100 text-green-600">MK</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">Mehmet Kaya</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div>
                <Badge variant="outline" className="flex items-center gap-1 w-fit">
                  Muhasebeci
                </Badge>
              </div>
              <div>
                <Badge className="bg-green-500 flex items-center gap-1 w-fit">
                  <Activity className="h-3 w-3" /> Çevrimiçi
                </Badge>
              </div>
              <div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
              <div className="col-span-2 flex items-center gap-3">
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-amber-100 text-amber-600">ZŞ</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">Zeynep Şahin</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div>
                <Badge variant="outline" className="flex items-center gap-1 w-fit">
                  Satış Temsilcisi
                </Badge>
              </div>
              <div>
                <Badge className="bg-gray-500 flex items-center gap-1 w-fit">
                  Çevrimdışı
                </Badge>
              </div>
              <div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-5 gap-4 p-3 items-center hover:bg-muted/20">
              <div className="col-span-2 flex items-center gap-3">
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-red-100 text-red-600">CK</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">Can Kılıç</p>
                  <p className="text-xs text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div>
                <Badge variant="outline" className="flex items-center gap-1 w-fit">
                  Depo Sorumlusu
                </Badge>
              </div>
              <div>
                <Badge className="bg-green-500 flex items-center gap-1 w-fit">
                  <Activity className="h-3 w-3" /> Çevrimiçi
                </Badge>
              </div>
              <div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";

interface User {
  id: number;
  name: string;
  role: string;
  email: string;
  avatar: string;
  permissions: string[];
  companyId?: number;
}

interface Company {
  id: number;
  name: string;
  code: string;
  active: boolean;
  modules: string[];
  optionalModules: string[];
  type: string;
}

interface ModuleInfo {
  id: string;
  name: string;
  icon: any;
  type: string;
  color: string;
  bgColor: string;
  features?: string[];
}

interface UserPermissionsTabProps {
  users: User[];
  setUsers: (users: User[]) => void;
  companies: Company[];
  basicModules: ModuleInfo[];
  optionalModules: ModuleInfo[];
}

export const UserPermissionsTab = ({ 
  users, 
  setUsers, 
  companies, 
  basicModules, 
  optionalModules 
}: UserPermissionsTabProps) => {
  const { toast } = useToast();

  const getCompanyModules = (companyId: number) => {
    const company = companies.find(c => c.id === companyId);
    if (!company) return [];
    
    const basic = basicModules.filter(m => company.modules.includes(m.id));
    const optional = optionalModules.filter(m => company.optionalModules.includes(m.id));
    
    return [...basic, ...optional];
  };

  const updateUserPermissions = (userId: number, moduleId: string) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        const updatedPermissions = user.permissions.includes(moduleId)
          ? user.permissions.filter(p => p !== moduleId)
          : [...user.permissions, moduleId];
        
        return {...user, permissions: updatedPermissions};
      }
      return user;
    }));
    
    toast({
      title: "Kullanıcı yetkileri güncellendi",
      description: "Kullanıcı yetkileri başarıyla değiştirildi",
      variant: "default",
    });
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Kullanıcı Yetkileri</h3>
        <div className="flex gap-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700">Şirket Modülü</Badge>
          <Badge variant="outline" className="bg-green-50 text-green-700">Kullanıcı Yetkisi</Badge>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-4">
        {users.filter(u => u.role !== "Admin").map(user => {
          const userCompany = companies.find(c => c.id === user.companyId);
          const availableModules = getCompanyModules(user.companyId || 0);
          
          return (
            <Card key={user.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src="" />
                      <AvatarFallback className="bg-blue-100 text-blue-600">{user.avatar}</AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{user.name}</CardTitle>
                      <CardDescription>{user.email}</CardDescription>
                    </div>
                  </div>
                  <Badge>{user.role}</Badge>
                </div>
                <div className="mt-2">
                  <span className="text-sm font-medium">Firma: </span>
                  <span className="text-sm">{userCompany?.name || "Atanmamış"}</span>
                  {userCompany && (
                    <Badge variant="outline" className="ml-2 font-mono">
                      Kurum Kodu: {userCompany.code}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Kullanıcı Yetkileri</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Bu kullanıcı için şirketinize tanımlı modüllere erişim yetkisi verin
                  </p>
                  
                  {userCompany ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {availableModules.map(module => (
                        <div key={module.id} className="flex items-center justify-between p-3 rounded-md border">
                          <div className="flex items-center gap-2">
                            <div className={`p-2 rounded-md ${module.bgColor}`}>
                              <module.icon className={`h-5 w-5 ${module.color}`} />
                            </div>
                            <div>
                              <div className="font-medium">{module.name}</div>
                              <div className="text-xs text-muted-foreground">
                                <Badge variant="outline" className={`text-xs ${module.type === "Temel" ? "bg-blue-50 text-blue-700" : "bg-amber-50 text-amber-700"}`}>
                                  {module.type}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <Switch 
                            checked={user.permissions.includes(module.id)}
                            onCheckedChange={() => updateUserPermissions(user.id, module.id)}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 border rounded-md text-center text-muted-foreground">
                      Bu kullanıcıya henüz bir firma atanmamış
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </>
  );
};

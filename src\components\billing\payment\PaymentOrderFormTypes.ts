
import { z } from "zod";

// Form schema validation
export const paymentOrderSchema = z.object({
  requestingUser: z.string().min(2, { message: "Kullanıcı adı gereklidir" }),
  date: z.date({ required_error: "<PERSON>rih seçilmelidir" }),
  details: z.string().min(10, { message: "En az 10 karakter girmelisiniz" }),
  priority: z.enum(["high", "medium", "low"], { required_error: "Öncelik seçilmelidir" }),
  sourceType: z.enum(["nakit", "avans", "diğer"], { required_error: "Kaynak türü seçilmelidir" }),
  documentNumber: z.string().optional(),
  amount: z.string().min(1, "Tutar gereklidir"),
  dueDate: z.date({ required_error: "Vade tarihi seçilmelidir" }),
});

export type PaymentOrderFormValues = z.infer<typeof paymentOrderSchema>;

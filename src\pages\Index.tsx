
import { Card } from "@/components/ui/card";
import { Users, Package2, CircleDollarSign, TrendingUp, UserPlus, Calendar, BanknoteIcon, FileText, Building, Wallet, Receipt } from "lucide-react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const stats = [
  {
    title: "Kasa Bakiyesi",
    value: "₺25,400",
    icon: Wallet,
    trend: "+3.2%",
    color: "text-green-500",
    bgColor: "bg-green-100 dark:bg-green-900/30"
  },
  {
    title: "Toplam Alacak",
    value: "₺145,300",
    icon: CircleDollarSign,
    trend: "+8.1%",
    color: "text-blue-500",
    bgColor: "bg-blue-100 dark:bg-blue-900/30"
  },
  {
    title: "Toplam Borç",
    value: "₺98,700",
    icon: BanknoteIcon,
    trend: "-2.5%",
    color: "text-red-500",
    bgColor: "bg-red-100 dark:bg-red-900/30"
  },
  {
    title: "<PERSON><PERSON><PERSON> Yaklaşan Çek/Senet",
    value: "12",
    icon: Receipt,
    trend: "+2 yeni",
    color: "text-amber-500",
    bgColor: "bg-amber-100 dark:bg-amber-900/30"
  },
];

const Index = () => {
  const navigate = useNavigate();
  
  return (
    <MainLayout>
      <div className="space-y-8">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Hoş Geldiniz</h2>
          <p className="text-muted-foreground">
            İşletmenizin genel durumunu buradan takip edebilirsiniz.
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title} className="hover-card p-6">
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <h3 className="text-2xl font-semibold">{stat.value}</h3>
                  <p className={`text-sm ${stat.trend.startsWith('+') ? 'text-success' : 'text-destructive'}`}>{stat.trend}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Son İşlemler</h3>
            <div className="space-y-4">
              <div className="rounded-md bg-muted p-3">
                <div className="flex justify-between">
                  <div className="font-medium">Fatura #42561</div>
                  <div className="text-green-500">₺4,250.00</div>
                </div>
                <div className="text-sm text-muted-foreground">Ahmet Yılmaz Ltd. - 05.04.2025</div>
              </div>
              <div className="rounded-md bg-muted p-3">
                <div className="flex justify-between">
                  <div className="font-medium">Ödeme #18345</div>
                  <div className="text-red-500">₺2,780.00</div>
                </div>
                <div className="text-sm text-muted-foreground">Tedarikçi Elektronik A.Ş. - 03.04.2025</div>
              </div>
              <div className="rounded-md bg-muted p-3">
                <div className="flex justify-between">
                  <div className="font-medium">Çek Tahsilat</div>
                  <div className="text-green-500">₺5,400.00</div>
                </div>
                <div className="text-sm text-muted-foreground">Yıldız İnşaat Ltd. - 02.04.2025</div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Hızlı Erişim</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-24 p-2 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-all" 
                  onClick={() => navigate("/finans/cari")}
                >
                  <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-2">
                    <Users className="h-6 w-6 text-blue-500" />
                  </div>
                  <span>Cari Hesap Yönetimi</span>
                </Button>
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-24 p-2 hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-all"
                  onClick={() => navigate("/finans/faturalar")}
                >
                  <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-2">
                    <FileText className="h-6 w-6 text-purple-500" />
                  </div>
                  <span>Fatura Yönetimi</span>
                </Button>
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-24 p-2 hover:bg-green-50 dark:hover:bg-green-900/30 transition-all"
                  onClick={() => navigate("/finans/nakit")}
                >
                  <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 mb-2">
                    <Wallet className="h-6 w-6 text-green-500" />
                  </div>
                  <span>Kasa İşlemleri</span>
                </Button>
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-24 p-2 hover:bg-amber-50 dark:hover:bg-amber-900/30 transition-all"
                  onClick={() => navigate("/finans/cek-senet")}
                >
                  <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30 mb-2">
                    <Receipt className="h-6 w-6 text-amber-500" />
                  </div>
                  <span>Çek/Senet Takibi</span>
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Index;

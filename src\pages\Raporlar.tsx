
import { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { ReportsDashboard } from "@/components/reports/ReportsDashboard";
import { ReportsHeader } from "@/components/reports/ReportsHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Users, Package, TrendingUp, BarChart3, Calendar, Settings, PieChart } from "lucide-react";

const Reports = () => {
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Panel</h2>
            <p className="text-muted-foreground">
              Firma performansını ve metriklerini buradan izleyebilirsiniz
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
              Genel Bakış
            </Badge>
            <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-100">
              Finansal
            </Badge>
            <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-100">
              Üretim
            </Badge>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-muted/50">
            <TabsTrigger value="dashboard">Genel Bakış</TabsTrigger>
            <TabsTrigger value="reports">Raporlar</TabsTrigger>
            <TabsTrigger value="analytics">Analiz</TabsTrigger>
            <TabsTrigger value="settings">Ayarlar</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="hover-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Toplam Satış</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">₺124,750</div>
                    <FileText className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <div>Aylık Hedef</div>
                      <div className="font-medium">₺150,000</div>
                    </div>
                    <Progress value={82} className="h-2" />
                    <div className="mt-1 text-xs text-right text-green-600">
                      %14 artış
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="hover-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Aktif Müşteriler</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">875</div>
                    <Users className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <div>Yeni Müşteriler</div>
                      <div className="font-medium">42</div>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="mt-1 text-xs text-right text-green-600">
                      %8 artış
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="hover-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Stok Durumu</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">2,341</div>
                    <Package className="h-8 w-8 text-amber-500" />
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <div>Kritik Ürünler</div>
                      <div className="font-medium">15</div>
                    </div>
                    <Progress value={12} className="h-2" />
                    <div className="mt-1 text-xs text-right text-amber-600">
                      Takip altında
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="hover-card">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Büyüme Oranı</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">%18.2</div>
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <div>Geçen Yıla Göre</div>
                      <div className="font-medium">%12.5</div>
                    </div>
                    <Progress value={88} className="h-2" />
                    <div className="mt-1 text-xs text-right text-green-600">
                      %5.7 artış
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="col-span-1 hover-card">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Satış Performansı</CardTitle>
                    <Button variant="outline" size="sm" className="h-8">
                      <Calendar className="h-4 w-4 mr-2" />
                      Bu Ay
                    </Button>
                  </div>
                  <CardDescription>Aylık satış performansı analizi</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px] flex items-center justify-center text-muted-foreground">
                    <BarChart3 className="h-16 w-16" />
                    <span className="ml-2">Grafik gösterimi</span>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="col-span-1 hover-card">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Ürün Kategorileri</CardTitle>
                    <Button variant="outline" size="sm" className="h-8">
                      <Settings className="h-4 w-4 mr-2" />
                      Filtrele
                    </Button>
                  </div>
                  <CardDescription>Kategorilere göre dağılım</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px] flex items-center justify-center text-muted-foreground">
                    <PieChart className="h-16 w-16" />
                    <span className="ml-2">Grafik gösterimi</span>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card className="hover-card">
              <CardHeader>
                <CardTitle>Aktif Projeler</CardTitle>
                <CardDescription>Devam eden projeler ve durumları</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {["Muhasebe Modülü Entegrasyonu", "Mobil Uygulama Geliştirme", "Veri Tabanı Optimizasyonu", "UI/UX İyileştirmeleri"].map((project, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="font-medium">{project}</div>
                        <div className="text-sm text-muted-foreground">
                          {["Tasarım", "Geliştirme", "Test", "Analiz"][index]} aşamasında
                        </div>
                      </div>
                      <div>
                        <Progress value={[75, 45, 90, 30][index]} className="h-2 w-32" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="reports">
            <ReportsDashboard />
          </TabsContent>
          
          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>İleri Düzey Analizler</CardTitle>
                <CardDescription>Bu özellik henüz geliştirme aşamasındadır.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Analiz modülü yakında kullanıma sunulacaktır.</p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Rapor Ayarları</CardTitle>
                <CardDescription>Raporlama tercihlerinizi ve görünüm ayarlarınızı düzenleyin.</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Ayarlar modülü yakında kullanıma sunulacaktır.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Reports;


import express from 'express';
import { query, testConnection, pool } from '../../utils/db';

const router = express.Router();

// Test database connection and return information
router.get('/connection', async (req, res) => {
  try {
    const connected = await testConnection();
    
    if (connected) {
      // Get database server information
      const dbInfo = await query('SELECT version() as version, database() as database_name, user() as user');
      
      // Get table list
      const tables = await query('SHOW TABLES');
      
      // Return detailed information
      res.json({
        status: 'connected',
        message: 'Veritabanı bağlantısı başarılı',
        db_info: dbInfo[0],
        tables: Array.isArray(tables) ? tables.map((table: any) => Object.values(table)[0]) : [],
        connection_info: {
          host: process.env.DB_HOST,
          database: process.env.DB_NAME,
          user: process.env.DB_USER,
          port: process.env.DB_PORT
        },
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        status: 'disconnected',
        message: 'Veritabanı bağlantısı başarısız',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Test bağlantı hatası:', error);
    res.status(500).json({
      status: 'error',
      message: 'Veritabanı test hatası',
      error: String(error),
      timestamp: new Date().toISOString()
    });
  }
});

// Test query execution
router.get('/query', async (req, res) => {
  try {
    // Run a simple test query
    const result = await query('SELECT COUNT(*) as cari_count FROM cari_hesaplar');
    const productCount = await query('SELECT COUNT(*) as urun_count FROM urunler');
    const invoiceCount = await query('SELECT COUNT(*) as fatura_count FROM faturalar');
    
    res.json({
      status: 'success',
      data: {
        cari_hesaplar: result[0].cari_count,
        urunler: productCount[0].urun_count,
        faturalar: invoiceCount[0].fatura_count
      },
      message: 'Test sorgusu başarıyla çalıştırıldı',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test sorgu hatası:', error);
    res.status(500).json({
      status: 'error',
      message: 'Test sorgu hatası',
      error: String(error),
      timestamp: new Date().toISOString()
    });
  }
});

// Get pool statistics
router.get('/pool', async (req, res) => {
  try {
    // @ts-ignore - accessing private property for diagnostics
    const poolStats = pool.pool ? {
      // @ts-ignore - accessing private property for diagnostics
      connectionLimit: pool.pool.config.connectionLimit,
      // @ts-ignore - accessing private property for diagnostics
      queueLimit: pool.pool.config.queueLimit,
      // @ts-ignore - accessing private property for diagnostics
      acquireTimeout: pool.pool.config.acquireTimeout,
      // @ts-ignore - accessing private property for diagnostics
      waitForConnections: pool.pool.config.waitForConnections
    } : 'Pool statistics not available';
    
    res.json({
      status: 'success',
      pool_stats: poolStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Pool stats error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Havuz istatistikleri alınamadı',
      error: String(error),
      timestamp: new Date().toISOString()
    });
  }
});

export default router;

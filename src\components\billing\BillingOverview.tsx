
import React, { useState, useEffect } from 'react';
import { DataTable } from './DataTable';
import { invoiceColumns } from './columns';
import { useBillingStore } from '@/stores/billingStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Search, Send } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

export const BillingOverview = () => {
  const { invoices } = useBillingStore();
  const [searchValue, setSearchValue] = useState('');
  const [filteredInvoices, setFilteredInvoices] = useState(invoices);

  // Filter invoices when search value changes
  useEffect(() => {
    if (searchValue.trim() === '') {
      setFilteredInvoices(invoices);
    } else {
      const filtered = invoices.filter(invoice => 
        invoice.number.toLowerCase().includes(searchValue.toLowerCase()) ||
        invoice.customerId.toLowerCase().includes(searchValue.toLowerCase())
      );
      setFilteredInvoices(filtered);
    }
  }, [searchValue, invoices]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  const handleSendInvoices = () => {
    toast({
      title: "Faturalar Gönderildi",
      description: "Seçili faturalar başarıyla müşterilere gönderildi.",
    });
  };

  const handleDownloadInvoices = () => {
    toast({
      title: "Faturalar İndirildi",
      description: "Seçili faturalar başarıyla indirildi.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-2">
        <h3 className="text-xl font-medium">Fatura Listesi</h3>
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Fatura veya müşteri ara..."
              value={searchValue}
              onChange={handleSearch}
              className="pl-8 w-full sm:w-[250px]"
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleSendInvoices} title="Faturaları Gönder">
              <Send className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleDownloadInvoices} title="Faturaları İndir">
              <Download className="h-4 w-4" />
            </Button>
            {/* Removed CreateInvoiceButton here */}
          </div>
        </div>
      </div>
      
      <DataTable columns={invoiceColumns} data={filteredInvoices} />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Fatura</CardTitle>
            <CardDescription>Tüm faturalar</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invoices.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Gelir</CardTitle>
            <CardDescription>Tüm faturalardan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ₺{invoices.reduce((sum, inv) => sum + inv.total, 0).toLocaleString('tr-TR')}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Bekleyen Ödeme</CardTitle>
            <CardDescription>Tahsil edilmemiş</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              ₺{invoices
                .filter(inv => inv.status === 'pending')
                .reduce((sum, inv) => sum + inv.total, 0)
                .toLocaleString('tr-TR')}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

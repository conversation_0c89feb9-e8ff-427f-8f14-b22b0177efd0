
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Printer, Send, Save, Download } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ReceiptData {
  number: string;
  date: string;
  client: string;
  clientTaxId: string;
  clientAddress: string;
  description: string;
  amount: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
}

export const FreelanceReceipt = () => {
  const { toast } = useToast();
  
  const [receiptData, setReceiptData] = useState<ReceiptData>({
    number: `SMM-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    date: new Date().toISOString().split('T')[0],
    client: "",
    clientTaxId: "",
    clientAddress: "",
    description: "",
    amount: 0,
    taxRate: 20, // Default tax rate for freelancers in Turkey
    taxAmount: 0,
    totalAmount: 0,
  });
  
  const calculateTotals = (amount: number, taxRate: number) => {
    const taxAmount = (amount * taxRate) / 100;
    const totalAmount = amount;
    
    return { taxAmount, totalAmount };
  };
  
  const handleAmountChange = (amount: string) => {
    const numericAmount = parseFloat(amount) || 0;
    const { taxAmount, totalAmount } = calculateTotals(numericAmount, receiptData.taxRate);
    
    setReceiptData({
      ...receiptData,
      amount: numericAmount,
      taxAmount,
      totalAmount,
    });
  };
  
  const handleTaxRateChange = (taxRate: string) => {
    const numericTaxRate = parseInt(taxRate);
    const { taxAmount, totalAmount } = calculateTotals(receiptData.amount, numericTaxRate);
    
    setReceiptData({
      ...receiptData,
      taxRate: numericTaxRate,
      taxAmount,
      totalAmount,
    });
  };
  
  const saveReceipt = () => {
    // In a real app, this would save to a database
    toast({
      title: "Makbuz Kaydedildi",
      description: "Serbest meslek makbuzu başarıyla kaydedildi.",
    });
  };
  
  const sendReceipt = () => {
    toast({
      title: "Makbuz Gönderiliyor",
      description: "Serbest meslek makbuzu müşteriye gönderiliyor.",
    });
    
    // Simulate sending with a success message
    setTimeout(() => {
      toast({
        title: "Makbuz Gönderildi",
        description: "Makbuz başarıyla müşteriye gönderildi.",
      });
    }, 1500);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Serbest Meslek Makbuzu</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="receiptNumber">Makbuz No</Label>
                <Input 
                  id="receiptNumber" 
                  value={receiptData.number}
                  onChange={(e) => setReceiptData({...receiptData, number: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="receiptDate">Tarih</Label>
                <Input 
                  id="receiptDate" 
                  type="date"
                  value={receiptData.date}
                  onChange={(e) => setReceiptData({...receiptData, date: e.target.value})}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="client">Müşteri Adı/Unvanı</Label>
              <Input 
                id="client" 
                value={receiptData.client}
                onChange={(e) => setReceiptData({...receiptData, client: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="clientTaxId">VKN/TCKN</Label>
              <Input 
                id="clientTaxId" 
                value={receiptData.clientTaxId}
                onChange={(e) => setReceiptData({...receiptData, clientTaxId: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="clientAddress">Müşteri Adresi</Label>
              <Textarea 
                id="clientAddress" 
                rows={3}
                value={receiptData.clientAddress}
                onChange={(e) => setReceiptData({...receiptData, clientAddress: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="description">Hizmet Açıklaması</Label>
              <Textarea 
                id="description" 
                rows={4}
                value={receiptData.description}
                onChange={(e) => setReceiptData({...receiptData, description: e.target.value})}
                placeholder="Sunulan hizmetin detaylı açıklaması..."
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amount">Tutar (₺)</Label>
                <Input 
                  id="amount" 
                  type="number"
                  min="0"
                  step="0.01"
                  value={receiptData.amount || ""}
                  onChange={(e) => handleAmountChange(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="taxRate">Stopaj Oranı (%)</Label>
                <Select 
                  value={String(receiptData.taxRate)} 
                  onValueChange={handleTaxRateChange}
                >
                  <SelectTrigger id="taxRate">
                    <SelectValue placeholder="Seçiniz" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">%0</SelectItem>
                    <SelectItem value="10">%10</SelectItem>
                    <SelectItem value="15">%15</SelectItem>
                    <SelectItem value="17">%17</SelectItem>
                    <SelectItem value="20">%20</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="bg-gray-100 p-6 rounded-lg">
                <div className="text-center font-bold text-xl mb-6">
                  SERBEST MESLEK MAKBUZU
                </div>
                
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">Makbuz No:</span>
                    <span>{receiptData.number}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">Tarih:</span>
                    <span>{receiptData.date ? new Date(receiptData.date).toLocaleDateString('tr-TR') : ''}</span>
                  </div>
                  
                  <div className="border-t pt-2 mt-2">
                    <div className="text-sm">
                      <span className="font-medium">Müşteri:</span>
                      <p>{receiptData.client}</p>
                    </div>
                    
                    {receiptData.clientTaxId && (
                      <div className="text-sm mt-1">
                        <span className="font-medium">VKN/TCKN:</span>
                        <p>{receiptData.clientTaxId}</p>
                      </div>
                    )}
                    
                    {receiptData.clientAddress && (
                      <div className="text-sm mt-1">
                        <span className="font-medium">Adres:</span>
                        <p className="whitespace-pre-line">{receiptData.clientAddress}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="border-t pt-2 mt-2">
                    <div className="text-sm">
                      <span className="font-medium">Hizmet Açıklaması:</span>
                      <p className="whitespace-pre-line">{receiptData.description || "..."}</p>
                    </div>
                  </div>
                  
                  <div className="border-t pt-2 mt-2">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">Brüt Tutar:</span>
                      <span>₺{receiptData.amount.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm mt-1">
                      <span className="font-medium">Stopaj (%{receiptData.taxRate}):</span>
                      <span>₺{receiptData.taxAmount.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                    </div>
                    
                    <div className="flex justify-between font-bold text-base mt-2">
                      <span>Net Tutar:</span>
                      <span>₺{(receiptData.amount - receiptData.taxAmount).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-between">
              <div className="space-x-2">
                <Button variant="outline" className="gap-2">
                  <Printer className="h-4 w-4" />
                  Yazdır
                </Button>
                <Button variant="outline" className="gap-2">
                  <Download className="h-4 w-4" />
                  İndir (PDF)
                </Button>
              </div>
              
              <div className="space-x-2">
                <Button variant="outline" onClick={saveReceipt}>
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button onClick={sendReceipt}>
                  <Send className="h-4 w-4 mr-2" />
                  Gönder
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

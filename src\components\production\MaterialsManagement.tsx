
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, PlusCircle, RefreshCw } from "lucide-react";

export const MaterialsManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium">Malzeme Yönetimi</h3>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Stok Güncelle
          </Button>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Yeni Malzeme
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Malzeme Durumu</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative overflow-x-auto rounded-md border">
            <table className="w-full text-sm">
              <thead className="bg-muted text-muted-foreground">
                <tr>
                  <th className="px-4 py-3 text-left">Malzeme Kodu</th>
                  <th className="px-4 py-3 text-left">Malzeme Adı</th>
                  <th className="px-4 py-3 text-left">Stok Miktarı</th>
                  <th className="px-4 py-3 text-left">Birim</th>
                  <th className="px-4 py-3 text-left">Kritik Seviye</th>
                  <th className="px-4 py-3 text-left">Durum</th>
                  <th className="px-4 py-3 text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">MAT-1001</td>
                  <td className="px-4 py-3">Metal Profil 30x30</td>
                  <td className="px-4 py-3">850</td>
                  <td className="px-4 py-3">metre</td>
                  <td className="px-4 py-3">500</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      Yeterli
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Sipariş Ver</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50 bg-red-50">
                  <td className="px-4 py-3 font-medium">MAT-1002</td>
                  <td className="px-4 py-3">Cam Panel 50x50</td>
                  <td className="px-4 py-3">25</td>
                  <td className="px-4 py-3">adet</td>
                  <td className="px-4 py-3">100</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Kritik Seviye Altında
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm" className="text-red-600">Acil Sipariş</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">MAT-1003</td>
                  <td className="px-4 py-3">Ahşap Kaplama</td>
                  <td className="px-4 py-3">1200</td>
                  <td className="px-4 py-3">m²</td>
                  <td className="px-4 py-3">500</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      Yeterli
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Sipariş Ver</Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

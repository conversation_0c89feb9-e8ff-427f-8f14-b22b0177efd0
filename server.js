import express from 'express';
import mysql from 'mysql2';
import cors from 'cors';
import https from 'https';
import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// CORS ayarlarını güncelle
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204
}));

// HTTPS başlıkları
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        return res.sendStatus(204);
    }
    next();
});

app.use(express.json());

// MySQL bağlantı havuzu oluştur
const pool = mysql.createPool({
    host: '*************',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'borudata',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    timezone: '+03:00' // İstanbul zaman dilimi (UTC+3)
});

// Aktif ürün modellerini getir
app.get('/api/urun-modelleri', (req, res) => {
    const query = `
    SELECT stok_kodu, stok_adi, uzunluk, hedef_agirlik 
    FROM urun_modelleri 
    WHERE aktif = 1 
    ORDER BY stok_adi ASC
  `;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Silodata verilerini getir - Tarih formatı düzeltildi
app.get('/api/getSilodata', (req, res) => {
    const dateFilter = req.query.date;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (dateFilter) {
        query += ` AND DATE(s.tarih) = ?`;
        queryParams.push(dateFilter);
    } else if (startDate && endDate) {
        query += ` AND DATE(s.tarih) >= ? AND DATE(s.tarih) <= ?`;
        queryParams.push(startDate, endDate);
    }

    query += ` ORDER BY s.tarih DESC`;

    console.log('Sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        // Artık timezone ayarı pool üzerinde olduğundan saatleri doğrudan gönderiyoruz
        res.json(results);
    });
});

// Üretim Hattı 2 - Silodata2 verilerini getir
app.get('/api/getSilodata2', (req, res) => {
    const dateFilter = req.query.date;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata2 s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (dateFilter) {
        query += ` AND DATE(s.tarih) = ?`;
        queryParams.push(dateFilter);
    } else if (startDate && endDate) {
        query += ` AND DATE(s.tarih) >= ? AND DATE(s.tarih) <= ?`;
        queryParams.push(startDate, endDate);
    }

    query += ` ORDER BY s.tarih DESC`;

    console.log('Üretim Hattı 2 - Sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        // Artık timezone ayarı pool üzerinde olduğundan saatleri doğrudan gönderiyoruz
        res.json(results);
    });
});

// Üretim Hattı 3 - Silodata3 verilerini getir
app.get('/api/getSilodata3', (req, res) => {
    const dateFilter = req.query.date;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata3 s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (dateFilter) {
        query += ` AND DATE(s.tarih) = ?`;
        queryParams.push(dateFilter);
    } else if (startDate && endDate) {
        query += ` AND DATE(s.tarih) >= ? AND DATE(s.tarih) <= ?`;
        queryParams.push(startDate, endDate);
    }

    query += ` ORDER BY s.tarih DESC`;

    console.log('Üretim Hattı 3 - Sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        // Artık timezone ayarı pool üzerinde olduğundan saatleri doğrudan gönderiyoruz
        res.json(results);
    });
});

// Aktif üretim stok adını getir
app.get('/api/aktif-uretim-sql', (req, res) => {
    const query = `
    SELECT
      um.stok_kodu AS stok_kodu,
      um.stok_adi AS stok_adi
    FROM
      urun_modelleri um
    WHERE
      um.stok_kodu = (
        SELECT
          s.stok_kodu
        FROM
          silodata s
        WHERE
          s.stok_kodu IS NOT NULL
        ORDER BY
          s.ID DESC
        LIMIT 1
      );
  `;

    console.log('Aktif üretim sorgusu çalıştırılıyor');

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        console.log('Aktif üretim sonuçları:', results);
        res.json(results);
    });
});

// Aktif üretim bilgisini getir
app.get('/api/aktif-uretim', (req, res) => {
    const query = `SELECT * FROM aktif_uretim`;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ürün model detayını getir
app.get('/api/urun-model-detay', (req, res) => {
    const stokKodu = req.query.stok_kodu;
    const query = `
    SELECT hedef_agirlik, tolerans_yuzde 
    FROM urun_modelleri 
    WHERE stok_kodu = ?
  `;

    pool.query(query, [stokKodu], (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Silodata'ya kayıt ekle
app.post('/api/silodata', (req, res) => {
    const { stok_kodu, BoruNo, tarih } = req.body;

    // Tarih formatını MySQL için düzenle
    const formattedDate = new Date(tarih).toISOString().slice(0, 19).replace('T', ' ');

    // Önce stok kodunun geçerli olup olmadığını kontrol et
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stok_kodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }

            // Önce son ID'yi al
            pool.query('SELECT MAX(id) as maxId FROM silodata', (error, results) => {
                if (error) {
                    console.error('ID sorgulama hatası:', error.message);
                    res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                    return;
                }

                const maxId = results[0].maxId;

                // Sonra güncelleme yap
                const updateQuery = `
          UPDATE silodata 
          SET yeni_stok_kodu = ? 
          WHERE id = ?
        `;

                pool.query(
                    updateQuery,
                    [stok_kodu, maxId],
                    (error, results) => {
                        if (error) {
                            console.error('Güncelleme hatası:', error.message);
                            res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                            return;
                        }

                        if (results.affectedRows === 0) {
                            console.error('Güncelleme yapılmadı, muhtemelen kayıt yok.');
                            res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                            return;
                        }

                        res.json({
                            message: 'Kayıt güncellendi',
                            stok_kodu,
                            BoruNo
                        });
                    }
                );
            });
        }
    );
});

// Silodata'dan kayıt silme
app.delete('/api/silodata/:id', (req, res) => {
    const id = req.params.id;

    pool.query(
        'DELETE FROM silodata WHERE ID = ?',
        [id],
        (error, results) => {
            if (error) {
                console.error('Silme hatası:', error.message);
                res.status(500).json({ error: 'Silme sırasında hata oluştu', details: error });
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).json({ error: 'Silinecek kayıt bulunamadı.' });
                return;
            }

            res.json({
                message: 'Kayıt silindi',
                id
            });
        }
    );
});

// Silodata kaydı güncelleme
app.put('/api/silodata/:id', (req, res) => {
    const id = req.params.id;
    const { stokKodu } = req.body;

    // Stok kodunun geçerli olup olmadığını kontrol et
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stokKodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }

            // Silodata güncelleme
            const updateQuery = `
        UPDATE silodata 
        SET stok_kodu = ? 
        WHERE ID = ?
      `;

            pool.query(
                updateQuery,
                [stokKodu, id],
                (error, results) => {
                    if (error) {
                        console.error('Güncelleme hatası:', error.message);
                        res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                        return;
                    }

                    if (results.affectedRows === 0) {
                        res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                        return;
                    }

                    res.json({
                        message: 'Kayıt güncellendi',
                        id,
                        stokKodu
                    });
                }
            );
        }
    );
});

// ID'ye göre silodata kaydını getirme - sonra tanımlanıyor
app.get('/api/silodata/:id', (req, res) => {
    const id = req.params.id;
    console.log(`Ürün bilgisi isteği alındı - ID: ${id}`);

    pool.query(
        'SELECT s.*, um.stok_adi FROM silodata s LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu WHERE s.ID = ?',
        [id],
        (error, results) => {
            if (error) {
                console.error('Veri getirme hatası:', error.message);
                res.status(500).json({ error: 'Veri getirme sırasında hata oluştu', details: error });
                return;
            }

            if (results.length === 0) {
                console.error(`Ürün bulunamadı - ID: ${id}`);
                res.status(404).json({ error: 'Kayıt bulunamadı' });
                return;
            }

            console.log(`Ürün bilgisi başarıyla getirildi - ID: ${id}`);
            res.json(results[0]);
        }
    );
});

// Silodata2 kaydı silme
app.delete('/api/silodata2/:id', (req, res) => {
    const id = req.params.id;

    pool.query('DELETE FROM silodata2 WHERE id = ?', [id], (error, results) => {
        if (error) {
            console.error('Silodata2 silme hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        if (results.affectedRows === 0) {
            res.status(404).json({ error: 'Kayıt bulunamadı' });
            return;
        }

        res.json({ success: true, message: 'Kayıt başarıyla silindi' });
    });
});

// Silodata2 kaydı güncelleme
app.put('/api/silodata2/:id', (req, res) => {
    const id = req.params.id;
    const { stokKodu } = req.body;

    if (!stokKodu) {
        res.status(400).json({ error: 'Stok kodu belirtilmedi' });
        return;
    }

    pool.query(
        'UPDATE silodata2 SET stok_kodu = ? WHERE id = ?',
        [stokKodu, id],
        (error, results) => {
            if (error) {
                console.error('Silodata2 güncelleme hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).json({ error: 'Kayıt bulunamadı' });
                return;
            }

            res.json({ success: true, message: 'Kayıt başarıyla güncellendi' });
        }
    );
});

// Silodata3 kaydı silme
app.delete('/api/silodata3/:id', (req, res) => {
    const id = req.params.id;

    pool.query('DELETE FROM silodata3 WHERE id = ?', [id], (error, results) => {
        if (error) {
            console.error('Silodata3 silme hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        if (results.affectedRows === 0) {
            res.status(404).json({ error: 'Kayıt bulunamadı' });
            return;
        }

        res.json({ success: true, message: 'Kayıt başarıyla silindi' });
    });
});

// Silodata3 kaydı güncelleme
app.put('/api/silodata3/:id', (req, res) => {
    const id = req.params.id;
    const { stokKodu } = req.body;

    if (!stokKodu) {
        res.status(400).json({ error: 'Stok kodu belirtilmedi' });
        return;
    }

    pool.query(
        'UPDATE silodata3 SET stok_kodu = ? WHERE id = ?',
        [stokKodu, id],
        (error, results) => {
            if (error) {
                console.error('Silodata3 güncelleme hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).json({ error: 'Kayıt bulunamadı' });
                return;
            }

            res.json({ success: true, message: 'Kayıt başarıyla güncellendi' });
        }
    );
});

// Silodata2'ye kayıt ekle
app.post('/api/silodata2', (req, res) => {
    const { stok_kodu, BoruNo, tarih } = req.body;
    const formattedDate = new Date(tarih).toISOString().slice(0, 19).replace('T', ' ');
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stok_kodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }
            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }
            pool.query('SELECT MAX(id) as maxId FROM silodata2', (error, results) => {
                if (error) {
                    console.error('ID sorgulama hatası:', error.message);
                    res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                    return;
                }
                const maxId = results[0].maxId;
                const updateQuery = `
          UPDATE silodata2 
          SET yeni_stok_kodu = ? 
          WHERE id = ?
        `;
                pool.query(
                    updateQuery,
                    [stok_kodu, maxId],
                    (error, results) => {
                        if (error) {
                            console.error('Güncelleme hatası:', error.message);
                            res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                            return;
                        }
                        if (results.affectedRows === 0) {
                            console.error('Güncelleme yapılmadı, muhtemelen kayıt yok.');
                            res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                            return;
                        }
                        res.json({
                            message: 'Kayıt güncellendi',
                            stok_kodu,
                            BoruNo
                        });
                    }
                );
            });
        }
    );
});

// Silodata3'ye kayıt ekle
app.post('/api/silodata3', (req, res) => {
    const { stok_kodu, BoruNo, tarih } = req.body;
    const formattedDate = new Date(tarih).toISOString().slice(0, 19).replace('T', ' ');
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stok_kodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }
            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }
            pool.query('SELECT MAX(id) as maxId FROM silodata3', (error, results) => {
                if (error) {
                    console.error('ID sorgulama hatası:', error.message);
                    res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                    return;
                }
                const maxId = results[0].maxId;
                const updateQuery = `
          UPDATE silodata3 
          SET yeni_stok_kodu = ? 
          WHERE id = ?
        `;
                pool.query(
                    updateQuery,
                    [stok_kodu, maxId],
                    (error, results) => {
                        if (error) {
                            console.error('Güncelleme hatası:', error.message);
                            res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                            return;
                        }
                        if (results.affectedRows === 0) {
                            console.error('Güncelleme yapılmadı, muhtemelen kayıt yok.');
                            res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                            return;
                        }
                        res.json({
                            message: 'Kayıt güncellendi',
                            stok_kodu,
                            BoruNo
                        });
                    }
                );
            });
        }
    );
});

// Üretim Hattı 4 - Silodata4 verilerini getir
app.get('/api/getSilodata4', (req, res) => {
    const dateFilter = req.query.date;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata4 s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (dateFilter) {
        query += ` AND DATE(s.tarih) = ?`;
        queryParams.push(dateFilter);
    } else if (startDate && endDate) {
        query += ` AND DATE(s.tarih) >= ? AND DATE(s.tarih) <= ?`;
        queryParams.push(startDate, endDate);
    }

    query += ` ORDER BY s.tarih DESC`;

    console.log('Üretim Hattı 4 - Sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        // Artık timezone ayarı pool üzerinde olduğundan saatleri doğrudan gönderiyoruz
        res.json(results);
    });
});

// Üretim Hattı 5 - Silodata5 verilerini getir
app.get('/api/getSilodata5', (req, res) => {
    const dateFilter = req.query.date;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata5 s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (dateFilter) {
        query += ` AND DATE(s.tarih) = ?`;
        queryParams.push(dateFilter);
    } else if (startDate && endDate) {
        query += ` AND DATE(s.tarih) >= ? AND DATE(s.tarih) <= ?`;
        queryParams.push(startDate, endDate);
    }

    query += ` ORDER BY s.tarih DESC`;

    console.log('Üretim Hattı 5 - Sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        // Artık timezone ayarı pool üzerinde olduğundan saatleri doğrudan gönderiyoruz
        res.json(results);
    });
});

// Silodata4 kaydı silme
app.delete('/api/silodata4/:id', (req, res) => {
    const id = req.params.id;

    pool.query('DELETE FROM silodata4 WHERE id = ?', [id], (error, results) => {
        if (error) {
            console.error('Silodata4 silme hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        if (results.affectedRows === 0) {
            res.status(404).json({ error: 'Kayıt bulunamadı' });
            return;
        }

        res.json({ success: true, message: 'Kayıt başarıyla silindi' });
    });
});

// Silodata4 kaydı güncelleme
app.put('/api/silodata4/:id', (req, res) => {
    const id = req.params.id;
    const { stokKodu } = req.body;

    if (!stokKodu) {
        res.status(400).json({ error: 'Stok kodu belirtilmedi' });
        return;
    }

    pool.query(
        'UPDATE silodata4 SET stok_kodu = ? WHERE id = ?',
        [stokKodu, id],
        (error, results) => {
            if (error) {
                console.error('Silodata4 güncelleme hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).json({ error: 'Kayıt bulunamadı' });
                return;
            }

            res.json({ success: true, message: 'Kayıt başarıyla güncellendi' });
        }
    );
});

// Silodata5 kaydı silme
app.delete('/api/silodata5/:id', (req, res) => {
    const id = req.params.id;

    pool.query('DELETE FROM silodata5 WHERE id = ?', [id], (error, results) => {
        if (error) {
            console.error('Silodata5 silme hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        if (results.affectedRows === 0) {
            res.status(404).json({ error: 'Kayıt bulunamadı' });
            return;
        }

        res.json({ success: true, message: 'Kayıt başarıyla silindi' });
    });
});

// Silodata5 kaydı güncelleme
app.put('/api/silodata5/:id', (req, res) => {
    const id = req.params.id;
    const { stokKodu } = req.body;

    if (!stokKodu) {
        res.status(400).json({ error: 'Stok kodu belirtilmedi' });
        return;
    }

    pool.query(
        'UPDATE silodata5 SET stok_kodu = ? WHERE id = ?',
        [stokKodu, id],
        (error, results) => {
            if (error) {
                console.error('Silodata5 güncelleme hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).json({ error: 'Kayıt bulunamadı' });
                return;
            }

            res.json({ success: true, message: 'Kayıt başarıyla güncellendi' });
        }
    );
});

// Silodata4'ye kayıt ekle
app.post('/api/silodata4', (req, res) => {
    const { stok_kodu, BoruNo, tarih } = req.body;
    const formattedDate = new Date(tarih).toISOString().slice(0, 19).replace('T', ' ');
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stok_kodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }
            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }
            pool.query('SELECT MAX(id) as maxId FROM silodata4', (error, results) => {
                if (error) {
                    console.error('ID sorgulama hatası:', error.message);
                    res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                    return;
                }
                const maxId = results[0].maxId;
                const updateQuery = `
          UPDATE silodata4
          SET yeni_stok_kodu = ?
          WHERE id = ?
        `;
                pool.query(
                    updateQuery,
                    [stok_kodu, maxId],
                    (error, results) => {
                        if (error) {
                            console.error('Güncelleme hatası:', error.message);
                            res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                            return;
                        }
                        if (results.affectedRows === 0) {
                            console.error('Güncelleme yapılmadı, muhtemelen kayıt yok.');
                            res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                            return;
                        }
                        res.json({
                            message: 'Kayıt güncellendi',
                            stok_kodu,
                            BoruNo
                        });
                    }
                );
            });
        }
    );
});

// Silodata5'ye kayıt ekle
app.post('/api/silodata5', (req, res) => {
    const { stok_kodu, BoruNo, tarih } = req.body;
    const formattedDate = new Date(tarih).toISOString().slice(0, 19).replace('T', ' ');
    pool.query(
        'SELECT id FROM urun_modelleri WHERE stok_kodu = ? AND aktif = 1',
        [stok_kodu],
        (error, results) => {
            if (error) {
                console.error('Stok kodu kontrolü hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }
            if (results.length === 0) {
                res.status(400).json({ error: 'Geçersiz stok kodu' });
                return;
            }
            pool.query('SELECT MAX(id) as maxId FROM silodata5', (error, results) => {
                if (error) {
                    console.error('ID sorgulama hatası:', error.message);
                    res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                    return;
                }
                const maxId = results[0].maxId;
                const updateQuery = `
          UPDATE silodata5
          SET yeni_stok_kodu = ?
          WHERE id = ?
        `;
                pool.query(
                    updateQuery,
                    [stok_kodu, maxId],
                    (error, results) => {
                        if (error) {
                            console.error('Güncelleme hatası:', error.message);
                            res.status(500).json({ error: 'Güncelleme sırasında hata oluştu', details: error });
                            return;
                        }
                        if (results.affectedRows === 0) {
                            console.error('Güncelleme yapılmadı, muhtemelen kayıt yok.');
                            res.status(404).json({ error: 'Güncellenmesi gereken kayıt bulunamadı.' });
                            return;
                        }
                        res.json({
                            message: 'Kayıt güncellendi',
                            stok_kodu,
                            BoruNo
                        });
                    }
                );
            });
        }
    );
});

// Stok koduna ve durum değerine göre silodata verilerini getiren endpoint
app.get('/api/silodata', (req, res) => {
    const stokKodu = req.query.stokKodu;
    const durum = req.query.durum;
    const limit = parseInt(req.query.limit) || 10;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu, s.durum, s.donusum_tarihi, s.donusum_id,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (stokKodu) {
        query += ` AND s.stok_kodu = ?`;
        queryParams.push(stokKodu);
    }

    if (durum) {
        query += ` AND s.durum = ?`;
        queryParams.push(durum);
    }

    query += ` ORDER BY s.tarih DESC LIMIT ?`;
    queryParams.push(limit);

    console.log('Stok kodu ve durum ile sorgu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Veritabanı hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }

        res.json(results);
    });
});

// Delikli boru kategorilerini getiren endpoint
app.get('/api/delikli-boru-kategorileri', (req, res) => {
    console.log('Delikli boru kategorileri isteği alındı');

    // Tüm aktif ürün modellerini al
    pool.query('SELECT stok_kodu, stok_adi FROM urun_modelleri WHERE aktif = 1', (error, results) => {
        if (error) {
            console.error('Delikli boru kategorileri sorgu hatası:', error);
            return res.status(500).json({ error: 'Veritabanı hatası oluştu', details: error.message });
        }

        // Önişlem: delikli boru kategorilerine göre filtrele
        const filtered = results.filter((item) => {
            const ad = item.stok_adi || '';
            return (
                ad.includes('DELİKLİ BORU') ||
                ad.includes('DELIKLI BORU') ||
                ad.includes('DELİKLİ')
            );
        });

        // Varsayılan listeler (eğer veri yoksa)
        const defaultList = [
            { stok_kodu: 'MRZ-ST00661', stok_adi: 'Ø 80 DELİKLİ BORU' },
            { stok_kodu: 'MGS0055', stok_adi: 'Ø 100 DELİKLİ BORU' },
            { stok_kodu: 'MRZ-ST00665', stok_adi: 'Ø 125 DELİKLİ BORU' },
            { stok_kodu: 'MGS0053', stok_adi: 'Ø 160 DELİKLİ BORU' },
            { stok_kodu: 'MGS0056', stok_adi: 'Ø 200 DELİKLİ BORU' }
        ];

        const finalList = filtered.length > 0 ? filtered : defaultList;
        res.json(
            finalList.map(item => ({ stok_kodu: item.stok_kodu, stok_adi: item.stok_adi }))
        );
    });
});

// Silodata_genel verilerini hat ve durum filtresi ile getir
app.get('/api/silodata_genel', (req, res) => {
    const stokKodu = req.query.stokKodu;
    const durum = req.query.durum;
    const limit = parseInt(req.query.limit) || 100;

    let query = `
    SELECT s.ID, s.tarih, s.boru_ad, s.boru_ag, s.buyuk_s_ag, s.stok_kodu,
           s.durum, s.donusum_tarihi, s.donusum_id, s.Hat_no AS hat,
           um.stok_adi, um.hedef_agirlik, um.uzunluk
    FROM silodata_genel s
    LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu
    WHERE 1=1
  `;

    let queryParams = [];

    if (stokKodu) {
        query += ` AND s.stok_kodu = ?`;
        queryParams.push(stokKodu);
    }

    if (durum) {
        query += ` AND s.durum = ?`;
        queryParams.push(durum);
    }

    query += ` ORDER BY s.tarih DESC LIMIT ?`;
    queryParams.push(limit);

    console.log('Silodata_genel sorgusu çalıştırılıyor:', query);
    console.log('Parametreler:', queryParams);

    pool.query(query, queryParams, (error, results) => {
        if (error) {
            console.error('Silodata_genel endpoint hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu', details: error.message });
            return;
        }

        res.json(results);
    });
});

// Ürün dönüştürme endpoint'i - silodata_genel tablosu üzerinden
app.post('/api/silodata_genel/convert/:id', (req, res) => {
    const id = req.params.id;
    console.log(`Silodata_genel - Ürün dönüştürme isteği alındı - ID: ${id}`);

    // Önce ürünün mevcut olup olmadığını kontrol et
    pool.query(
        'SELECT s.*, um.stok_adi FROM silodata_genel s LEFT JOIN urun_modelleri um ON s.stok_kodu = um.stok_kodu WHERE s.ID = ?',
        [id],
        (error, results) => {
            if (error) {
                console.error('Ürün kontrol hatası:', error.message);
                res.status(500).json({ error: 'Veri kontrol sırasında hata oluştu', details: error });
                return;
            }

            if (results.length === 0) {
                console.error(`Dönüştürülecek ürün bulunamadı - ID: ${id}`);
                res.status(404).json({ error: 'Dönüştürülecek ürün bulunamadı' });
                return;
            }

            const urun = results[0];
            if (urun.durum !== 'aktif') {
                console.error(`Ürün dönüştürülemez, durumu aktif değil - ID: ${id}, Durum: ${urun.durum}`);
                res.status(400).json({ error: `Ürün dönüştürülemez, durumu aktif değil. Mevcut durum: ${urun.durum}` });
                return;
            }

            // Ürünün delikli boru tipini belirle
            const stokKodu = urun.stok_kodu;
            const stokAdi = urun.stok_adi || '';
            let keceliBoruKodu = '';

            // Delikli boru stok koduna göre keçeli boru kodunu belirle
            switch (stokKodu) {
                case 'MRZ-ST00661': // Ø 80 DELİKLİ BORU
                    keceliBoruKodu = 'MRZ-ST00663'; // Ø 80 KEÇELİ BORU
                    break;
                case 'MGS0055': // Ø 100 DELİKLİ BORU
                    keceliBoruKodu = 'MRZ-ST02549'; // Ø 100 KEÇELİ BORU
                    break;
                case 'MRZ-ST00665': // Ø 125 DELİKLİ BORU
                    keceliBoruKodu = 'MRZ-ST00666'; // Ø 125 KEÇELİ BORU
                    break;
                case 'MGS0053': // Ø 160 DELİKLİ BORU
                    keceliBoruKodu = 'MRZ-ST02550'; // Ø 160 KEÇELİ BORU
                    break;
                case 'MGS0056': // Ø 200 DELİKLİ BORU
                    keceliBoruKodu = 'MRZ-ST02551'; // Ø 200 KEÇELİ BORU
                    break;
                default:
                    keceliBoruKodu = ''; // Tanımlı eşleşme yok
            }

            console.log(`Ürün bulundu, dönüştürülüyor - ID: ${id}, ${stokKodu} -> ${keceliBoruKodu}`);

            // Keçeli boru listesinden aktif durumda olan en yeni kayıdı bul
            pool.query(
                'SELECT ID FROM silodata_genel WHERE stok_kodu = ? AND durum = "aktif" ORDER BY tarih DESC LIMIT 1',
                [keceliBoruKodu],
                (error, keceliResults) => {
                    if (error) {
                        console.error('Keçeli boru arama hatası:', error.message);
                        res.status(500).json({ error: 'Keçeli boru aranırken hata oluştu', details: error });
                        return;
                    }

                    if (keceliResults.length === 0) {
                        console.error(`Dönüşüm için uygun keçeli boru bulunamadı - Stok Kodu: ${keceliBoruKodu}`);
                        res.status(404).json({ error: `Dönüşüm için uygun keçeli boru bulunamadı - Stok Kodu: ${keceliBoruKodu}` });
                        return;
                    }

                    const keceliBoruId = keceliResults[0].ID;
                    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

                    // Transaction başlat
                    pool.getConnection((err, connection) => {
                        if (err) {
                            console.error('Veritabanı bağlantı hatası:', err);
                            return res.status(500).json({ error: 'Veritabanı bağlantı hatası' });
                        }

                        connection.beginTransaction(err => {
                            if (err) {
                                connection.release();
                                console.error('Transaction başlatma hatası:', err);
                                return res.status(500).json({ error: 'İşlem başlatılamadı' });
                            }

                            // Delikli boruyu pasif yap ve ilişkilendir
                            const updateDelikliQuery = `
                                UPDATE silodata_genel 
                                SET durum = 'pasif', donusum_id = ?, donusum_tarihi = ?
                                WHERE ID = ?
                            `;

                            connection.query(updateDelikliQuery, [keceliBoruId, now, id], (err, result) => {
                                if (err) {
                                    return connection.rollback(() => {
                                        connection.release();
                                        console.error('Delikli boru güncelleme hatası:', err);
                                        res.status(500).json({ error: 'Delikli boru güncellenirken hata oluştu' });
                                    });
                                }

                                // Keçeli boruyu dönüştürülmüş olarak işaretle ve ilişkilendir
                                const updateKeceliQuery = `
                                    UPDATE silodata_genel 
                                    SET durum = 'dönüştürülmüş', donusum_id = ?, donusum_tarihi = ?
                                    WHERE ID = ?
                                `;

                                connection.query(updateKeceliQuery, [id, now, keceliBoruId], (err, result) => {
                                    if (err) {
                                        return connection.rollback(() => {
                                            connection.release();
                                            console.error('Keçeli boru güncelleme hatası:', err);
                                            res.status(500).json({ error: 'Keçeli boru güncellenirken hata oluştu' });
                                        });
                                    }

                                    // İşlemi tamamla
                                    connection.commit(err => {
                                        if (err) {
                                            return connection.rollback(() => {
                                                connection.release();
                                                console.error('İşlem onaylama hatası:', err);
                                                res.status(500).json({ error: 'İşlem tamamlanamadı' });
                                            });
                                        }

                                        connection.release();
                                        console.log(`Dönüşüm başarılı - Delikli: ${id}, Keçeli: ${keceliBoruId}`);
                                        res.json({
                                            success: true,
                                            message: 'Ürün başarıyla dönüştürüldü',
                                            delikliBoruId: id,
                                            keceliBoruId: keceliBoruId,
                                            keceliBoruKodu: keceliBoruKodu,
                                            donusumTarihi: now
                                        });
                                    });
                                });
                            });
                        });
                    });
                }
            );
        }
    );
});

// Silodata_genel tablosuna yeni kayıt ekleme endpoint'i
app.post('/api/silodata_genel', (req, res) => {
    const { tarih, stok_kodu, boru_ad } = req.body;

    if (!tarih || !stok_kodu || !boru_ad) {
        return res.status(400).json({ error: 'Tarih, stok kodu ve boru adı gereklidir' });
    }

    console.log('Silodata_genel - Yeni kayıt ekleme isteği:', { tarih, stok_kodu, boru_ad });

    const insertQuery = `
        INSERT INTO silodata_genel (tarih, stok_kodu, boru_ad, durum, Hat_no)
        VALUES (?, ?, ?, 'aktif', 0)
    `;

    pool.query(insertQuery, [tarih, stok_kodu, boru_ad], (error, results) => {
        if (error) {
            console.error('Silodata_genel kayıt ekleme hatası:', error);
            res.status(500).json({ error: 'Kayıt eklenirken hata oluştu', details: error.message });
            return;
        }

        console.log('Silodata_genel - Yeni kayıt başarıyla eklendi, ID:', results.insertId);
        res.json({
            success: true,
            id: results.insertId,
            message: 'Kayıt başarıyla eklendi'
        });
    });
});

// Toplu dönüşüm işlemi için endpoint - silodata_genel tablosu üzerinden
app.post('/api/silodata_genel/bulk-convert', (req, res) => {
    const { delikliBoruId, keceliBoruId } = req.body;

    if (!delikliBoruId || !keceliBoruId) {
        return res.status(400).json({ error: 'Delikli boru ID ve keçeli boru ID gereklidir' });
    }

    console.log(`Silodata_genel - Toplu dönüşüm isteği alındı - Delikli: ${delikliBoruId}, Keçeli: ${keceliBoruId}`);

    // İşlem için transaction başlat
    pool.getConnection((err, connection) => {
        if (err) {
            console.error('Veritabanı bağlantı hatası:', err);
            return res.status(500).json({ error: 'Veritabanı bağlantı hatası' });
        }

        connection.beginTransaction(err => {
            if (err) {
                connection.release();
                console.error('Transaction başlatma hatası:', err);
                return res.status(500).json({ error: 'İşlem başlatılamadı' });
            }

            const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

            // Önce delikli boruyu pasif yap ve ilişkilendir
            const updateDelikliQuery = `
                UPDATE silodata_genel 
                SET durum = 'pasif', donusum_id = ?, donusum_tarihi = ?
                WHERE ID = ?
            `;

            connection.query(updateDelikliQuery, [keceliBoruId, now, delikliBoruId], (err, result) => {
                if (err) {
                    return connection.rollback(() => {
                        connection.release();
                        console.error('Delikli boru güncelleme hatası:', err);
                        res.status(500).json({ error: 'Delikli boru güncellenirken hata oluştu' });
                    });
                }

                // Sonra keçeli boruyu dönüştürülmüş olarak işaretle ve ilişkilendir
                const updateKeceliQuery = `
                    UPDATE silodata_genel 
                    SET durum = 'dönüştürülmüş', donusum_id = ?, donusum_tarihi = ?
                    WHERE ID = ?
                `;

                connection.query(updateKeceliQuery, [delikliBoruId, now, keceliBoruId], (err, result) => {
                    if (err) {
                        return connection.rollback(() => {
                            connection.release();
                            console.error('Keçeli boru güncelleme hatası:', err);
                            res.status(500).json({ error: 'Keçeli boru güncellenirken hata oluştu' });
                        });
                    }

                    // İşlemi tamamla
                    connection.commit(err => {
                        if (err) {
                            return connection.rollback(() => {
                                connection.release();
                                console.error('İşlem onaylama hatası:', err);
                                res.status(500).json({ error: 'İşlem tamamlanamadı' });
                            });
                        }

                        connection.release();
                        console.log(`Dönüşüm başarılı - Delikli: ${delikliBoruId}, Keçeli: ${keceliBoruId}`);
                        res.json({
                            success: true,
                            message: 'Dönüşüm başarıyla tamamlandı',
                            delikliBoruId,
                            keceliBoruId,
                            donusumTarihi: now
                        });
                    });
                });
            });
        });
    });
});

// Statik dosyaları sunma
app.use('/assets', express.static(path.join(__dirname, 'dist/assets')));
app.use(express.static(path.join(__dirname, 'dist')));

// Ana sayfa
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// HTTP ve HTTPS sunucuları başlat
const PORT = process.env.PORT || 3000;
const HTTPS_PORT = process.env.HTTPS_PORT || 3002;

try {
    // HTTP sunucusu
    const httpServer = http.createServer(app);
    httpServer.listen(PORT, () => {
        console.log(`HTTP Sunucusu port ${PORT} üzerinde çalışıyor`);
    });

    // SSL sertifikası varsa HTTPS sunucusu da başlat
    const sslPath = path.join(__dirname, 'ssl');
    if (fs.existsSync(sslPath)) {
        try {
            const privateKey = fs.readFileSync(path.join(sslPath, 'privkey.pem'), 'utf8');
            const certificate = fs.readFileSync(path.join(sslPath, 'cert.pem'), 'utf8');
            const ca = fs.readFileSync(path.join(sslPath, 'chain.pem'), 'utf8');

            const credentials = {
                key: privateKey,
                cert: certificate,
                ca: ca
            };

            const httpsServer = https.createServer(credentials, app);
            httpsServer.listen(HTTPS_PORT, () => {
                console.log(`HTTPS Sunucusu port ${HTTPS_PORT} üzerinde çalışıyor`);
            });
        } catch (error) {
            console.error('HTTPS sunucusu başlatılamadı:', error);
            console.log('Sadece HTTP sunucusu çalışıyor.');
        }
    } else {
        console.log('SSL sertifikaları bulunamadı. Sadece HTTP sunucusu çalışıyor.');
    }
} catch (error) {
    console.error('Sunucu başlatılamadı:', error);
}

// Ham madde API endpoint'leri

// Ham madde anlık verilerini getir
app.get('/api/hammadde/anlik', (req, res) => {
    const query = `SELECT * FROM HammaddeAnlik ORDER BY Tarih DESC LIMIT 1`;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Ham madde anlık veri hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ham madde set verilerini getir
app.get('/api/hammadde/set', (req, res) => {
    const query = `SELECT * FROM HammaddeSet ORDER BY Tarih DESC LIMIT 1`;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Ham madde set veri hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ham madde stok verilerini getir
app.get('/api/hammadde/stok', (req, res) => {
    const query = `SELECT * FROM HammaddeStok ORDER BY ID DESC LIMIT 1`;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Ham madde stok veri hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ham madde günlük toplamlarını getir
app.get('/api/hammadde/gunluk-toplam', (req, res) => {
    // Bugünün gününü al (22 gibi)
    const today = new Date();
    const todayDay = today.getDate(); // Günün sayısını al (1-31)

    // Bugünün gününe eşit olan tüm kayıtların toplamını hesapla
    const query = `
        SELECT
            SUM(STAB1) as STAB1,
            SUM(STAB2) as STAB2,
            SUM(MUKAVEMET) as MUKAVEMET,
            SUM(PROSES) as PROSES,
            SUM(ENJEKSIYON) as ENJEKSIYON,
            SUM(KALSIT) as KALSIT,
            SUM(PVC) as PVC,
            SUM(MOT12) as MOT12,
            SUM(MOT13) as MOT13,
            SUM(MOT14) as MOT14,
            SUM(MOT15) as MOT15,
            SUM(MOT16) as MOT16,
            SUM(MOT17) as MOT17,
            SUM(MOT18) as MOT18
        FROM HammaddeAnlik
        WHERE DAY(Tarih) = ?
    `;

    pool.query(query, [todayDay], (error, results) => {
        if (error) {
            console.error('Ham madde günlük toplam hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ham madde anlık verilerinin tümünü getir (liste için)
app.get('/api/hammadde/anlik-liste', (req, res) => {
    const query = `SELECT * FROM HammaddeAnlik ORDER BY Tarih DESC, DozajNo DESC`;

    pool.query(query, (error, results) => {
        if (error) {
            console.error('Ham madde anlık liste hatası:', error);
            res.status(500).json({ error: 'Veritabanı hatası oluştu' });
            return;
        }
        res.json(results);
    });
});

// Ham madde stok girişi
app.post('/api/hammadde/stok-girisi', (req, res) => {
    const { hammaddeId, miktar, aciklama } = req.body;

    if (!hammaddeId || !miktar) {
        res.status(400).json({ error: 'Ham madde ID ve miktar gerekli' });
        return;
    }

    // Önce en son kaydı al
    pool.query(
        'SELECT * FROM HammaddeStok ORDER BY Tarih DESC LIMIT 1',
        (error, results) => {
            if (error) {
                console.error('Stok sorgulama hatası:', error);
                res.status(500).json({ error: 'Veritabanı hatası oluştu' });
                return;
            }

            const mevcutMiktar = results.length > 0 ? results[0][hammaddeId] : 0;
            const yeniMiktar = parseFloat(mevcutMiktar || 0) + parseFloat(miktar);

            // Yeni kayıt ekle
            const insertQuery = `
                INSERT INTO HammaddeStok (Tarih, DozajNo, ${hammaddeId})
                VALUES (CURDATE(), ?, ?)
            `;

            pool.query(
                insertQuery,
                [`STOK-${Date.now()}`, yeniMiktar],
                (error, results) => {
                    if (error) {
                        console.error('Stok güncelleme hatası:', error);
                        res.status(500).json({ error: 'Stok güncelleme hatası oluştu' });
                        return;
                    }

                    res.json({
                        success: true,
                        message: 'Stok girişi başarıyla yapıldı',
                        hammaddeId,
                        eklenenMiktar: miktar,
                        yeniMiktar
                    });
                }
            );
        }
    );
});

export default app;

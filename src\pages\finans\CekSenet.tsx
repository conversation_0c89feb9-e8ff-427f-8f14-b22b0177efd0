
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { ChequeManagement } from "@/components/billing/ChequeManagement";
import { PromissoryNoteManagement } from "@/components/billing/PromissoryNoteManagement";

const CekSenet = () => {
  const tabs: TabItem[] = [
    { id: "cheques", label: "Çek Yönetimi", component: <ChequeManagement /> },
    { id: "promissoryNotes", label: "Senet Yönetimi", component: <PromissoryNoteManagement /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Çek ve Senet Yönetimi</h2>
          <p className="text-muted-foreground">
            Çek ve senet işlemleri ve takibi
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default CekSenet;

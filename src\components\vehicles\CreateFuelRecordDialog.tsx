
import { Button } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useVehicleStore } from "@/stores/vehicleStore";
import { FuelRecord } from "@/stores/vehicleStore";

export function CreateFuelRecordDialog() {
  const [open, setOpen] = useState(false);
  const addFuelRecord = useVehicleStore((state) => state.addFuelRecord);
  const vehicles = useVehicleStore((state) => state.vehicles);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const record: Omit<FuelRecord, 'id' | 'createdAt'> = {
      vehicleId: formData.get('vehicleId') as string,
      date: new Date().toISOString(),
      odometer: parseInt(formData.get('odometer') as string),
      fuelAmount: parseFloat(formData.get('fuelAmount') as string),
      fuelPrice: parseFloat(formData.get('fuelPrice') as string),
      totalCost: parseFloat(formData.get('fuelAmount') as string) * parseFloat(formData.get('fuelPrice') as string),
      fullTank: formData.get('fullTank') === 'on',
      station: formData.get('station') as string,
      driver: formData.get('driver') as string,
      notes: formData.get('notes') as string,
    };

    addFuelRecord(record);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Yakıt Kaydı Ekle</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Yakıt Kaydı Ekle</DialogTitle>
            <DialogDescription>
              Yeni bir yakıt kaydı eklemek için aşağıdaki bilgileri doldurun.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="fuelAmount" className="text-right">
                Miktar (L)
              </Label>
              <Input
                id="fuelAmount"
                name="fuelAmount"
                type="number"
                step="0.01"
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="fuelPrice" className="text-right">
                Birim Fiyat
              </Label>
              <Input
                id="fuelPrice"
                name="fuelPrice"
                type="number"
                step="0.01"
                className="col-span-3"
                required
              />
            </div>
            {/* Additional fields */}
          </div>
          <DialogFooter>
            <Button type="submit">Kaydet</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

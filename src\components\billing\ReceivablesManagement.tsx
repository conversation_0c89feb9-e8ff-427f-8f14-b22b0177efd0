
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { DataTable } from "./DataTable";
import { useBillingStore } from "@/stores/billingStore";
import { Badge } from "@/components/ui/badge";

const agingData = [
  { name: "0-30 Gün", value: 150000 },
  { name: "30-60 Gün", value: 80000 },
  { name: "60-90 Gün", value: 40000 },
  { name: "90+ <PERSON>ü<PERSON>", value: 25000 }
];

const collectionData = [
  { name: "<PERSON>amanı<PERSON>", value: 185000 },
  { name: "1-15 Gün Gecik<PERSON>", value: 65000 },
  { name: "15-30 <PERSON>ün Gecikme", value: 30000 },
  { name: "30+ <PERSON><PERSON><PERSON>", value: 15000 }
];

const upcomingCollections = [
  { id: "1", customer: "ABC Şirketi", amount: 25000, dueDate: "2024-07-15", status: "Yaklaşan" },
  { id: "2", customer: "XYZ Ltd", amount: 18500, dueDate: "2024-07-16", status: "Yaklaşan" },
  { id: "3", customer: "123 Holding", amount: 32000, dueDate: "2024-07-12", status: "Gecikmiş" },
  { id: "4", customer: "Tech Çözümler", amount: 14750, dueDate: "2024-07-10", status: "Gecikmiş" },
  { id: "5", customer: "Mega Elektronik", amount: 9800, dueDate: "2024-07-20", status: "Yaklaşan" }
];

const accountsData = [
  { id: "1", customer: "ABC Şirketi", balance: 42500, lastPayment: "2024-06-20", lastPaymentAmount: 15000 },
  { id: "2", customer: "XYZ Ltd", balance: 28750, lastPayment: "2024-06-15", lastPaymentAmount: 22000 },
  { id: "3", customer: "123 Holding", balance: 53200, lastPayment: "2024-06-10", lastPaymentAmount: 18500 },
  { id: "4", customer: "Tech Çözümler", balance: 17800, lastPayment: "2024-06-25", lastPaymentAmount: 9500 },
  { id: "5", customer: "Mega Elektronik", balance: 21300, lastPayment: "2024-06-18", lastPaymentAmount: 12000 }
];

const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444"];

const collectionsColumns = [
  {
    header: "Müşteri",
    accessorKey: "customer"
  },
  {
    header: "Tahsilat Tutarı",
    accessorKey: "amount",
    cell: ({ row }) => <span>₺{row.original.amount.toLocaleString()}</span>
  },
  {
    header: "Vade Tarihi",
    accessorKey: "dueDate"
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => (
      <Badge className={row.original.status === "Gecikmiş" ? "bg-red-500" : "bg-blue-500"}>
        {row.original.status}
      </Badge>
    )
  }
];

const accountsColumns = [
  {
    header: "Müşteri",
    accessorKey: "customer"
  },
  {
    header: "Bakiye",
    accessorKey: "balance",
    cell: ({ row }) => <span>₺{row.original.balance.toLocaleString()}</span>
  },
  {
    header: "Son Ödeme Tarihi",
    accessorKey: "lastPayment"
  },
  {
    header: "Son Ödeme Tutarı",
    accessorKey: "lastPaymentAmount",
    cell: ({ row }) => <span>₺{row.original.lastPaymentAmount.toLocaleString()}</span>
  }
];

export const ReceivablesManagement = () => {
  // Toplam alacak tutarını hesaplama
  const totalReceivables = agingData.reduce((sum, item) => sum + item.value, 0);
  const averageCollectionPeriod = 28; // Demo veri
  const collectionRate = 89; // Demo veri

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Alacak Yönetimi</h2>
      </div>

      {/* Alacak Özeti */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Alacak Tutarı</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">₺{totalReceivables.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Ortalama Tahsilat Süresi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageCollectionPeriod} Gün</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Tahsilat Oranı</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">%{collectionRate}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Vade Analizi */}
        <Card>
          <CardHeader>
            <CardTitle>Vade Analizi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={agingData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                  >
                    {agingData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Alacak Tutarı"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Tahsilat Performansı */}
        <Card>
          <CardHeader>
            <CardTitle>Tahsilat Performansı</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={collectionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                  >
                    {collectionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Tahsilat Tutarı"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tahsilat Takibi */}
      <Card>
        <CardHeader>
          <CardTitle>Tahsilat Takibi</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={collectionsColumns} data={upcomingCollections} />
        </CardContent>
      </Card>

      {/* Müşteri Cari Hesapları */}
      <Card>
        <CardHeader>
          <CardTitle>Müşteri Cari Hesapları</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={accountsColumns} data={accountsData} />
        </CardContent>
      </Card>
    </div>
  );
};

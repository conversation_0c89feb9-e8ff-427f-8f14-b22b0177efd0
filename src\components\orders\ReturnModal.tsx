
import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useOrderStore, ReturnReason, OrderStatus } from "@/stores/orderStore";
import { toast } from "sonner";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface ReturnModalProps {
  orderId: string;
  isOpen: boolean;
  onClose: () => void;
}

export const ReturnModal = ({ orderId, isOpen, onClose }: ReturnModalProps) => {
  const { orders, createReturnRequest, updateReturnStatus, updateOrderStatus } = useOrderStore();
  const order = orders.find(o => o.id === orderId);
  
  const [actionType, setActionType] = useState<'cancel' | 'return'>(
    !order?.shipping ? 'cancel' : 'return'
  );
  const [reason, setReason] = useState<ReturnReason>('not_needed');
  const [description, setDescription] = useState('');
  const [refundAmount, setRefundAmount] = useState(
    order ? order.totalAmount.toString() : '0'
  );
  
  // Reset form when order changes
  useEffect(() => {
    if (order) {
      setActionType(!order.shipping ? 'cancel' : 'return');
      setReason(order.returnRequest?.reason || 'not_needed');
      setDescription(order.returnRequest?.description || '');
      setRefundAmount(order.totalAmount.toString());
    }
  }, [order]);

  const handleSubmit = () => {
    if (!order) return;
    
    try {
      if (actionType === 'cancel') {
        // Cancel the order
        updateOrderStatus(orderId, 'cancelled');
        toast.success("Sipariş iptal edildi");
      } else {
        // Create return request if not exists
        if (!order.returnRequest) {
          createReturnRequest(orderId, {
            reason,
            description,
            items: order.items.map(item => ({
              orderItemId: item.id,
              quantity: item.quantity,
            })),
            status: 'pending',
          });
        }
        
        // Update order status to returned
        updateOrderStatus(orderId, 'returned');
        
        // Update return status if already exists
        if (order.returnRequest) {
          updateReturnStatus(
            orderId, 
            'completed', 
            parseFloat(refundAmount) || order.totalAmount
          );
        }
        
        toast.success("İade işlemi başarıyla oluşturuldu");
      }
      
      onClose();
    } catch (error) {
      toast.error("İşlem sırasında bir hata oluştu");
      console.error(error);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>İptal/İade İşlemi</DialogTitle>
          <DialogDescription>
            {order.number} numaralı siparişin iptal veya iade işlemini yönetin
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label>İşlem Türü</Label>
            <RadioGroup 
              value={actionType} 
              onValueChange={(value: 'cancel' | 'return') => setActionType(value)}
              className="flex gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cancel" id="cancel" />
                <Label htmlFor="cancel">Siparişi İptal Et</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="return" id="return" disabled={!order.shipping} />
                <Label htmlFor="return">Ürün İadesi</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="reason">İptal/İade Nedeni</Label>
            <Select value={reason} onValueChange={(value: ReturnReason) => setReason(value)}>
              <SelectTrigger id="reason">
                <SelectValue placeholder="Neden seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="damaged">Hasarlı Ürün</SelectItem>
                <SelectItem value="wrong_item">Yanlış Ürün</SelectItem>
                <SelectItem value="not_needed">İhtiyaç Kalmadı</SelectItem>
                <SelectItem value="quality_issue">Kalite Sorunu</SelectItem>
                <SelectItem value="other">Diğer</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              placeholder="Detaylı açıklama girin"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="refund-amount">İade Tutarı (₺)</Label>
            <Input
              id="refund-amount"
              type="number"
              value={refundAmount}
              onChange={(e) => setRefundAmount(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose}>İptal</Button>
          <Button type="submit" variant="destructive" onClick={handleSubmit}>
            {actionType === 'cancel' ? 'Siparişi İptal Et' : 'İade İşlemini Tamamla'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

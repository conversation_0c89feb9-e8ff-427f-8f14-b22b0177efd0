
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface YearNavigationProps {
  currentYear: number;
  onYearChange: (year: number) => void;
}

export const YearNavigation: React.FC<YearNavigationProps> = ({ 
  currentYear, 
  onYearChange 
}) => {
  return (
    <div className="flex items-center gap-2">
      <Button 
        variant="outline" 
        size="sm" 
        className="text-gray-700"
        onClick={() => onYearChange(currentYear - 1)}
      >
        {currentYear - 1}
      </Button>
      <Button 
        variant="default" 
        size="sm" 
        className="bg-blue-600 hover:bg-blue-700"
      >
        {currentYear}
      </Button>
      <Button 
        variant="outline" 
        size="sm" 
        className="text-gray-700"
        onClick={() => onYearChange(currentYear + 1)}
      >
        {currentYear + 1}
      </Button>
    </div>
  );
};

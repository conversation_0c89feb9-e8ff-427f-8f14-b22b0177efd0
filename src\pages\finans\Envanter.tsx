
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { InventoryManagement } from "@/components/billing/InventoryManagement";
import { OrderManagement } from "@/components/billing/OrderManagement";

const Envanter = () => {
  const tabs: TabItem[] = [
    { id: "inventory", label: "Stok ve Sipariş", component: <InventoryManagement /> },
    { id: "orders", label: "<PERSON><PERSON><PERSON><PERSON><PERSON>", component: <OrderManagement /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Envanter</h2>
          <p className="text-muted-foreground">
            Stok yönetimi ve sipariş takibi
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Envanter;

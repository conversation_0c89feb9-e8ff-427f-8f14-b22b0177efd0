
import { Company } from "../types";
import { CompanyListItem } from "./CompanyListItem";

interface CompanyListProps {
  companies: Company[];
  onToggleStatus: (id: number) => void;
  onEdit: (company: Company) => void;
}

export const CompanyList = ({ companies, onToggleStatus, onEdit }: CompanyListProps) => {
  return (
    <div className="border rounded-md divide-y">
      <div className="grid grid-cols-7 gap-4 p-3 bg-muted/50 font-medium text-sm">
        <div className="col-span-2">Firma</div>
        <div>Kurum Kodu</div>
        <div>Paket</div>
        <div>Abonelik</div>
        <div>Durum</div>
        <div>İşlemler</div>
      </div>
      
      {companies.map(company => (
        <CompanyListItem 
          key={company.id}
          company={company}
          onToggleStatus={onToggleStatus}
          onEdit={onEdit}
        />
      ))}
    </div>
  );
};

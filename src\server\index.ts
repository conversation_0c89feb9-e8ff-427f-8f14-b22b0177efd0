import express from 'express';
import cors from 'cors';
import 'dotenv/config';
import { testConnection } from '../utils/db';
import musteriRoutes from './routes/musteri';
import faturalarRoutes from './routes/faturalar';
import urunlerRoutes from './routes/urunler';
import odemelerRoutes from './routes/odemeler';
import testRoutes from './routes/test';
import silodataRoutes from './routes/silodata';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Test endpoint
app.get('/api/health', async (req, res) => {
  const dbConnected = await testConnection();
  res.json({
    status: 'up',
    database: dbConnected ? 'connected' : 'disconnected',
    timestamp: new Date().toISOString()
  });
});

// API rotaları
app.use('/api/musteri', musteriRoutes);
app.use('/api/fatura', faturalarRoutes);
app.use('/api/urun', urunlerRoutes);
app.use('/api/odeme', odemelerRoutes);
app.use('/api/test', testRoutes);
app.use('/api/silodata', silodataRoutes);
app.use('/api/silodata2', silodataRoutes);
app.use('/api/silodata3', silodataRoutes);

// Sunucuyu başlat
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    testConnection();
  });
}

export default app;

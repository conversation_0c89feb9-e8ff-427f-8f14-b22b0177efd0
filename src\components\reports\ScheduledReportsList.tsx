
import { useReportsStore } from "@/stores/reportsStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, X, Mail } from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";

export const ScheduledReportsList = () => {
  const { savedReports, scheduledReports, unscheduleReport } = useReportsStore();

  const getReportById = (id: string) => {
    return savedReports.find(report => report.id === id);
  };

  const getFrequencyText = (frequency: string | null) => {
    switch (frequency) {
      case 'daily': return 'Her gün';
      case 'weekly': return 'Her hafta';
      case 'monthly': return 'Her ay';
      default: return '';
    }
  };

  if (scheduledReports.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Hen<PERSON>z zamanlanmış rapor bulunmuyor.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {scheduledReports.map((scheduledReport) => {
        const report = getReportById(scheduledReport.reportId);
        if (!report) return null;

        return (
          <Card key={scheduledReport.reportId}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {report.name}
                </CardTitle>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {getFrequencyText(report.schedule.frequency)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-muted-foreground">Sonraki gönderim:</span>
                    <span className="font-medium">
                      {format(new Date(scheduledReport.nextRun), 'PPP', { locale: tr })}
                    </span>
                  </div>
                  {report.schedule.lastSent && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Son gönderim:</span>
                      <span>
                        {format(new Date(report.schedule.lastSent), 'PPP', { locale: tr })}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <Mail className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-sm">
                      {report.schedule.recipients.length} alıcı
                    </span>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="flex items-center gap-1 text-destructive hover:bg-destructive/10"
                    onClick={() => unscheduleReport(report.id)}
                  >
                    <X className="h-3.5 w-3.5" />
                    İptal
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

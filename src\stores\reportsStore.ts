
import { create } from "zustand";
import { addDays, subDays, format } from "date-fns";

export type ReportType = 
  | "sales" 
  | "inventory" 
  | "customers" 
  | "orders"
  | "custom";

export type TimeFrame = 
  | "daily" 
  | "weekly" 
  | "monthly" 
  | "quarterly" 
  | "yearly" 
  | "custom";

export type ChartType = 
  | "bar" 
  | "line" 
  | "pie" 
  | "area"
  | "table";

export interface Report {
  id: string;
  name: string;
  description: string;
  type: ReportType;
  timeframe: TimeFrame;
  chartType: ChartType;
  createdAt: Date;
  lastRunAt: Date | null;
  schedule: {
    enabled: boolean;
    frequency: "daily" | "weekly" | "monthly" | null;
    recipients: string[];
    lastSent: Date | null;
  };
  filters: Record<string, any>;
  data: any[];
}

interface ScheduledReport {
  reportId: string;
  nextRun: Date;
}

interface ReportsState {
  reports: Report[];
  savedReports: Report[];
  scheduledReports: ScheduledReport[];
  salesData: any[];
  inventoryData: any[];
  ordersData: any[];
  createReport: (report: Omit<Report, "id" | "createdAt" | "lastRunAt">) => Report;
  saveReport: (report: Report) => void;
  deleteReport: (id: string) => void;
  scheduleReport: (reportId: string, frequency: "daily" | "weekly" | "monthly", recipients: string[]) => void;
  unscheduleReport: (reportId: string) => void;
  generateDemoData: () => void;
  getReportById: (id: string) => Report | undefined;
}

export const useReportsStore = create<ReportsState>((set, get) => ({
  reports: [],
  savedReports: [],
  scheduledReports: [],
  salesData: [],
  inventoryData: [],
  ordersData: [],

  createReport: (reportData) => {
    const newReport: Report = {
      id: crypto.randomUUID(),
      createdAt: new Date(),
      lastRunAt: new Date(),
      ...reportData,
    };

    set((state) => ({ 
      reports: [...state.reports, newReport]
    }));

    return newReport;
  },

  saveReport: (report) => {
    set((state) => {
      // Check if report already exists
      const existingIndex = state.savedReports.findIndex(r => r.id === report.id);
      
      if (existingIndex >= 0) {
        // Update existing report
        const updatedReports = [...state.savedReports];
        updatedReports[existingIndex] = report;
        return { savedReports: updatedReports };
      } else {
        // Add new report
        return { savedReports: [...state.savedReports, report] };
      }
    });
  },

  deleteReport: (id) => {
    set((state) => ({
      savedReports: state.savedReports.filter(report => report.id !== id),
      scheduledReports: state.scheduledReports.filter(sr => sr.reportId !== id)
    }));
  },

  scheduleReport: (reportId, frequency, recipients) => {
    set((state) => {
      // Update the report with scheduling info
      const updatedReports = state.savedReports.map(report => {
        if (report.id === reportId) {
          return {
            ...report,
            schedule: {
              enabled: true,
              frequency,
              recipients,
              lastSent: null
            }
          };
        }
        return report;
      });

      // Add to scheduled reports
      let nextRun = new Date();
      if (frequency === "daily") {
        nextRun = addDays(new Date(), 1);
      } else if (frequency === "weekly") {
        nextRun = addDays(new Date(), 7);
      } else if (frequency === "monthly") {
        nextRun = addDays(new Date(), 30);
      }

      const newScheduledReport = {
        reportId,
        nextRun
      };

      const existingIndex = state.scheduledReports.findIndex(sr => sr.reportId === reportId);
      let updatedScheduledReports;
      
      if (existingIndex >= 0) {
        updatedScheduledReports = [...state.scheduledReports];
        updatedScheduledReports[existingIndex] = newScheduledReport;
      } else {
        updatedScheduledReports = [...state.scheduledReports, newScheduledReport];
      }

      return {
        savedReports: updatedReports,
        scheduledReports: updatedScheduledReports
      };
    });
  },

  unscheduleReport: (reportId) => {
    set((state) => {
      // Update the report to disable scheduling
      const updatedReports = state.savedReports.map(report => {
        if (report.id === reportId) {
          return {
            ...report,
            schedule: {
              ...report.schedule,
              enabled: false
            }
          };
        }
        return report;
      });

      // Remove from scheduled reports
      const updatedScheduledReports = state.scheduledReports.filter(
        sr => sr.reportId !== reportId
      );

      return {
        savedReports: updatedReports,
        scheduledReports: updatedScheduledReports
      };
    });
  },

  getReportById: (id) => {
    return get().savedReports.find(report => report.id === id);
  },

  generateDemoData: () => {
    // Generate demo data for reports
    const today = new Date();
    const days = Array.from({ length: 30 }, (_, i) => {
      const date = subDays(today, 29 - i);
      return format(date, 'yyyy-MM-dd');
    });

    // Sales data by day
    const salesData = days.map(day => {
      const randomSales = Math.floor(Math.random() * 50000) + 10000;
      const randomOrders = Math.floor(Math.random() * 50) + 10;
      const avgOrderValue = randomSales / randomOrders;
      
      return {
        date: day,
        sales: randomSales,
        orders: randomOrders,
        avgOrderValue: avgOrderValue.toFixed(2)
      };
    });

    // Inventory data for products
    const products = ['Laptop', 'Telefon', 'Tablet', 'Kulaklık', 'Monitör', 'Klavye', 'Mouse', 'Hoparlör'];
    const inventoryData = products.map(product => {
      const stock = Math.floor(Math.random() * 100) + 10;
      const value = (Math.random() * 1000 + 200) * stock;
      const reorderLevel = Math.floor(Math.random() * 20) + 5;
      const turnoverRate = Math.random() * 0.5 + 0.1;
      
      return {
        product,
        stock,
        value: value.toFixed(2),
        reorderLevel,
        turnoverRate: turnoverRate.toFixed(2)
      };
    });

    // Orders data by status
    const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'];
    const ordersData = statuses.map(status => {
      const count = Math.floor(Math.random() * 50) + 5;
      
      return {
        status,
        count
      };
    });

    // Create some example reports
    const salesReport: Report = {
      id: crypto.randomUUID(),
      name: "Aylık Satış Raporu",
      description: "Son 30 günlük satış verileri",
      type: "sales",
      timeframe: "monthly",
      chartType: "line",
      createdAt: new Date(),
      lastRunAt: new Date(),
      data: salesData,
      schedule: {
        enabled: true,
        frequency: "monthly",
        recipients: ["<EMAIL>"],
        lastSent: subDays(new Date(), 5)
      },
      filters: {
        startDate: subDays(new Date(), 30),
        endDate: new Date()
      }
    };

    const inventoryReport: Report = {
      id: crypto.randomUUID(),
      name: "Stok Durum Raporu",
      description: "Mevcut stok durumu ve değeri",
      type: "inventory",
      timeframe: "daily",
      chartType: "bar",
      createdAt: new Date(),
      lastRunAt: new Date(),
      data: inventoryData,
      schedule: {
        enabled: false,
        frequency: null,
        recipients: [],
        lastSent: null
      },
      filters: {}
    };

    const ordersReport: Report = {
      id: crypto.randomUUID(),
      name: "Sipariş Durum Raporu",
      description: "Tüm siparişlerin durum dağılımı",
      type: "orders",
      timeframe: "weekly",
      chartType: "pie",
      createdAt: new Date(),
      lastRunAt: new Date(),
      data: ordersData,
      schedule: {
        enabled: true,
        frequency: "weekly",
        recipients: ["<EMAIL>", "<EMAIL>"],
        lastSent: subDays(new Date(), 2)
      },
      filters: {
        statuses: ["pending", "processing", "shipped", "delivered", "cancelled", "returned"]
      }
    };

    set({
      salesData,
      inventoryData,
      ordersData,
      savedReports: [salesReport, inventoryReport, ordersReport],
      scheduledReports: [
        {
          reportId: salesReport.id,
          nextRun: addDays(new Date(), 25)
        },
        {
          reportId: ordersReport.id,
          nextRun: addDays(new Date(), 5)
        }
      ]
    });
  }
}));

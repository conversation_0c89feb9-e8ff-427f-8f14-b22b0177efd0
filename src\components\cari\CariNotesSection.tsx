
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { StickyNote } from "lucide-react";

interface CariNotesSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
}

export const CariNotesSection = ({ formData, updateFormData }: CariNotesSectionProps) => {
  return (
    <div className="space-y-2 bg-white p-3 rounded-lg border shadow-sm">
      <h4 className="text-md font-semibold text-primary border-b pb-1">Notlar</h4>
      
      <div className="space-y-1">
        <div className="flex items-center gap-1">
          <StickyNote className="h-4 w-4 text-primary" />
          <Label htmlFor="notlar" className="font-medium text-sm">Notlar</Label>
        </div>
        <Textarea 
          id="notlar" 
          placeholder="Cari hesapla ilgili <PERSON> notlar, hatırlatmalar veya ek bilgiler" 
          rows={2}
          value={formData.notlar || ""}
          onChange={(e) => updateFormData("notlar", e.target.value)}
          className="text-sm min-h-[60px]"
        />
      </div>
    </div>
  );
};

import React, { useEffect, useCallback, memo } from 'react';
import { Calendar, Filter, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';

interface FilterCardProps {
  selectedDate: string;
  setSelectedDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
  selectedDiameters: Record<string, boolean>;
  handleDiameterChange: (diameter: string, checked: boolean) => void;
  dateFilterType: 'tekGun' | 'aralik';
  setDateFilterType: (type: 'tekGun' | 'aralik') => void;
  applyFilters: () => void;
  resetFilters: () => void;
  disabledDiameters: string[];
}

const FilterCard = ({
  selectedDate,
  setSelectedDate,
  endDate,
  setEndDate,
  selectedDiameters,
  handleDiameterChange,
  dateFilterType,
  setDateFilterType,
  applyFilters,
  resetFilters,
  disabledDiameters
}: FilterCardProps) => {
  // Tarih değişikliklerinde filtreleri uygula
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      applyFilters();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [selectedDate, endDate, dateFilterType, applyFilters]);

  // Tarih filtresi tipini değiştiren fonksiyon
  const handleDateTypeChange = useCallback((type: 'tekGun' | 'aralik') => {
    setDateFilterType(type);
  }, [setDateFilterType]);

  // Tarih değişikliği işleyicisi
  const handleDateChange = useCallback((e: React.ChangeEvent<HTMLInputElement>, isEndDate = false) => {
    const value = e.target.value;
    if (isEndDate) {
      setEndDate(value);
    } else {
      setSelectedDate(value);
    }
  }, [setSelectedDate, setEndDate]);

  // Çap değişikliği işleyicisi
  const handleCheckboxChange = useCallback((diameter: string, checked: boolean) => {
    if (!disabledDiameters.includes(diameter)) {
      handleDiameterChange(diameter, checked);
    }
  }, [handleDiameterChange, disabledDiameters]);

  return (
    <div className="bg-white p-3 rounded-lg shadow-sm h-full">
      {/* Tarih Filtresi */}
      <div className="mb-2">
        <div className="flex items-center gap-1 mb-1">
          <Calendar className="text-blue-600" size={14} />
          <span className="font-medium text-blue-600 text-xs">Tarih Filtreleri</span>

          <div className="ml-auto flex gap-1">
            <button
              className={`px-2 py-0.5 text-xs ${dateFilterType === 'tekGun' ? 'bg-blue-100 text-blue-600 font-medium rounded' : 'text-gray-500 hover:bg-gray-100 rounded'}`}
              onClick={() => handleDateTypeChange('tekGun')}
            >
              Tek Gün
            </button>
            <button
              className={`px-2 py-0.5 text-xs ${dateFilterType === 'aralik' ? 'bg-blue-100 text-blue-600 font-medium rounded' : 'text-gray-500 hover:bg-gray-100 rounded'}`}
              onClick={() => handleDateTypeChange('aralik')}
            >
              Aralık
            </button>
          </div>
        </div>

        {dateFilterType === 'tekGun' ? (
          <div className="border rounded-lg p-1">
            <Input
              type="date"
              value={selectedDate}
              onChange={(e) => handleDateChange(e)}
              className="p-1 w-full text-xs"
            />
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-1">
            <div className="border rounded-lg p-1">
              <label className="text-xs text-gray-500 block">Başlangıç:</label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => handleDateChange(e)}
                className="p-1 w-full text-xs"
              />
            </div>
            <div className="border rounded-lg p-1">
              <label className="text-xs text-gray-500 block">Bitiş:</label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => handleDateChange(e, true)}
                className="p-1 w-full text-xs"
              />
            </div>
          </div>
        )}
      </div>

      {/* Boru Çapı Filtresi */}
      <div>
        <div className="flex items-center gap-1 mb-1">
          <Filter className="text-blue-600" size={14} />
          <span className="font-medium text-blue-600 text-xs">Boru Çapı</span>

          <Button
            variant="outline"
            className="ml-auto flex items-center gap-1 border-blue-200 text-blue-600 hover:bg-blue-50 py-0.5 h-auto text-xs"
            onClick={resetFilters}
          >
            <RotateCcw size={10} />
            Temizle
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-1">
          {Object.entries(selectedDiameters).map(([diameter, isSelected]) => {
            const isDisabled = disabledDiameters.includes(diameter);
            return (
              <div key={diameter} className="flex items-center space-x-1">
                <Checkbox
                  id={`diameter-${diameter}`}
                  checked={isSelected}
                  onCheckedChange={(checked) => handleCheckboxChange(diameter, checked === true)}
                  className={`h-3 w-3 border-blue-400 data-[state=checked]:bg-blue-600 ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={isDisabled}
                />
                <label
                  htmlFor={`diameter-${diameter}`}
                  className={`text-xs font-medium leading-none ${isDisabled ? 'text-gray-400 cursor-not-allowed' : ''}`}
                  onClick={(e) => {
                    if (isDisabled) {
                      e.preventDefault();
                    }
                  }}
                >
                  Ø {diameter}
                </label>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// memo kullanarak gereksiz yeniden render'ları önlüyoruz
export default memo(FilterCard);

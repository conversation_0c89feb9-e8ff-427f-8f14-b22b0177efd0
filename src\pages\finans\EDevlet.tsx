
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { EGovernmentIntegration } from "@/components/billing/EGovernmentIntegration";

const EDevlet = () => {
  const tabs: TabItem[] = [
    { id: "e-government", label: "E-Devlet", component: <EGovernmentIntegration /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">E-Devlet İşlemleri</h2>
          <p className="text-muted-foreground">
            E-devlet entegrasyonları ve işlemleri
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default EDevlet;

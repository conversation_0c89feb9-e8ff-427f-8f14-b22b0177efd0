
import { format } from 'date-fns';

// Log seviyeleri
type LogLevel = 'info' | 'warn' | 'error' | 'debug';

// Logger sınıfı
class Logger {
  private logToConsole: boolean;
  private logToStorage: boolean;
  private logLevel: LogLevel;
  
  constructor() {
    this.logToConsole = true;
    this.logToStorage = true;
    this.logLevel = 'info'; // default log seviyesi
  }
  
  // Log seviyesini değiştir
  setLogLevel(level: LogLevel) {
    this.logLevel = level;
  }
  
  // Konsol loglamayı aç/kapat
  setConsoleLogging(enabled: boolean) {
    this.logToConsole = enabled;
  }
  
  // Depolama loglamayı aç/kapat
  setStorageLogging(enabled: boolean) {
    this.logToStorage = enabled;
  }
  
  // Tarih ve saat formatını al
  private getTimeStamp(): string {
    return format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  }
  
  // Hata bilgisini formatla
  private formatError(error: unknown): string {
    if (error instanceof Error) {
      return `${error.name}: ${error.message}\n${error.stack || ''}`;
    }
    return String(error);
  }
  
  // Logu kaydet (localStorage veya IndexedDB'ye)
  private saveLog(level: LogLevel, message: string): void {
    if (!this.logToStorage) return;
    
    try {
      // Son 100 log kaydını tut
      const MAX_LOGS = 100;
      
      // LocalStorage'dan mevcut logları al
      const storedLogs = localStorage.getItem('application_logs');
      const logs = storedLogs ? JSON.parse(storedLogs) : [];
      
      // Yeni log ekle
      logs.push({
        timestamp: this.getTimeStamp(),
        level,
        message
      });
      
      // En eski logları sil (maksimum sayıda log tut)
      while (logs.length > MAX_LOGS) {
        logs.shift();
      }
      
      // Güncellenmiş logları kaydet
      localStorage.setItem('application_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Log kaydetme hatası:', error);
    }
  }
  
  // Info log
  info(message: string): void {
    const formattedMessage = `[INFO] [${this.getTimeStamp()}] ${message}`;
    
    if (this.logToConsole) {
      console.info('%c' + formattedMessage, 'color: #3498db');
    }
    
    this.saveLog('info', formattedMessage);
  }
  
  // Uyarı log
  warn(message: string): void {
    const formattedMessage = `[WARN] [${this.getTimeStamp()}] ${message}`;
    
    if (this.logToConsole) {
      console.warn('%c' + formattedMessage, 'color: #f39c12');
    }
    
    this.saveLog('warn', formattedMessage);
  }
  
  // Hata log
  error(message: string, error?: unknown): void {
    let formattedMessage = `[ERROR] [${this.getTimeStamp()}] ${message}`;
    
    if (error) {
      formattedMessage += '\n' + this.formatError(error);
    }
    
    if (this.logToConsole) {
      console.error('%c' + formattedMessage, 'color: #e74c3c');
    }
    
    this.saveLog('error', formattedMessage);
  }
  
  // Debug log
  debug(message: string): void {
    if (this.logLevel !== 'debug') return;
    
    const formattedMessage = `[DEBUG] [${this.getTimeStamp()}] ${message}`;
    
    if (this.logToConsole) {
      console.debug('%c' + formattedMessage, 'color: #2ecc71');
    }
    
    this.saveLog('debug', formattedMessage);
  }
  
  // Tüm logları al
  getLogs(): Array<{ timestamp: string; level: LogLevel; message: string }> {
    const storedLogs = localStorage.getItem('application_logs');
    return storedLogs ? JSON.parse(storedLogs) : [];
  }
  
  // Logları temizle
  clearLogs(): void {
    localStorage.removeItem('application_logs');
  }
}

// Singleton instance oluştur
export const logger = new Logger();

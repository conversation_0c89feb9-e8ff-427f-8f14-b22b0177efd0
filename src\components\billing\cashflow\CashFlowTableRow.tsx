
import React from "react";
import { Button } from "@/components/ui/button";
import { TableRow, TableCell } from "@/components/ui/table";
import { ChevronDown, ChevronUp } from "lucide-react";
import { formatCurrency, months } from "../utils/cashFlowUtils";

interface CashFlowTableRowProps {
  title: string;
  bgColor?: string;
  isExpandable?: boolean;
  isExpanded?: boolean;
  onToggle?: () => void;
  data?: Record<string, number>;
  indented?: boolean;
}

export const CashFlowTableRow: React.FC<CashFlowTableRowProps> = ({
  title,
  bgColor = "",
  isExpandable = false,
  isExpanded = false,
  onToggle,
  data = {},
  indented = false
}) => {
  return (
    <TableRow className={bgColor}>
      <TableCell className={`font-medium ${indented ? "pl-10" : ""}`}>
        {isExpandable ? (
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6 p-0" 
              onClick={onToggle}
            >
              {isExpanded ? 
                <ChevronDown className="h-4 w-4" /> : 
                <ChevronUp className="h-4 w-4" />
              }
            </Button>
            {title}
          </div>
        ) : (
          title
        )}
      </TableCell>
      {months.map(month => (
        <TableCell key={month} className="text-center font-medium">
          {formatCurrency(data[month] || 0)}
        </TableCell>
      ))}
    </TableRow>
  );
};

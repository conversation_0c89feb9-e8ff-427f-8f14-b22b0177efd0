import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { useReportsStore, ReportType, TimeFrame, ChartType } from "@/stores/reportsStore";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

export function CreateReportForm() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [type, setType] = useState<ReportType>("sales");
  const [timeframe, setTimeframe] = useState<TimeFrame>("monthly");
  const [chartType, setChartType] = useState<ChartType>("line");

  const { createReport, saveReport } = useReportsStore();
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name) {
      toast({
        title: "Hata",
        description: "Rapor adı girmelisiniz.",
        variant: "destructive",
      });
      return;
    }

    // Create new report
    const reportData = {
      name,
      description,
      type,
      timeframe,
      chartType,
      schedule: {
        enabled: false,
        frequency: null,
        recipients: [],
        lastSent: null,
      },
      filters: {},
      data: [],
    };
    
    // Create the report
    const newReport = createReport(reportData);
    
    // Save the report - no need to check if it exists
    saveReport(newReport);

    toast({
      title: "Rapor Oluşturuldu",
      description: "Özel rapor başarıyla oluşturuldu.",
    });

    // Reset form
    setName("");
    setDescription("");
    setType("sales");
    setTimeframe("monthly");
    setChartType("line");
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Rapor Adı</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Aylık Satış Raporu"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Rapor Tipi</Label>
              <Select 
                value={type} 
                onValueChange={(value: ReportType) => setType(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Rapor tipi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Satış</SelectItem>
                  <SelectItem value="inventory">Envanter</SelectItem>
                  <SelectItem value="orders">Siparişler</SelectItem>
                  <SelectItem value="customers">Müşteriler</SelectItem>
                  <SelectItem value="custom">Özel</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Bu rapor aylık satış verilerini gösterir..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="timeframe">Zaman Aralığı</Label>
              <Select 
                value={timeframe} 
                onValueChange={(value: TimeFrame) => setTimeframe(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Zaman aralığı seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Günlük</SelectItem>
                  <SelectItem value="weekly">Haftalık</SelectItem>
                  <SelectItem value="monthly">Aylık</SelectItem>
                  <SelectItem value="quarterly">Üç Aylık</SelectItem>
                  <SelectItem value="yearly">Yıllık</SelectItem>
                  <SelectItem value="custom">Özel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="chartType">Görselleştirme Tipi</Label>
              <Select 
                value={chartType} 
                onValueChange={(value: ChartType) => setChartType(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Grafik türü seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Çizgi Grafik</SelectItem>
                  <SelectItem value="bar">Çubuk Grafik</SelectItem>
                  <SelectItem value="pie">Pasta Grafik</SelectItem>
                  <SelectItem value="area">Alan Grafik</SelectItem>
                  <SelectItem value="table">Tablo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Tabs defaultValue="data-source" className="w-full">
          <TabsList className="w-full">
            <TabsTrigger value="data-source" className="flex-1">Veri Kaynağı</TabsTrigger>
            <TabsTrigger value="filters" className="flex-1">Filtreler</TabsTrigger>
            <TabsTrigger value="visualization" className="flex-1">Görselleştirme</TabsTrigger>
          </TabsList>
          
          <TabsContent value="data-source" className="p-4 border rounded-md mt-4">
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Veri Kaynağı Seçin</Label>
                    <Select defaultValue="database">
                      <SelectTrigger>
                        <SelectValue placeholder="Veri kaynağı seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="database">Veritabanı</SelectItem>
                        <SelectItem value="file">Dosya (CSV, Excel)</SelectItem>
                        <SelectItem value="api">Harici API</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Tablo/Koleksiyon</Label>
                    <Select defaultValue="orders">
                      <SelectTrigger>
                        <SelectValue placeholder="Tablo seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="orders">Siparişler</SelectItem>
                        <SelectItem value="products">Ürünler</SelectItem>
                        <SelectItem value="customers">Müşteriler</SelectItem>
                        <SelectItem value="invoices">Faturalar</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="filters" className="p-4 border rounded-md mt-4">
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Tarih Aralığı</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Input type="date" placeholder="Başlangıç" />
                      <Input type="date" placeholder="Bitiş" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Özel Filtreler</Label>
                    <div className="grid grid-cols-3 gap-4">
                      <Select defaultValue="status">
                        <SelectTrigger>
                          <SelectValue placeholder="Alan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="status">Durum</SelectItem>
                          <SelectItem value="category">Kategori</SelectItem>
                          <SelectItem value="amount">Tutar</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Select defaultValue="equals">
                        <SelectTrigger>
                          <SelectValue placeholder="Operatör" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="equals">Eşittir</SelectItem>
                          <SelectItem value="contains">İçerir</SelectItem>
                          <SelectItem value="greater">Büyüktür</SelectItem>
                          <SelectItem value="less">Küçüktür</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Input placeholder="Değer" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="visualization" className="p-4 border rounded-md mt-4">
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>X Ekseni</Label>
                    <Select defaultValue="date">
                      <SelectTrigger>
                        <SelectValue placeholder="X Ekseni" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date">Tarih</SelectItem>
                        <SelectItem value="category">Kategori</SelectItem>
                        <SelectItem value="product">Ürün</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Y Ekseni</Label>
                    <Select defaultValue="amount">
                      <SelectTrigger>
                        <SelectValue placeholder="Y Ekseni" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="amount">Tutar</SelectItem>
                        <SelectItem value="quantity">Miktar</SelectItem>
                        <SelectItem value="count">Sayı</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Renk Teması</Label>
                    <Select defaultValue="default">
                      <SelectTrigger>
                        <SelectValue placeholder="Renk Teması" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">Varsayılan</SelectItem>
                        <SelectItem value="monochrome">Tek Renk</SelectItem>
                        <SelectItem value="colorful">Renkli</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button type="submit">Rapor Oluştur</Button>
        </div>
      </div>
    </form>
  );
}

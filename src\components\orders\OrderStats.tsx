
import { useOrderStore } from "@/stores/orderStore";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, PackageCheck, PackageX, RefreshCcw } from "lucide-react";

export const OrderStats = () => {
  const { orders } = useOrderStore();
  
  // Calculate stats
  const pendingOrders = orders.filter(order => order.status === 'pending' || order.status === 'processing').length;
  const shippedOrders = orders.filter(order => order.status === 'shipped').length;
  const deliveredOrders = orders.filter(order => order.status === 'delivered').length;
  const returnedOrders = orders.filter(order => order.status === 'returned' || order.returnRequest).length;
  
  const stats = [
    {
      title: "Bekleyen Siparişler",
      value: pendingOrders,
      description: "İşlem bekleyen siparişler",
      icon: Package,
      iconColor: "text-blue-500",
    },
    {
      title: "Kargodaki Si<PERSON>şler",
      value: shippedOrders,
      description: "Teslimat yolunda",
      icon: PackageCheck,
      iconColor: "text-yellow-500",
    },
    {
      title: "Teslim Edilen",
      value: deliveredOrders,
      description: "Başarıyla teslim edildi",
      icon: PackageCheck,
      iconColor: "text-green-500",
    },
    {
      title: "İadeler",
      value: returnedOrders,
      description: "İptal/iade edilen siparişler",
      icon: RefreshCcw,
      iconColor: "text-red-500",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.iconColor}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

import express from 'express';
import { query } from '../../utils/db';
import { ResultSetHeader } from 'mysql2';

const router = express.Router();

// Get silodata record by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('SELECT * FROM silodata WHERE ID = ?', [id]);

    if (Array.isArray(result) && result.length === 0) {
      return res.status(404).json({ error: 'Ürün bulunamadı' });
    }

    res.json(result[0]);
  } catch (error) {
    console.error('Ürün getirme hatası:', error);
    res.status(500).json({ error: 'Ürün getirilemedi' });
  }
});

// Get silodata2 record by ID
router.get('/hat2/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('SELECT * FROM silodata2 WHERE ID = ?', [id]);

    if (Array.isArray(result) && result.length === 0) {
      return res.status(404).json({ error: 'Ürün bulunamadı' });
    }

    res.json(result[0]);
  } catch (error) {
    console.error('Ürün getirme hatası:', error);
    res.status(500).json({ error: 'Ürün getirilemedi' });
  }
});

// Update silodata2 record
router.put('/hat2/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { stokKodu } = req.body;

    if (!stokKodu) {
      return res.status(400).json({ error: 'stokKodu gereklidir' });
    }

    const result = await query(
      'UPDATE silodata2 SET stok_kodu = ? WHERE ID = ?',
      [stokKodu, id]
    ) as ResultSetHeader;

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Güncellenecek kayıt bulunamadı' });
    }

    res.status(200).json({ message: 'Kayıt başarıyla güncellendi', id });
  } catch (error) {
    console.error('Güncelleme hatası:', error);
    res.status(500).json({ error: 'Güncelleme işlemi başarısız' });
  }
});

// Delete silodata2 record
router.delete('/hat2/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('DELETE FROM silodata2 WHERE ID = ?', [id]) as ResultSetHeader;

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Silinecek kayıt bulunamadı' });
    }

    res.status(200).json({ message: 'Kayıt başarıyla silindi', id });
  } catch (error) {
    console.error('Silme hatası:', error);
    res.status(500).json({ error: 'Silme işlemi başarısız' });
  }
});

// Get silodata3 record by ID
router.get('/hat3/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('SELECT * FROM silodata3 WHERE ID = ?', [id]);

    if (Array.isArray(result) && result.length === 0) {
      return res.status(404).json({ error: 'Ürün bulunamadı' });
    }

    res.json(result[0]);
  } catch (error) {
    console.error('Ürün getirme hatası:', error);
    res.status(500).json({ error: 'Ürün getirilemedi' });
  }
});

// Update silodata3 record
router.put('/hat3/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { stokKodu } = req.body;

    if (!stokKodu) {
      return res.status(400).json({ error: 'stokKodu gereklidir' });
    }

    const result = await query(
      'UPDATE silodata3 SET stok_kodu = ? WHERE ID = ?',
      [stokKodu, id]
    ) as ResultSetHeader;

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Güncellenecek kayıt bulunamadı' });
    }

    res.status(200).json({ message: 'Kayıt başarıyla güncellendi', id });
  } catch (error) {
    console.error('Güncelleme hatası:', error);
    res.status(500).json({ error: 'Güncelleme işlemi başarısız' });
  }
});

// Delete silodata3 record
router.delete('/hat3/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('DELETE FROM silodata3 WHERE ID = ?', [id]) as ResultSetHeader;

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Silinecek kayıt bulunamadı' });
    }

    res.status(200).json({ message: 'Kayıt başarıyla silindi', id });
  } catch (error) {
    console.error('Silme hatası:', error);
    res.status(500).json({ error: 'Silme işlemi başarısız' });
  }
});

// Convert product
router.post('/convert/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Start a transaction
    await query('START TRANSACTION');

    // Get the original record
    const originalResult = await query('SELECT * FROM silodata WHERE ID = ? AND durum = "aktif"', [id]);

    if (Array.isArray(originalResult) && originalResult.length === 0) {
      await query('ROLLBACK');
      return res.status(404).json({ error: 'Aktif ürün bulunamadı veya ürün dönüştürülmüş olabilir' });
    }

    const originalRecord = originalResult[0];

    // Get the target product code from urun_modelleri table
    const targetProductResult = await query('SELECT stok_kodu FROM urun_modelleri WHERE stok_adi = "Ø 160 KEÇELİ BORU" LIMIT 1');

    if (Array.isArray(targetProductResult) && targetProductResult.length === 0) {
      await query('ROLLBACK');
      return res.status(404).json({ error: 'Hedef ürün modeli bulunamadı' });
    }

    const targetProductCode = targetProductResult[0].stok_kodu;

    // Update original record status
    await query('UPDATE silodata SET durum = "pasif" WHERE ID = ?', [id]);

    // Create new record
    const now = new Date();
    await query(
      `INSERT INTO silodata 
      (tarih, boru_ad, boru_ag, buyuk_s_ag, stok_kodu, durum, donusum_id, donusum_tarihi) 
      VALUES (?, ?, ?, ?, ?, "donusmus", ?, ?)`,
      [
        originalRecord.tarih,
        originalRecord.boru_ad,
        originalRecord.boru_ag,
        originalRecord.buyuk_s_ag,
        targetProductCode,
        id,
        now
      ]
    );

    // Get the ID of the newly inserted record
    const newRecordResult = await query('SELECT LAST_INSERT_ID() as newId');
    const newId = newRecordResult[0].newId;

    // Commit the transaction
    await query('COMMIT');

    res.status(200).json({
      message: 'Ürün başarıyla dönüştürüldü',
      originalId: id,
      newId: newId,
      conversionDate: now
    });

  } catch (error) {
    // Rollback the transaction on error
    await query('ROLLBACK');
    console.error('Dönüşüm hatası:', error);
    res.status(500).json({ error: 'Dönüşüm işlemi başarısız' });
  }
});

export default router;

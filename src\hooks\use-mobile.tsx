
import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize state with a default value to avoid undefined
  const [isMobile, setIsMobile] = React.useState<boolean>(false)

  React.useEffect(() => {
    // Function to check if screen is mobile
    const checkIsMobile = () => {
      return window.innerWidth < MOBILE_BREAKPOINT
    }
    
    // Set initial value
    setIsMobile(checkIsMobile())
    
    // Create media query listener
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    
    // Create handler for change events
    const onChange = () => {
      setIsMobile(checkIsMobile())
    }
    
    // Modern event listener approach
    if (mql.addEventListener) {
      mql.addEventListener("change", onChange)
    } else {
      // Fallback for older browsers
      mql.addListener(onChange)
    }
    
    // Cleanup function
    return () => {
      if (mql.removeEventListener) {
        mql.removeEventListener("change", onChange)
      } else {
        // Fallback for older browsers
        mql.removeListener(onChange)
      }
    }
  }, [])

  return isMobile
}


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

// Demo data
const bankAccountsData = [
  { id: "1", accountName: "Ana İşletme Hesabı", bank: "Ziraat Bankası", branch: "Merkez", accountNumber: "********", iban: "TR12 0001 2345 6789 1234 5678 90", currency: "TRY", balance: 158700.25, lastUpdate: "2024-07-10" },
  { id: "2", accountName: "Euro Hesabı", bank: "İş Bankası", branch: "Merkez", accountNumber: "********", iban: "TR98 0001 2345 6789 1234 5678 90", currency: "EUR", balance: 42500.75, lastUpdate: "2024-07-10" },
  { id: "3", accountName: "<PERSON><PERSON>ı", bank: "<PERSON><PERSON>ti Bankası", branch: "Merkez", accountNumber: "********", iban: "TR56 0001 2345 6789 1234 5678 90", currency: "USD", balance: 35200.50, lastUpdate: "2024-07-09" },
  { id: "4", accountName: "Personel Maaş Hesabı", bank: "Vakıfbank", branch: "Merkez", accountNumber: "********", iban: "TR43 0001 2345 6789 1234 5678 90", currency: "TRY", balance: 325800.00, lastUpdate: "2024-07-10" }
];

// Column definitions for the bank accounts table
const bankAccountsColumns = [
  {
    header: "Hesap Adı",
    accessorKey: "accountName"
  },
  {
    header: "Banka",
    accessorKey: "bank"
  },
  {
    header: "Şube",
    accessorKey: "branch"
  },
  {
    header: "Hesap No",
    accessorKey: "accountNumber"
  },
  {
    header: "IBAN",
    accessorKey: "iban"
  },
  {
    header: "Para Birimi",
    accessorKey: "currency"
  },
  {
    header: "Bakiye",
    accessorKey: "balance",
    cell: ({ row }) => {
      const currency = row.original.currency;
      const symbol = currency === "TRY" ? "₺" : currency === "USD" ? "$" : currency === "EUR" ? "€" : "";
      return <span className="font-medium">{symbol} {row.original.balance.toLocaleString()}</span>;
    }
  },
  {
    header: "Son Güncelleme",
    accessorKey: "lastUpdate"
  }
];

// Bank statements/transactions data
const bankTransactionsData = [
  { id: "1", accountName: "Ana İşletme Hesabı", date: "2024-07-10", description: "Müşteri Ödemesi", amount: 15000.00, type: "credit" },
  { id: "2", accountName: "Ana İşletme Hesabı", date: "2024-07-09", description: "Tedarikçi Ödemesi", amount: 8750.50, type: "debit" },
  { id: "3", accountName: "Euro Hesabı", date: "2024-07-09", description: "Müşteri Ödemesi", amount: 5000.00, type: "credit" },
  { id: "4", accountName: "Dolar Hesabı", date: "2024-07-08", description: "Transfer", amount: 3500.00, type: "credit" },
  { id: "5", accountName: "Ana İşletme Hesabı", date: "2024-07-08", description: "Kira Ödemesi", amount: 12500.00, type: "debit" },
  { id: "6", accountName: "Personel Maaş Hesabı", date: "2024-07-07", description: "Maaş Transferi", amount: 78500.00, type: "debit" }
];

// Column definitions for bank transactions
const bankTransactionsColumns = [
  {
    header: "Hesap",
    accessorKey: "accountName"
  },
  {
    header: "Tarih",
    accessorKey: "date"
  },
  {
    header: "Açıklama",
    accessorKey: "description"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => {
      const type = row.original.type;
      const className = type === "credit" ? "text-green-600 font-medium" : "text-red-600 font-medium";
      return <span className={className}>₺ {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "İşlem Tipi",
    accessorKey: "type",
    cell: ({ row }) => {
      return row.original.type === "credit" ? "Giriş" : "Çıkış";
    }
  }
];

export const BankManagement = () => {
  const [activeTab, setActiveTab] = useState<"accounts" | "transactions">("accounts");

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={activeTab === "accounts" ? "default" : "outline"}
            onClick={() => setActiveTab("accounts")}
          >
            Banka Hesapları
          </Button>
          <Button 
            variant={activeTab === "transactions" ? "default" : "outline"}
            onClick={() => setActiveTab("transactions")}
          >
            Hesap Hareketleri
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Hesap Ekle
        </Button>
      </div>

      {activeTab === "accounts" ? (
        <Card>
          <CardHeader>
            <CardTitle>Banka Hesapları</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable columns={bankAccountsColumns} data={bankAccountsData} />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Hesap Hareketleri</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable columns={bankTransactionsColumns} data={bankTransactionsData} />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

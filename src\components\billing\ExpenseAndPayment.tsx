
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { DataTable } from "./DataTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import { toast } from "@/hooks/use-toast";

// Demo veriler
const expensesData = [
  { name: "<PERSON><PERSON>", value: 120000 },
  { name: "<PERSON>", value: 45000 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: 28000 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", value: 35000 },
  { name: "<PERSON><PERSON> Giderleri", value: 18000 },
  { name: "<PERSON><PERSON><PERSON>", value: 24000 }
];

const upcomingPayments = [
  { id: "1", supplier: "Ofis Kira", amount: 15000, dueDate: "2024-07-15", status: "Yaklaşan" },
  { id: "2", supplier: "Elektrik Faturası", amount: 3500, dueDate: "2024-07-10", status: "Acil" },
  { id: "3", supplier: "İnternet Aboneliği", amount: 1200, dueDate: "2024-07-18", status: "Yaklaşan" },
  { id: "4", supplier: "Ofis Malzemeleri Ltd.", amount: 2800, dueDate: "2024-07-12", status: "Acil" },
  { id: "5", supplier: "Yazılım Lisansı", amount: 7500, dueDate: "2024-07-25", status: "Planlanan" }
];

const supplierAccountsData = [
  { id: "1", supplier: "ABC Tedarik", balance: 18500, lastPayment: "2024-06-20", lastPaymentAmount: 12000 },
  { id: "2", supplier: "XYZ Dağıtım", balance: 24750, lastPayment: "2024-06-15", lastPaymentAmount: 15000 },
  { id: "3", supplier: "Teknik Servis Ltd.", balance: 5200, lastPayment: "2024-06-25", lastPaymentAmount: 8500 },
  { id: "4", supplier: "Güvenlik Hizmetleri", balance: 11800, lastPayment: "2024-06-18", lastPaymentAmount: 4500 },
  { id: "5", supplier: "Temizlik Şirketi", balance: 3300, lastPayment: "2024-06-22", lastPaymentAmount: 2000 }
];

const pendingApprovals = [
  { id: "1", description: "Marketing Kampanyası", supplier: "Dijital Medya Ltd.", amount: 12500, submittedBy: "Ahmet Yılmaz", submittedDate: "2024-07-05" },
  { id: "2", description: "Bilgisayar Ekipmanları", supplier: "Teknoloji Market", amount: 8750, submittedBy: "Ayşe Demir", submittedDate: "2024-07-04" },
  { id: "3", description: "Eğitim Hizmetleri", supplier: "Eğitim Danışmanlık", amount: 15000, submittedBy: "Mehmet Can", submittedDate: "2024-07-06" }
];

const regularExpenses = [
  { id: "1", description: "Ofis Kirası", amount: 15000, frequency: "Aylık", nextDue: "2024-07-15" },
  { id: "2", description: "Personel Maaşları", amount: 120000, frequency: "Aylık", nextDue: "2024-07-25" },
  { id: "3", description: "İnternet Aboneliği", amount: 1200, frequency: "Aylık", nextDue: "2024-07-18" },
  { id: "4", description: "Yazılım Lisansları", amount: 7500, frequency: "Yıllık", nextDue: "2024-09-10" },
  { id: "5", description: "Araç Bakım", amount: 2500, frequency: "3 Aylık", nextDue: "2024-08-05" }
];

const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"];

// Tablo sütunları
const paymentsColumns = [
  {
    header: "Tedarikçi",
    accessorKey: "supplier"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => <span>₺{row.original.amount.toLocaleString()}</span>
  },
  {
    header: "Vade Tarihi",
    accessorKey: "dueDate"
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => {
      let color = "bg-blue-500";
      if (row.original.status === "Acil") color = "bg-red-500";
      if (row.original.status === "Planlanan") color = "bg-green-500";
      
      return (
        <Badge className={color}>
          {row.original.status}
        </Badge>
      );
    }
  }
];

const supplierAccountsColumns = [
  {
    header: "Tedarikçi",
    accessorKey: "supplier"
  },
  {
    header: "Bakiye",
    accessorKey: "balance",
    cell: ({ row }) => <span>₺{row.original.balance.toLocaleString()}</span>
  },
  {
    header: "Son Ödeme Tarihi",
    accessorKey: "lastPayment"
  },
  {
    header: "Son Ödeme Tutarı",
    accessorKey: "lastPaymentAmount",
    cell: ({ row }) => <span>₺{row.original.lastPaymentAmount.toLocaleString()}</span>
  }
];

const pendingApprovalsColumns = [
  {
    header: "Açıklama",
    accessorKey: "description"
  },
  {
    header: "Tedarikçi",
    accessorKey: "supplier"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => <span>₺{row.original.amount.toLocaleString()}</span>
  },
  {
    header: "Talep Eden",
    accessorKey: "submittedBy"
  },
  {
    header: "Talep Tarihi",
    accessorKey: "submittedDate"
  },
  {
    header: "İşlemler",
    id: "actions",
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 w-8 p-0 text-green-600"
          onClick={() => {
            toast({
              title: "Fatura Onaylandı",
              description: `${row.original.description} faturası onaylandı.`
            });
          }}
        >
          <Check className="h-4 w-4" />
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 w-8 p-0 text-red-600"
          onClick={() => {
            toast({
              title: "Fatura Reddedildi",
              description: `${row.original.description} faturası reddedildi.`
            });
          }}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    )
  }
];

const regularExpensesColumns = [
  {
    header: "Açıklama",
    accessorKey: "description"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => <span>₺{row.original.amount.toLocaleString()}</span>
  },
  {
    header: "Sıklık",
    accessorKey: "frequency"
  },
  {
    header: "Sonraki Vade",
    accessorKey: "nextDue"
  }
];

export const ExpenseAndPayment = () => {
  // Toplam gider hesaplama
  const totalExpenses = expensesData.reduce((sum, item) => sum + item.value, 0);
  const pendingPayments = upcomingPayments.reduce((sum, item) => sum + item.amount, 0);
  const pendingApprovalTotal = pendingApprovals.reduce((sum, item) => sum + item.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gider ve Ödeme Yönetimi</h2>
      </div>

      {/* Özet Kartları */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Toplam Giderler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">₺{totalExpenses.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Yaklaşan Ödemeler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">₺{pendingPayments.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Onay Bekleyen Faturalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">₺{pendingApprovalTotal.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Gider Takibi */}
      <Card>
        <CardHeader>
          <CardTitle>Gider Dağılımı</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={expensesData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                >
                  {expensesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`₺${value.toLocaleString()}`, "Gider Tutarı"]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Yaklaşan Ödemeler */}
      <Card>
        <CardHeader>
          <CardTitle>Tedarikçi Ödemeleri</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={paymentsColumns} data={upcomingPayments} />
        </CardContent>
      </Card>

      {/* Tedarikçi Cari Hesapları */}
      <Card>
        <CardHeader>
          <CardTitle>Tedarikçi Cari Hesapları</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={supplierAccountsColumns} data={supplierAccountsData} />
        </CardContent>
      </Card>

      {/* Gider Faturası Onay Süreci */}
      <Card>
        <CardHeader>
          <CardTitle>Onay Bekleyen Gider Faturaları</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={pendingApprovalsColumns} data={pendingApprovals} />
        </CardContent>
      </Card>

      {/* Düzenli Gider Takibi */}
      <Card>
        <CardHeader>
          <CardTitle>Düzenli Giderler</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={regularExpensesColumns} data={regularExpenses} />
        </CardContent>
      </Card>
    </div>
  );
};

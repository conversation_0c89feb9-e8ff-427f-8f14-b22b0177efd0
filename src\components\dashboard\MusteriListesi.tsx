
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, FileText, Phone, Mail, RefreshCw } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { cariService } from '@/services/cariService';
import { CariHesap } from '@/types/cari';
import { toast } from 'sonner';

export const MusteriListesi = () => {
  const [musteriler, setMusteriler] = useState<CariHesap[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchMusteriler = async () => {
    try {
      setLoading(true);
      const data = await cariService.getAll();
      // En fazla 5 müşteri göster
      setMusteriler(data.slice(0, 5));
      setError(null);
    } catch (err) {
      console.error('Müşteri verileri alınırken hata oluştu:', err);
      setError('Müşteri verileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchMusteriler();
    } catch (error) {
      toast.error("Müşteri listesi yenilenirken hata oluştu");
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchMusteriler();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Müşteri Listesi</CardTitle>
        <Button 
          variant="outline" 
          size="icon" 
          onClick={handleRefresh} 
          disabled={refreshing || loading}
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
            {error}
          </div>
        )}
        
        {loading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((n) => (
              <div key={n} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Kod</TableHead>
                  <TableHead>Unvan</TableHead>
                  <TableHead>VKN/TCKN</TableHead>
                  <TableHead>Tür</TableHead>
                  <TableHead>İletişim</TableHead>
                  <TableHead>Bakiye</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {musteriler.map((musteri) => (
                  <TableRow key={musteri.id}>
                    <TableCell className="font-medium">{musteri.kod}</TableCell>
                    <TableCell>{musteri.unvan}</TableCell>
                    <TableCell>{musteri.vknTckn}</TableCell>
                    <TableCell>
                      <Badge variant={musteri.tip?.includes("Alıcı") ? 'outline' : 'secondary'}>
                        {musteri.tip?.includes("Alıcı") ? 'Alıcı' : 'Satıcı'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {musteri.telefon && (
                          <Button variant="ghost" size="icon" title={musteri.telefon}>
                            <Phone className="h-4 w-4" />
                          </Button>
                        )}
                        {musteri.email && (
                          <Button variant="ghost" size="icon" title={musteri.email}>
                            <Mail className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className={parseFloat(String(musteri.bakiye || 0)) >= 0 ? 'text-green-600' : 'text-red-600'}>
                      {parseFloat(String(musteri.bakiye || 0)).toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" title="Detaylar">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" title="Ekstre">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                
                {musteriler.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                      Müşteri kaydı bulunamadı.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};


import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";

// Define types for our authentication system
export type UserRole = "admin" | "company_admin" | "user";

export interface CompanyType {
  id: number;
  name: string;
  code: string;
  active: boolean;
  modules: string[];
  optionalModules: string[];
  type: string;
}

export interface UserType {
  id: number;
  name: string;
  role: string;
  email: string;
  avatar: string;
  companyId?: number;
  permissions: string[];
}

// New interface for login logs
export interface LoginLogType {
  userId: number;
  userName: string;
  companyName: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: UserType | null;
  company: CompanyType | null;
  loginLogs: LoginLogType[];
  login: (companyCode: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (user: UserType) => void;
  updateCompany: (company: CompanyType) => void;
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  company: null,
  loginLogs: [],
  login: async () => false,
  logout: () => {},
  updateUser: () => {},
  updateCompany: () => {},
});

// Initial admin user and company data
const initialCompanies: CompanyType[] = [
  { 
    id: 1, 
    name: "Admin", 
    code: "12628313332", 
    active: true, 
    modules: ["all"], 
    optionalModules: [], 
    type: "Admin" 
  },
  { 
    id: 2, 
    name: "Teknoloji A.Ş.", 
    code: "10001", 
    active: true, 
    modules: ["finans", "stok", "insan_kaynaklari", "musteri"], 
    optionalModules: ["uretim", "proje"], 
    type: "Kurumsal" 
  },
  { 
    id: 3, 
    name: "Yazılım Ltd. Şti.", 
    code: "10002", 
    active: true, 
    modules: ["finans", "stok", "musteri"], 
    optionalModules: ["proje", "satis"], 
    type: "KOBİ" 
  },
  { 
    id: 4, 
    name: "Bormeg A.Ş.", 
    code: "bormeg", 
    active: true, 
    modules: ["uretim"], 
    optionalModules: [], 
    type: "Özel" 
  },
  { 
    id: 5, 
    name: "Muhasebe Şirketi", 
    code: "muhasebe", 
    active: true, 
    modules: ["finans"], 
    optionalModules: [], 
    type: "Muhasebe" 
  },
];

const initialUsers: UserType[] = [
  { 
    id: 1, 
    name: "Mehmet Aşık", 
    role: "Admin", 
    email: "<EMAIL>", 
    avatar: "MA", 
    permissions: ["all"] 
  },
  { 
    id: 2, 
    name: "Ayşe Demir", 
    role: "Müdür", 
    email: "<EMAIL>", 
    avatar: "AD", 
    companyId: 2, 
    permissions: ["finans", "stok", "insan_kaynaklari", "uretim"] 
  },
  { 
    id: 3, 
    name: "Can Yılmaz", 
    role: "Muhasebeci", 
    email: "<EMAIL>", 
    avatar: "CY", 
    companyId: 2, 
    permissions: ["finans", "stok"] 
  },
  { 
    id: 4, 
    name: "Bormeg Yönetici", 
    role: "Müdür", 
    email: "<EMAIL>", 
    avatar: "BY", 
    companyId: 4, 
    permissions: ["uretim"] 
  },
  { 
    id: 5, 
    name: "Şenay Sever", 
    role: "Muhasebeci", 
    email: "<EMAIL>", 
    avatar: "ŞS", 
    companyId: 5, 
    permissions: ["finans"] 
  }
];

// Create provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<UserType | null>(null);
  const [company, setCompany] = useState<CompanyType | null>(null);
  const [companies, setCompanies] = useState<CompanyType[]>(initialCompanies);
  const [users, setUsers] = useState<UserType[]>(initialUsers);
  const [loginLogs, setLoginLogs] = useState<LoginLogType[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if user is already logged in (using localStorage)
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    const storedCompany = localStorage.getItem("company");
    const storedLogs = localStorage.getItem("loginLogs");
    
    if (storedUser && storedCompany) {
      setUser(JSON.parse(storedUser));
      setCompany(JSON.parse(storedCompany));
      setIsAuthenticated(true);
    }

    if (storedLogs) {
      setLoginLogs(JSON.parse(storedLogs));
    }
  }, []);

  // Add log entry function
  const addLoginLog = (userId: number, userName: string, companyName: string) => {
    const ipAddress = "192.168.1." + Math.floor(Math.random() * 255); // Simulate IP address in development
    const newLog: LoginLogType = {
      userId,
      userName,
      companyName,
      timestamp: new Date().toISOString(),
      ipAddress,
      userAgent: navigator.userAgent
    };

    const updatedLogs = [...loginLogs, newLog];
    setLoginLogs(updatedLogs);
    localStorage.setItem("loginLogs", JSON.stringify(updatedLogs));
  };

  // Login function
  const login = async (companyCode: string, password: string): Promise<boolean> => {
    // Special case for muhasebe user
    if (companyCode === "muhasebe0102" && password === "Senay071124") {
      const muhasebeCompany = initialCompanies.find(c => c.id === 5);
      const muhasebeUser = initialUsers.find(u => u.id === 5);
      
      if (muhasebeCompany && muhasebeUser) {
        setUser(muhasebeUser);
        setCompany(muhasebeCompany);
        setIsAuthenticated(true);
        
        localStorage.setItem("user", JSON.stringify(muhasebeUser));
        localStorage.setItem("company", JSON.stringify(muhasebeCompany));
        
        // Add login log
        addLoginLog(muhasebeUser.id, muhasebeUser.name, muhasebeCompany.name);
        
        toast({
          title: "Giriş Başarılı",
          description: `Hoş geldiniz, ${muhasebeUser.name}`,
        });
        
        navigate("/finans");
        return true;
      }
    }
    
    // Special case for Bormeg
    if (companyCode === "bormeg" && password === "Bor+admin") {
      const bormegCompany = companies.find(c => c.code === "bormeg");
      const bormegUser = users.find(u => u.companyId === 4);
      
      if (bormegCompany && bormegUser) {
        setUser(bormegUser);
        setCompany(bormegCompany);
        setIsAuthenticated(true);
        
        localStorage.setItem("user", JSON.stringify(bormegUser));
        localStorage.setItem("company", JSON.stringify(bormegCompany));
        
        // Add login log
        addLoginLog(bormegUser.id, bormegUser.name, bormegCompany.name);
        
        toast({
          title: "Giriş Başarılı",
          description: `Hoş geldiniz, ${bormegUser.name}`,
        });
        
        return true;
      }
    }
    
    // Regular login logic
    // Simple password validation (in real app, this should be handled securely on server)
    if (password !== "password") {
      toast({
        title: "Giriş Başarısız",
        description: "Şirket kodu veya şifre hatalı",
        variant: "destructive",
      });
      return false;
    }

    // Find company by code
    const foundCompany = companies.find(c => c.code === companyCode && c.active);
    
    if (!foundCompany) {
      toast({
        title: "Giriş Başarısız",
        description: "Geçersiz şirket kodu veya şirket aktif değil",
        variant: "destructive",
      });
      return false;
    }

    // Find user associated with this company (for company we get the manager)
    let foundUser: UserType | undefined;
    
    if (foundCompany.id === 1) {
      // This is the admin account
      foundUser = users.find(u => u.id === 1);
    } else {
      // For other companies, find the manager
      foundUser = users.find(u => u.role === "Müdür" && u.companyId === foundCompany.id);
    }

    if (!foundUser) {
      toast({
        title: "Giriş Başarısız",
        description: "Bu şirket için kullanıcı bulunamadı",
        variant: "destructive",
      });
      return false;
    }

    // Set authentication state
    setUser(foundUser);
    setCompany(foundCompany);
    setIsAuthenticated(true);

    // Store in localStorage
    localStorage.setItem("user", JSON.stringify(foundUser));
    localStorage.setItem("company", JSON.stringify(foundCompany));

    // Add login log
    addLoginLog(foundUser.id, foundUser.name, foundCompany.name);

    toast({
      title: "Giriş Başarılı",
      description: `Hoş geldiniz, ${foundUser.name}`,
    });

    return true;
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setCompany(null);
    setIsAuthenticated(false);
    localStorage.removeItem("user");
    localStorage.removeItem("company");
    navigate("/login");
    
    toast({
      title: "Çıkış Yapıldı",
      description: "Başarıyla çıkış yaptınız",
    });
  };

  // Update user data
  const updateUser = (updatedUser: UserType) => {
    setUser(updatedUser);
    localStorage.setItem("user", JSON.stringify(updatedUser));
    
    // Also update in users array
    setUsers(prev => prev.map(u => u.id === updatedUser.id ? updatedUser : u));
  };

  // Update company data
  const updateCompany = (updatedCompany: CompanyType) => {
    setCompany(updatedCompany);
    localStorage.setItem("company", JSON.stringify(updatedCompany));
    
    // Also update in companies array
    setCompanies(prev => prev.map(c => c.id === updatedCompany.id ? updatedCompany : c));
  };

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      user, 
      company, 
      loginLogs,
      login, 
      logout, 
      updateUser, 
      updateCompany 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

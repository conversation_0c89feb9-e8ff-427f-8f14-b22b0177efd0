
import { Sidebar, <PERSON>bar<PERSON>ontent, <PERSON>barProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarUserProfile } from "./SidebarUserProfile";
import { SidebarModulesSection } from "./SidebarModulesSection";
import { ThemeToggle } from "./ThemeToggle";

const MainLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-blue-50 to-slate-50 dark:from-slate-900 dark:to-blue-950">
        <Sidebar className="glass-panel border-r dark:bg-slate-900/70 dark:border-slate-800">
          <SidebarContent>
            <div className="p-4">
              <h1 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">ERP System</h1>
            </div>

            <SidebarUserProfile />
            <SidebarModulesSection />
          </SidebarContent>
        </Sidebar>

        <main className="flex-1 p-4 dark:bg-slate-900 dark:text-white">
          <div className="flex items-center justify-between mb-4">
            <SidebarTrigger>
              <Button variant="ghost" size="icon" className="hover:bg-blue-100 dark:hover:bg-blue-900">
                <Menu className="h-5 w-5" />
              </Button>
            </SidebarTrigger>

            <div className="flex items-center gap-2">
              <ThemeToggle />
            </div>
          </div>
          {children}
        </main>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;

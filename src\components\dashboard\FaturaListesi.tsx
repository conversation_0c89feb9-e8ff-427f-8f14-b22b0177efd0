
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Download, Send } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { faturaService } from '@/services/apiService';

interface Fatura {
  id: string;
  numara: string;
  cari_unvan: string;
  para_birimi: string;
  toplam: number;
  durum: string;
  vade_tarihi: string;
  olusturma_tarihi: string;
}

export const FaturaListesi = () => {
  const [faturalar, setFaturalar] = useState<Fatura[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFaturalar = async () => {
      try {
        setLoading(true);
        const data = await faturaService.getAll();
        setFaturalar(data);
        setError(null);
      } catch (err) {
        console.error('Fatura verileri alınırken hata oluştu:', err);
        setError('Fatura verileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      } finally {
        setLoading(false);
      }
    };

    fetchFaturalar();
  }, []);

  const getDurumBadge = (durum: string) => {
    switch (durum) {
      case 'odendi':
        return <Badge className="bg-green-100 text-green-800">Ödendi</Badge>;
      case 'bekliyor':
        return <Badge className="bg-yellow-100 text-yellow-800">Bekliyor</Badge>;
      case 'iptal':
        return <Badge className="bg-red-100 text-red-800">İptal</Badge>;
      default:
        return <Badge variant="outline">{durum}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fatura Listesi</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
            {error}
          </div>
        )}
        
        {loading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((n) => (
              <div key={n} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Fatura No</TableHead>
                  <TableHead>Müşteri</TableHead>
                  <TableHead>Toplam</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Vade Tarihi</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {faturalar.map((fatura) => (
                  <TableRow key={fatura.id}>
                    <TableCell className="font-medium">{fatura.numara}</TableCell>
                    <TableCell>{fatura.cari_unvan}</TableCell>
                    <TableCell>
                      {fatura.toplam.toLocaleString('tr-TR', { style: 'currency', currency: fatura.para_birimi })}
                    </TableCell>
                    <TableCell>{getDurumBadge(fatura.durum)}</TableCell>
                    <TableCell>{new Date(fatura.vade_tarihi).toLocaleDateString('tr-TR')}</TableCell>
                    <TableCell>{new Date(fatura.olusturma_tarihi).toLocaleDateString('tr-TR')}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" title="Detaylar">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" title="İndir">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" title="Gönder">
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                
                {faturalar.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                      Fatura kaydı bulunamadı.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

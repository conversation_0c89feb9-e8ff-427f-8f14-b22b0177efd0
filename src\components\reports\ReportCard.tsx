
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Table } from "lucide-react";

interface ReportCardProps {
  title: string;
  description: string;
  icon: string;
  children: React.ReactNode;
}

export const ReportCard = ({ title, description, icon, children }: ReportCardProps) => {
  // Map icon string to actual icon component
  const getIcon = () => {
    switch (icon) {
      case "chart-bar":
        return <BarChart className="h-4 w-4 text-muted-foreground" />;
      case "chart-line":
        return <LineChart className="h-4 w-4 text-muted-foreground" />;
      case "chart-pie":
        return <PieChart className="h-4 w-4 text-muted-foreground" />;
      case "table":
        return <Table className="h-4 w-4 text-muted-foreground" />;
      default:
        return <BarChart className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          {getIcon()}
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};

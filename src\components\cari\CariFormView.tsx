
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CariBasicInfoSection } from "./CariBasicInfoSection";
import { CariTaxInfoSection } from "./CariTaxInfoSection";
import { CariAddressSection } from "./CariAddressSection";
import { CariNotesSection } from "./CariNotesSection";
import { CariHesap } from "@/types/cari";
import { cariService } from "@/services/cariService";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface CariFormViewProps {
  onCancel: () => void;
  onSuccess?: () => void;
  initialData?: Partial<CariHesap>;
}

export const CariFormView = ({ onCancel, onSuccess, initialData }: CariFormViewProps) => {
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<Partial<CariHesap>>(initialData || {
    kod: "",
    unvan: "",
    vknTckn: "",
    tip: "120-Alıcı",
    telefon: "",
    email: "",
    bakiye: 0,
    kayitTarihi: new Date().toISOString().split('T')[0]
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const form = useForm<CariHesap>({
    defaultValues: initialData || {
      kod: "",
      unvan: "",
      vknTckn: "",
      tip: "120-Alıcı",
      telefon: "",
      email: "",
      bakiye: 0,
      kayitTarihi: new Date().toISOString().split('T')[0]
    }
  });
  
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      if (initialData?.id) {
        // Güncelleme işlemi
        await cariService.update(initialData.id, formData);
        toast.success("Cari hesap başarıyla güncellendi");
      } else {
        // Yeni kayıt işlemi
        await cariService.create(formData as CariHesap);
        toast.success("Cari hesap başarıyla oluşturuldu");
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Form gönderme hatası:", error);
      toast.error("Cari hesap kaydedilirken bir hata oluştu");
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSubmit();
    }}>
      <Card>
        <CardContent className="p-6 space-y-6">
          <CariBasicInfoSection 
            formData={formData} 
            updateFormData={updateFormData}
            errors={errors}
          />
          
          <CariTaxInfoSection 
            formData={formData} 
            updateFormData={updateFormData}
            errors={errors}
          />
          
          <CariAddressSection 
            formData={formData} 
            updateFormData={updateFormData}
            errors={errors}
          />
          
          <CariNotesSection 
            formData={formData} 
            updateFormData={updateFormData}
          />
          
          <div className="flex justify-end gap-2 pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={submitting}
            >
              İptal
            </Button>
            <Button 
              type="submit"
              disabled={submitting} 
            >
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {initialData?.id ? "Güncelle" : "Kaydet"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
};

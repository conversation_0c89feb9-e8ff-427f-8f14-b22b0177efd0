import { useLocation, useNavigate } from "react-router-dom";
import { 
  Layers, Database, PlusCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

interface HammaddeSubMenuProps {
  openHammaddeMenu: boolean;
  setOpenHammaddeMenu: (open: boolean) => void;
  iconColor: string;
}

interface HammaddeMenuItem {
  id: string;
  title: string;
  path: string;
  icon: React.ReactNode;
  color: string;
}

export const HammaddeSubMenu = ({ 
  openHammaddeMenu, 
  setOpenHammaddeMenu, 
  iconColor 
}: HammaddeSubMenuProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Check if we're on a hammadde page
  const isHammaddePath = location.pathname.startsWith('/hammadde');

  // Define hammadde menu items
  const hammaddeMenuItems: HammaddeMenuItem[] = [
    {
      id: "veriler",
      title: "Veriler",
      path: "/hammadde/veriler",
      icon: <Database className="h-4 w-4 text-emerald-500" />,
      color: "text-emerald-500",
    },
    {
      id: "stok-girisi",
      title: "Stok Girişi",
      path: "/hammadde/stok-girisi",
      icon: <PlusCircle className="h-4 w-4 text-teal-500" />,
      color: "text-teal-500",
    },
  ];

  // Handle menu item click
  const handleMenuItemClick = (path: string) => {
    navigate(path);
  };

  // Auto-open menu when on hammadde page
  useEffect(() => {
    if (isHammaddePath && !openHammaddeMenu) {
      setOpenHammaddeMenu(true);
    }
  }, [isHammaddePath, openHammaddeMenu, setOpenHammaddeMenu]);

  return (
    <div>
      <Button
        variant={isHammaddePath ? "secondary" : "ghost"}
        className={`w-full justify-between gap-2 mb-1 ${isHammaddePath ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-200' : ''}`}
        onClick={() => setOpenHammaddeMenu(!openHammaddeMenu)}
      >
        <div className="flex items-center gap-2">
          <Layers className={`h-4 w-4 ${iconColor || 'text-emerald-600'}`} />
          HAMMADDE
        </div>
        {openHammaddeMenu ? 
          <ChevronDown className="h-4 w-4" /> : 
          <ChevronRight className="h-4 w-4" />
        }
      </Button>
      
      {openHammaddeMenu && (
        <div className="pl-3 pr-1 py-1 space-y-1">
          {hammaddeMenuItems.map((item) => {
            const isActive = location.pathname.includes(item.path);
            return (
              <Button
                key={item.id}
                variant="ghost"
                size="sm"
                className={`w-full justify-start gap-2 ${isActive ? 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-200' : ''}`}
                onClick={() => handleMenuItemClick(item.path)}
              >
                {item.icon}
                <span className="text-sm">{item.title}</span>
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );
};


import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useVehicleStore } from "@/stores/vehicleStore";
import { Driver } from "@/stores/vehicleStore";

export function CreateDriverDialog() {
  const [open, setOpen] = useState(false);
  const addDriver = useVehicleStore((state) => state.addDriver);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const driver: Omit<Driver, 'id' | 'createdAt'> = {
      name: formData.get('name') as string,
      licenseNumber: formData.get('licenseNumber') as string,
      licenseExpiry: new Date(formData.get('licenseExpiry') as string).toISOString(),
      contactNumber: formData.get('contactNumber') as string,
      email: formData.get('email') as string,
      department: formData.get('department') as string,
      totalDistance: 0,
      totalTrips: 0,
      status: 'active',
      performanceRating: 5,
      notes: formData.get('notes') as string,
    };

    addDriver(driver);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Yeni Sürücü Ekle</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Yeni Sürücü Ekle</DialogTitle>
            <DialogDescription>
              Yeni bir sürücü eklemek için aşağıdaki bilgileri doldurun.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Ad Soyad
              </Label>
              <Input id="name" name="name" className="col-span-3" required />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="licenseNumber" className="text-right">
                Ehliyet No
              </Label>
              <Input id="licenseNumber" name="licenseNumber" className="col-span-3" required />
            </div>
            {/* Additional fields */}
          </div>
          <DialogFooter>
            <Button type="submit">Kaydet</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}


import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Download, ChartBar, Filter, CalendarIcon, FileSpreadsheet, ArrowUpDown } from "lucide-react";
import { ResponsiveContainer, LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";
import { SummaryReports } from "./SummaryReports";

export const FinancialReports = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-medium">Finansal Raporlar</h3>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4" />
            <span>Son 6 Ay</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span>Filtrele</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            <span>Excel'e Aktar</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span>PDF İndir</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <ArrowUpDown className="h-4 w-4" />
            <span>Sırala</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="summary" className="space-y-4">
        <TabsList>
          <TabsTrigger value="summary">Özet Rapor</TabsTrigger>
          <TabsTrigger value="detailed">Detaylı Analiz</TabsTrigger>
          <TabsTrigger value="taxes">Vergi Raporları</TabsTrigger>
          <TabsTrigger value="cash-flow">Nakit Akışı</TabsTrigger>
          <TabsTrigger value="comparison">Karşılaştırmalı Analiz</TabsTrigger>
        </TabsList>
        
        <TabsContent value="summary" className="space-y-4">
          <SummaryReports />
        </TabsContent>
        
        <TabsContent value="detailed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detaylı Finansal Analiz</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Ciro Analizi</h4>
                    <p className="text-muted-foreground mb-4">Aylık ciro değişimi ve yıllık karşılaştırma</p>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: "Ocak", ciro: 65000, geçenyıl: 58000 },
                            { name: "Şubat", ciro: 72000, geçenyıl: 63000 },
                            { name: "Mart", ciro: 68000, geçenyıl: 65000 },
                            { name: "Nisan", ciro: 75000, geçenyıl: 68000 },
                            { name: "Mayıs", ciro: 82000, geçenyıl: 72000 },
                            { name: "Haziran", ciro: 90000, geçenyıl: 78000 },
                          ]}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="ciro" name="Bu Yıl" fill="#8884d8" />
                          <Bar dataKey="geçenyıl" name="Geçen Yıl" fill="#82ca9d" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Kategori Bazlı Gelir Dağılımı</h4>
                    <p className="text-muted-foreground mb-4">Gelirlerin ürün/hizmet kategorilerine göre dağılımı</p>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: "Ürün A", value: 40 },
                              { name: "Ürün B", value: 25 },
                              { name: "Hizmet C", value: 20 },
                              { name: "Hizmet D", value: 15 },
                            ]}
                            cx="50%"
                            cy="50%"
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({name, percent}) => `${name}: %${(percent * 100).toFixed(0)}`}
                          >
                            <Cell fill="#8884d8" />
                            <Cell fill="#82ca9d" />
                            <Cell fill="#ffc658" />
                            <Cell fill="#ff8042" />
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="text-lg font-medium mb-2">Aylık Gider Dağılımı</h4>
                  <p className="text-muted-foreground mb-4">Kategorilere göre aylık gider dağılımı</p>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-muted">
                          <th className="border p-2 text-left">Kategori</th>
                          <th className="border p-2 text-right">Ocak</th>
                          <th className="border p-2 text-right">Şubat</th>
                          <th className="border p-2 text-right">Mart</th>
                          <th className="border p-2 text-right">Nisan</th>
                          <th className="border p-2 text-right">Mayıs</th>
                          <th className="border p-2 text-right">Haziran</th>
                          <th className="border p-2 text-right">Toplam</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">Personel</td>
                          <td className="border p-2 text-right">₺15,800</td>
                          <td className="border p-2 text-right">₺15,800</td>
                          <td className="border p-2 text-right">₺16,200</td>
                          <td className="border p-2 text-right">₺16,200</td>
                          <td className="border p-2 text-right">₺16,500</td>
                          <td className="border p-2 text-right">₺16,500</td>
                          <td className="border p-2 text-right font-medium">₺97,000</td>
                        </tr>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">Ofis Giderleri</td>
                          <td className="border p-2 text-right">₺8,500</td>
                          <td className="border p-2 text-right">₺8,200</td>
                          <td className="border p-2 text-right">₺8,300</td>
                          <td className="border p-2 text-right">₺8,100</td>
                          <td className="border p-2 text-right">₺8,400</td>
                          <td className="border p-2 text-right">₺8,200</td>
                          <td className="border p-2 text-right font-medium">₺49,700</td>
                        </tr>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">Pazarlama</td>
                          <td className="border p-2 text-right">₺5,200</td>
                          <td className="border p-2 text-right">₺6,500</td>
                          <td className="border p-2 text-right">₺5,800</td>
                          <td className="border p-2 text-right">₺7,200</td>
                          <td className="border p-2 text-right">₺6,800</td>
                          <td className="border p-2 text-right">₺7,500</td>
                          <td className="border p-2 text-right font-medium">₺39,000</td>
                        </tr>
                        <tr className="bg-muted font-medium">
                          <td className="border p-2">Toplam</td>
                          <td className="border p-2 text-right">₺29,500</td>
                          <td className="border p-2 text-right">₺30,500</td>
                          <td className="border p-2 text-right">₺30,300</td>
                          <td className="border p-2 text-right">₺31,500</td>
                          <td className="border p-2 text-right">₺31,700</td>
                          <td className="border p-2 text-right">₺32,200</td>
                          <td className="border p-2 text-right">₺185,700</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="taxes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vergi Raporları</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">KDV Özeti</h4>
                    <p className="text-2xl font-bold">₺42,850</p>
                    <p className="text-sm text-muted-foreground">Son 6 ay</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">Gelir Vergisi</h4>
                    <p className="text-2xl font-bold">₺36,250</p>
                    <p className="text-sm text-muted-foreground">Son 6 ay</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">Diğer Vergiler</h4>
                    <p className="text-2xl font-bold">₺12,400</p>
                    <p className="text-sm text-muted-foreground">Son 6 ay</p>
                  </Card>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h4 className="text-lg font-medium mb-4">Vergi Takvimi</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-muted">
                          <th className="border p-2 text-left">Vergi Türü</th>
                          <th className="border p-2 text-left">Dönem</th>
                          <th className="border p-2 text-left">Son Ödeme Tarihi</th>
                          <th className="border p-2 text-right">Tutar</th>
                          <th className="border p-2 text-center">Durum</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">KDV</td>
                          <td className="border p-2">Haziran 2023</td>
                          <td className="border p-2">26.07.2023</td>
                          <td className="border p-2 text-right">₺7,850</td>
                          <td className="border p-2 text-center">
                            <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs">Ödendi</span>
                          </td>
                        </tr>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">Gelir Vergisi</td>
                          <td className="border p-2">2. Çeyrek 2023</td>
                          <td className="border p-2">31.07.2023</td>
                          <td className="border p-2 text-right">₺12,450</td>
                          <td className="border p-2 text-center">
                            <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800 text-xs">Bekliyor</span>
                          </td>
                        </tr>
                        <tr className="hover:bg-muted/50">
                          <td className="border p-2">Damga Vergisi</td>
                          <td className="border p-2">Temmuz 2023</td>
                          <td className="border p-2">23.08.2023</td>
                          <td className="border p-2 text-right">₺1,240</td>
                          <td className="border p-2 text-center">
                            <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs">Yaklaşıyor</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="cash-flow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Nakit Akışı Raporu</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={[
                        { name: "Pazartesi", gelir: 12500, gider: 8500, bakiye: 4000 },
                        { name: "Salı", gelir: 9800, gider: 7200, bakiye: 6600 },
                        { name: "Çarşamba", gelir: 15400, gider: 9800, bakiye: 12200 },
                        { name: "Perşembe", gelir: 8200, gider: 10500, bakiye: 9900 },
                        { name: "Cuma", gelir: 18500, gider: 12000, bakiye: 16400 },
                        { name: "Cumartesi", gelir: 6500, gider: 3500, bakiye: 19400 },
                        { name: "Pazar", gelir: 2000, gider: 1200, bakiye: 20200 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip 
                        formatter={(value) => [`₺${value.toLocaleString()}`, undefined]} 
                      />
                      <Legend />
                      <Line type="monotone" dataKey="gelir" name="Gelir" stroke="#8884d8" />
                      <Line type="monotone" dataKey="gider" name="Gider" stroke="#ff8042" />
                      <Line type="monotone" dataKey="bakiye" name="Bakiye" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">Toplam Gelir</h4>
                    <p className="text-2xl font-bold text-green-600">₺72,900</p>
                    <p className="text-sm text-muted-foreground">Son 7 gün</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">Toplam Gider</h4>
                    <p className="text-2xl font-bold text-red-600">₺52,700</p>
                    <p className="text-sm text-muted-foreground">Son 7 gün</p>
                  </Card>
                  <Card className="p-4">
                    <h4 className="font-medium mb-1">Net Bakiye</h4>
                    <p className="text-2xl font-bold text-blue-600">₺20,200</p>
                    <p className="text-sm text-muted-foreground">Son 7 gün</p>
                  </Card>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <Card className="p-4 overflow-auto h-80">
                    <h4 className="text-lg font-medium mb-4">Gelecek Nakit Akışı Tahmini</h4>
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-2">Tarih</th>
                          <th className="pb-2">Tahsilat</th>
                          <th className="pb-2">Ödeme</th>
                          <th className="pb-2">Net</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        <tr>
                          <td className="py-3">25.07.2023</td>
                          <td className="py-3 text-green-600">₺15,800</td>
                          <td className="py-3 text-red-600">₺8,400</td>
                          <td className="py-3 font-medium">₺7,400</td>
                        </tr>
                        <tr>
                          <td className="py-3">01.08.2023</td>
                          <td className="py-3 text-green-600">₺22,500</td>
                          <td className="py-3 text-red-600">₺18,200</td>
                          <td className="py-3 font-medium">₺4,300</td>
                        </tr>
                        <tr>
                          <td className="py-3">08.08.2023</td>
                          <td className="py-3 text-green-600">₺18,400</td>
                          <td className="py-3 text-red-600">₺14,500</td>
                          <td className="py-3 font-medium">₺3,900</td>
                        </tr>
                        <tr>
                          <td className="py-3">15.08.2023</td>
                          <td className="py-3 text-green-600">₺24,200</td>
                          <td className="py-3 text-red-600">₺16,800</td>
                          <td className="py-3 font-medium">₺7,400</td>
                        </tr>
                      </tbody>
                      <tfoot className="border-t">
                        <tr>
                          <td className="pt-3 font-medium">Toplam</td>
                          <td className="pt-3 text-green-600 font-medium">₺80,900</td>
                          <td className="pt-3 text-red-600 font-medium">₺57,900</td>
                          <td className="pt-3 font-bold">₺23,000</td>
                        </tr>
                      </tfoot>
                    </table>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="text-lg font-medium mb-4">Gecikmeli Tahsilatlar</h4>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div>
                          <p className="font-medium">ABC Şirketi</p>
                          <p className="text-sm text-muted-foreground">Fatura #INV-2023-142</p>
                          <p className="text-xs text-red-600">15 gün gecikmiş</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">₺12,450</p>
                          <Button size="sm" variant="outline" className="mt-1">Hatırlat</Button>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div>
                          <p className="font-medium">XYZ Ltd. Şti.</p>
                          <p className="text-sm text-muted-foreground">Fatura #INV-2023-128</p>
                          <p className="text-xs text-red-600">8 gün gecikmiş</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">₺8,750</p>
                          <Button size="sm" variant="outline" className="mt-1">Hatırlat</Button>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center p-3 border rounded-md">
                        <div>
                          <p className="font-medium">DEF Holding</p>
                          <p className="text-sm text-muted-foreground">Fatura #INV-2023-135</p>
                          <p className="text-xs text-red-600">5 gün gecikmiş</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">₺15,200</p>
                          <Button size="sm" variant="outline" className="mt-1">Hatırlat</Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Karşılaştırmalı Analiz</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex gap-4 items-center">
                  <Button variant="outline" className="flex-1">Bu Ay vs Geçen Ay</Button>
                  <Button variant="outline" className="flex-1">Bu Çeyrek vs Geçen Çeyrek</Button>
                  <Button variant="outline" className="flex-1">Bu Yıl vs Geçen Yıl</Button>
                </div>
                
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { kategori: "Gelir", period1: 452000, period2: 380000 },
                        { kategori: "Gider", period1: 315000, period2: 290000 },
                        { kategori: "Kâr", period1: 137000, period2: 90000 },
                        { kategori: "Vergi", period1: 42800, period2: 36000 },
                        { kategori: "Net Kâr", period1: 94200, period2: 54000 },
                      ]}
                      layout="vertical"
                      margin={{ top: 20, right: 30, left: 60, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" 
                        tickFormatter={(value) => `₺${value/1000}K`}
                      />
                      <YAxis dataKey="kategori" type="category" />
                      <Tooltip 
                        formatter={(value) => [`₺${value.toLocaleString()}`, undefined]} 
                      />
                      <Legend />
                      <Bar dataKey="period1" name="Bu Yıl" fill="#8884d8" />
                      <Bar dataKey="period2" name="Geçen Yıl" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <Card className="p-4">
                    <h4 className="text-lg font-medium mb-4">Büyüme Analizi</h4>
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-2">Metrik</th>
                          <th className="pb-2 text-right">Değişim</th>
                          <th className="pb-2 text-right">Oran</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        <tr>
                          <td className="py-3">Gelir</td>
                          <td className="py-3 text-right">₺72,000</td>
                          <td className="py-3 text-right text-green-600">+18.9%</td>
                        </tr>
                        <tr>
                          <td className="py-3">Gider</td>
                          <td className="py-3 text-right">₺25,000</td>
                          <td className="py-3 text-right text-red-600">+8.6%</td>
                        </tr>
                        <tr>
                          <td className="py-3">Kâr</td>
                          <td className="py-3 text-right">₺47,000</td>
                          <td className="py-3 text-right text-green-600">+52.2%</td>
                        </tr>
                        <tr>
                          <td className="py-3">Vergi</td>
                          <td className="py-3 text-right">₺6,800</td>
                          <td className="py-3 text-right text-red-600">+18.9%</td>
                        </tr>
                        <tr>
                          <td className="py-3">Net Kâr</td>
                          <td className="py-3 text-right">₺40,200</td>
                          <td className="py-3 text-right text-green-600">+74.4%</td>
                        </tr>
                      </tbody>
                    </table>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="text-lg font-medium mb-4">Kâr Marjı Analizi</h4>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span>Brüt Kâr Marjı (Bu Yıl)</span>
                          <span className="font-medium">30.3%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: "30.3%" }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span>Brüt Kâr Marjı (Geçen Yıl)</span>
                          <span className="font-medium">23.7%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "23.7%" }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span>Net Kâr Marjı (Bu Yıl)</span>
                          <span className="font-medium">20.8%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-purple-600 h-2.5 rounded-full" style={{ width: "20.8%" }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span>Net Kâr Marjı (Geçen Yıl)</span>
                          <span className="font-medium">14.2%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-amber-600 h-2.5 rounded-full" style={{ width: "14.2%" }}></div>
                        </div>
                      </div>
                      
                      <div className="pt-4 mt-4 border-t">
                        <div className="flex justify-between">
                          <span className="font-medium">Net Kâr Marjı Artışı:</span>
                          <span className="font-bold text-green-600">+6.6%</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

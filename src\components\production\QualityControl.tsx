
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertTriangle, PlusCircle } from "lucide-react";

export const QualityControl = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium"><PERSON><PERSON></h3>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Kontrol Kaydı
        </Button>
      </div>
      
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Onaylanan Ürünler</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,245</div>
            <p className="text-xs text-muted-foreground">Bu ay</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Reddedilen Ürünler</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">28</div>
            <p className="text-xs text-muted-foreground">Bu ay</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Kalite Ret Oranı</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.2%</div>
            <p className="text-xs text-muted-foreground">Hedef: ≤3%</p>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Son Kalite Kontrol Kayıtları</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative overflow-x-auto rounded-md border">
            <table className="w-full text-sm">
              <thead className="bg-muted text-muted-foreground">
                <tr>
                  <th className="px-4 py-3 text-left">Kontrol No</th>
                  <th className="px-4 py-3 text-left">Üretim Emri</th>
                  <th className="px-4 py-3 text-left">Ürün</th>
                  <th className="px-4 py-3 text-left">Tarih</th>
                  <th className="px-4 py-3 text-left">Kontrol Eden</th>
                  <th className="px-4 py-3 text-left">Sonuç</th>
                  <th className="px-4 py-3 text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">QC-2024-089</td>
                  <td className="px-4 py-3">PO-2024-054</td>
                  <td className="px-4 py-3">Masa Lambası A-100</td>
                  <td className="px-4 py-3">18.06.2024</td>
                  <td className="px-4 py-3">Ayşe Demir</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Onaylandı
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Rapor</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">QC-2024-088</td>
                  <td className="px-4 py-3">PO-2024-053</td>
                  <td className="px-4 py-3">Ofis Sandalyesi</td>
                  <td className="px-4 py-3">17.06.2024</td>
                  <td className="px-4 py-3">Mehmet Yılmaz</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Reddedildi
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Rapor</Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">QC-2024-087</td>
                  <td className="px-4 py-3">PO-2024-052</td>
                  <td className="px-4 py-3">Kitaplık B-200</td>
                  <td className="px-4 py-3">16.06.2024</td>
                  <td className="px-4 py-3">Ali Kaya</td>
                  <td className="px-4 py-3">
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Onaylandı
                    </span>
                  </td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">Detay</Button>
                    <Button variant="ghost" size="sm">Rapor</Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

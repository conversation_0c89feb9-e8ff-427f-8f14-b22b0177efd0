import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog, 
  Plus, Save, RotateCcw 
} from 'lucide-react';

// Ham madde listesi
const hammaddeler = [
  { id: 'STAB1', name: 'STAB1', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'STAB2', name: 'STAB2', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'MUKAVEMET', name: 'MUKAVEMET', icon: Shield, color: 'text-red-600', bgColor: 'bg-red-100' },
  { id: 'PROSES', name: 'PROSES', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { id: 'ENJEKSIYON', name: 'ENJEKSIYON', icon: Droplets, color: 'text-cyan-600', bgColor: 'bg-cyan-100' },
  { id: 'KALSIT', name: 'KALSİT', icon: Atom, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { id: 'PVC', name: 'PVC', icon: Package2, color: 'text-green-600', bgColor: 'bg-green-100' },
  { id: 'MOT12', name: 'MOT12', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT13', name: 'MOT13', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT14', name: 'MOT14', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT15', name: 'MOT15', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT16', name: 'MOT16', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT17', name: 'MOT17', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT18', name: 'MOT18', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
];

interface StokGirisi {
  hammaddeId: string;
  miktar: string;
}

export const HammaddeStokGirisi = () => {
  const { toast } = useToast();
  const [stokGirisi, setStokGirisi] = useState<StokGirisi>({
    hammaddeId: '',
    miktar: ''
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: keyof StokGirisi, value: string) => {
    setStokGirisi(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!stokGirisi.hammaddeId || !stokGirisi.miktar) {
      toast({
        title: "Hata",
        description: "Lütfen ham madde ve miktar alanlarını doldurun.",
        variant: "destructive"
      });
      return;
    }

    const miktar = parseFloat(stokGirisi.miktar);
    if (isNaN(miktar) || miktar <= 0) {
      toast({
        title: "Hata",
        description: "Lütfen geçerli bir miktar girin.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);

    try {
      // Gerçek API çağrısı
      const response = await fetch('/api/hammadde/stok-girisi', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hammaddeId: stokGirisi.hammaddeId,
          miktar: parseFloat(stokGirisi.miktar)
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Stok girişi başarısız');
      }

      toast({
        title: "Başarılı",
        description: `${stokGirisi.miktar} kg ${hammaddeler.find(h => h.id === stokGirisi.hammaddeId)?.name} stok girişi yapıldı.`,
      });

      // Form'u temizle
      setStokGirisi({
        hammaddeId: '',
        miktar: ''
      });

    } catch (error) {
      console.error('Stok girişi hatası:', error);
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Stok girişi sırasında bir hata oluştu.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setStokGirisi({
      hammaddeId: '',
      miktar: ''
    });
  };

  const selectedHammadde = hammaddeler.find(h => h.id === stokGirisi.hammaddeId);

  return (
    <div className="space-y-6">
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5 text-emerald-600" />
            Stok Girişi
          </CardTitle>
          <CardDescription>
            Ham madde stok miktarlarını manuel olarak güncelleyin
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="hammadde">Ham Madde</Label>
              <Select 
                value={stokGirisi.hammaddeId} 
                onValueChange={(value) => handleInputChange('hammaddeId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Ham madde seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {hammaddeler.map((hammadde) => (
                    <SelectItem key={hammadde.id} value={hammadde.id}>
                      <div className="flex items-center gap-2">
                        <div className={`p-1 rounded ${hammadde.bgColor}`}>
                          <hammadde.icon className={`h-3 w-3 ${hammadde.color}`} />
                        </div>
                        {hammadde.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="miktar">Miktar (kg)</Label>
              <Input
                id="miktar"
                type="number"
                step="0.1"
                min="0"
                placeholder="Örn: 100.5"
                value={stokGirisi.miktar}
                onChange={(e) => handleInputChange('miktar', e.target.value)}
              />
            </div>

            {selectedHammadde && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <div className={`p-1.5 rounded ${selectedHammadde.bgColor}`}>
                    <selectedHammadde.icon className={`h-4 w-4 ${selectedHammadde.color}`} />
                  </div>
                  <span className="font-medium">{selectedHammadde.name}</span>
                  <span className="text-muted-foreground">için stok girişi yapılacak</span>
                </div>
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button 
                type="submit" 
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {loading ? 'Kaydediliyor...' : 'Kaydet'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleReset}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Temizle
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

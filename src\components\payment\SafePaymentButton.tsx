
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { safePost, generateTransactionId } from "@/utils/apiUtils";
import { logger } from "@/utils/logger";
import { type ButtonProps } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface SafePaymentButtonProps extends ButtonProps {
  paymentData: {
    cari_id: string;
    fatura_id?: string;
    tutar: number;
    odeme_tarihi: string;
    odeme_yontemi: string;
    aciklama?: string;
    referans_no?: string;
  };
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
  loadingText?: string;
  successText?: string;
  errorText?: string;
}

export const SafePaymentButton = ({
  paymentData,
  onSuccess,
  onError,
  loadingText = "Ödeme kaydediliyor...",
  successText = "Ödeme kaydedildi",
  errorText = "Ödeme kaydedilemedi",
  children,
  className,
  ...props
}: SafePaymentButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [transactionId, setTransactionId] = useState<string | null>(null);

  const handlePayment = async () => {
    // İşlem başladığında loading durumunu aktifleştir
    setIsLoading(true);
    
    // Benzersiz bir transaction ID oluştur
    const newTransactionId = generateTransactionId();
    setTransactionId(newTransactionId);
    
    logger.info(`Ödeme işlemi başlatıldı. [TransactionID: ${newTransactionId}]`);
    
    try {
      // Güvenli API çağrısını yap
      const { data, error } = await safePost(
        '/api/odemeler', 
        paymentData,
        {
          messages: {
            loading: loadingText,
            success: successText,
            error: errorText
          },
          retry: true,
          transactionId: newTransactionId
        }
      );
      
      // İşlem sonucunu kontrol et
      if (error) {
        logger.error(`Ödeme işleminde hata: ${error}. [TransactionID: ${newTransactionId}]`);
        if (onError) onError(error);
      } else {
        logger.info(`Ödeme işlemi başarılı. [TransactionID: ${newTransactionId}]`);
        if (onSuccess && data) onSuccess(data);
      }
    } catch (error) {
      logger.error(`Ödeme işleminde beklenmeyen hata: ${error}. [TransactionID: ${newTransactionId}]`);
      if (onError) onError(error);
    } finally {
      // İşlem bittiğinde loading durumunu kaldır
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={isLoading}
      className={cn(className)}
      {...props}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </Button>
  );
};

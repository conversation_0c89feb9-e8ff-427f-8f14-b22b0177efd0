
import { Button } from "@/components/ui/button";
import { PlusCircle, FileSpreadsheet, Calendar, Users } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { CreateProjectDialog } from "./CreateProjectDialog";
import { CreateTaskDialog } from "./CreateTaskDialog";

export const TeamManagementHeader = () => {
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false);
  const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
  const { toast } = useToast();

  const handleExportData = () => {
    toast({
      title: "Veriler Dışa Aktarıldı",
      description: "Ekip ve proje verileri başarıyla dışa aktarıldı.",
    });
  };

  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Ekip & Proje <PERSON></h2>
        <p className="text-muted-foreground">
          Görevleri yönetin, projeleri planlayın ve ekip iletişimini geliştirin
        </p>
      </div>
      <div className="flex flex-wrap gap-2">
        <Button 
          variant="outline" 
          onClick={handleExportData}
          className="flex items-center gap-2"
        >
          <FileSpreadsheet className="h-4 w-4" />
          Rapor Al
        </Button>
        <Button 
          onClick={() => setIsCreateTaskOpen(true)}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Calendar className="h-4 w-4" />
          Görev Oluştur
        </Button>
        <Button onClick={() => setIsCreateProjectOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Proje
        </Button>
      </div>
      
      {/* Stub dialogs - these will be implemented later */}
      <div className="hidden">
        <CreateProjectDialog 
          open={isCreateProjectOpen} 
          onOpenChange={setIsCreateProjectOpen}
        />
        
        <CreateTaskDialog
          open={isCreateTaskOpen}
          onOpenChange={setIsCreateTaskOpen}
        />
      </div>
    </div>
  );
};

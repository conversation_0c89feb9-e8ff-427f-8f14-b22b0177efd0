
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";

// Demo accounting account data
const accountingAccountsData = [
  { id: "1", code: "100", name: "<PERSON><PERSON>", type: "Asset", balance: 12500.75, currency: "TRY", description: "İşletme kasası" },
  { id: "2", code: "102", name: "<PERSON><PERSON>r", type: "Asset", balance: 158700.50, currency: "TRY", description: "Banka hesapları" },
  { id: "3", code: "120", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", type: "Asset", balance: 45300.25, currency: "TRY", description: "Müşterilerden alacaklar" },
  { id: "4", code: "153", name: "<PERSON><PERSON><PERSON>", type: "Asset", balance: 87500.00, currency: "TRY", description: "Satılmak üzere alınan mallar" },
  { id: "5", code: "191", name: "İndirilecek KDV", type: "Asset", balance: 12840.35, currency: "TRY", description: "İndirilecek KDV" },
  { id: "6", code: "320", name: "Satıcılar", type: "Liability", balance: 38750.80, currency: "TRY", description: "Tedarikçilere borçlar" },
  { id: "7", code: "335", name: "Personele Borçlar", type: "Liability", balance: 28500.00, currency: "TRY", description: "Personele olan borçlar" },
  { id: "8", code: "360", name: "Ödenecek Vergi ve Fonlar", type: "Liability", balance: 18750.25, currency: "TRY", description: "Ödenecek vergi ve fonlar" },
  { id: "9", code: "391", name: "Hesaplanan KDV", type: "Liability", balance: 15640.75, currency: "TRY", description: "Hesaplanan KDV" },
  { id: "10", code: "500", name: "Sermaye", type: "Equity", balance: 250000.00, currency: "TRY", description: "İşletme sermayesi" },
  { id: "11", code: "600", name: "Yurtiçi Satışlar", type: "Revenue", balance: 325000.50, currency: "TRY", description: "Yurtiçi satış gelirleri" },
  { id: "12", code: "601", name: "Yurtdışı Satışlar", type: "Revenue", balance: 175000.25, currency: "TRY", description: "Yurtdışı satış gelirleri" },
  { id: "13", code: "621", name: "Satılan Ticari Mallar Maliyeti", type: "Expense", balance: 285000.75, currency: "TRY", description: "Satılan ticari malların maliyeti" },
  { id: "14", code: "760", name: "Pazarlama Satış ve Dağıtım Giderleri", type: "Expense", balance: 48500.25, currency: "TRY", description: "Pazarlama, satış ve dağıtım giderleri" },
  { id: "15", code: "770", name: "Genel Yönetim Giderleri", type: "Expense", balance: 65800.50, currency: "TRY", description: "Genel yönetim giderleri" }
];

// Column definitions
const accountingAccountsColumns = [
  {
    header: "Kod",
    accessorKey: "code"
  },
  {
    header: "Hesap Adı",
    accessorKey: "name"
  },
  {
    header: "Tür",
    accessorKey: "type",
    cell: ({ row }) => {
      const typeMap: {[key: string]: string} = {
        "Asset": "Varlık",
        "Liability": "Borç",
        "Equity": "Özkaynak",
        "Revenue": "Gelir",
        "Expense": "Gider"
      };
      
      return typeMap[row.original.type] || row.original.type;
    }
  },
  {
    header: "Bakiye",
    accessorKey: "balance",
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span className="font-medium">{currency} {row.original.balance.toLocaleString()}</span>;
    }
  },
  {
    header: "Açıklama",
    accessorKey: "description"
  }
];

export const AccountingAccounts = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [accountType, setAccountType] = useState<string | null>(null);
  
  // Filter data based on search term and account type
  const filteredData = accountingAccountsData.filter(account => {
    const matchesSearch = searchTerm === "" || 
      account.code.toLowerCase().includes(searchTerm.toLowerCase()) || 
      account.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = accountType === null || account.type === accountType;
    
    return matchesSearch && matchesType;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4 items-center">
          <Input 
            placeholder="Hesap kodu veya adı ara..." 
            className="w-64"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Button 
            variant={accountType === null ? "default" : "outline"}
            onClick={() => setAccountType(null)}
          >
            Tümü
          </Button>
          <Button 
            variant={accountType === "Asset" ? "default" : "outline"}
            onClick={() => setAccountType("Asset")}
          >
            Varlık
          </Button>
          <Button 
            variant={accountType === "Liability" ? "default" : "outline"}
            onClick={() => setAccountType("Liability")}
          >
            Borç
          </Button>
          <Button 
            variant={accountType === "Equity" ? "default" : "outline"}
            onClick={() => setAccountType("Equity")}
          >
            Özkaynak
          </Button>
          <Button 
            variant={accountType === "Revenue" ? "default" : "outline"}
            onClick={() => setAccountType("Revenue")}
          >
            Gelir
          </Button>
          <Button 
            variant={accountType === "Expense" ? "default" : "outline"}
            onClick={() => setAccountType("Expense")}
          >
            Gider
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Hesap Ekle
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Hesap Planı</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={accountingAccountsColumns} data={filteredData} />
        </CardContent>
      </Card>
    </div>
  );
};

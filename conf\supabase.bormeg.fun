server {
    if ($host = supabase.bormeg.fun) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name supabase.bormeg.fun;
    return 301 https://$host$request_uri;


}

server {
    listen 443 ssl;
    server_name supabase.bormeg.fun;
    ssl_certificate /etc/letsencrypt/live/supabase.bormeg.fun/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/supabase.bormeg.fun/privkey.pem; # managed by Certbot


    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

}

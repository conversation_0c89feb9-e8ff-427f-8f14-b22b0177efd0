
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Company } from "../types";

export const useCompanies = (
  initialCompanies: Company[],
  setCompanies: (companies: Company[]) => void
) => {
  const { toast } = useToast();
  const [showAddCompanyDialog, setShowAddCompanyDialog] = useState(false);
  const [showEditCompanyDialog, setShowEditCompanyDialog] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [newCompany, setNewCompany] = useState({
    name: "",
    code: "",
    type: "KOBİ"
  });

  const toggleCompanyStatus = (id: number) => {
    setCompanies(initialCompanies.map(company => 
      company.id === id ? {...company, active: !company.active} : company
    ));
    
    const company = initialCompanies.find(c => c.id === id);
    const status = company?.active ? "pasif" : "aktif";
    
    toast({
      title: "Firma durumu güncellendi",
      description: `${company?.name} firması ${status} olarak güncellendi`,
      variant: "default",
    });
  };

  const handleAddCompany = () => {
    if (!newCompany.name || !newCompany.code) {
      toast({
        title: "Hata",
        description: "Lütfen tüm alanları doldurun",
        variant: "destructive",
      });
      return;
    }

    // Check if code is already used
    if (initialCompanies.some(c => c.code === newCompany.code)) {
      toast({
        title: "Hata",
        description: "Bu kurum kodu zaten kullanılıyor",
        variant: "destructive",
      });
      return;
    }

    const newId = Math.max(...initialCompanies.map(c => c.id)) + 1;
    const company = {
      id: newId,
      name: newCompany.name,
      code: newCompany.code,
      active: true,
      modules: ["finans", "stok"], // Basic modules by default
      optionalModules: [],
      type: newCompany.type,
      contactInfo: {},
      taxInfo: {},
      subscription: {
        plan: "Başlangıç",
        status: "Deneme",
        nextBillingDate: new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0], // 30 days from now
        amount: 0
      }
    };

    setCompanies([...initialCompanies, company]);
    setNewCompany({ name: "", code: "", type: "KOBİ" });
    setShowAddCompanyDialog(false);

    toast({
      title: "Firma eklendi",
      description: "Yeni firma başarıyla eklendi",
      variant: "default",
    });
  };

  const handleEditCompany = (company: Company) => {
    setSelectedCompany(company);
    setShowEditCompanyDialog(true);
  };

  const handleSaveCompany = (updatedCompany: Company) => {
    setCompanies(initialCompanies.map(company => 
      company.id === updatedCompany.id ? updatedCompany : company
    ));
    setShowEditCompanyDialog(false);
  };

  const handleResetPassword = (companyId: number) => {
    const company = initialCompanies.find(c => c.id === companyId);
    if (company) {
      toast({
        title: "Şifre Sıfırlama Bağlantısı Gönderildi",
        description: `${company.name} firması için şifre sıfırlama bağlantısı gönderildi`,
      });
    }
  };

  return {
    showAddCompanyDialog,
    setShowAddCompanyDialog,
    showEditCompanyDialog,
    setShowEditCompanyDialog,
    selectedCompany,
    setSelectedCompany,
    newCompany,
    setNewCompany,
    toggleCompanyStatus,
    handleAddCompany,
    handleEditCompany,
    handleSaveCompany,
    handleResetPassword
  };
};

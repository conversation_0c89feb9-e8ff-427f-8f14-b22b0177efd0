
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ProductionPlanning } from "./ProductionPlanning";
import { ProductionOrders } from "./ProductionOrders";
import { QualityControl } from "./QualityControl";
import { MaterialsManagement } from "./MaterialsManagement";
import { ProductionStats } from "./ProductionStats";

export const ProductionDashboard = () => {
  return (
    <div className="space-y-6">
      <ProductionStats />
      
      <Tabs defaultValue="planning" className="space-y-4">
        <TabsList className="flex flex-wrap">
          <TabsTrigger value="planning">Üretim Planlama</TabsTrigger>
          <TabsTrigger value="orders">Üretim Emirleri</TabsTrigger>
          <TabsTrigger value="materials">Malzeme Yönetimi</TabsTrigger>
          <TabsTrigger value="quality">Kalite Kontrol</TabsTrigger>
        </TabsList>
        
        <TabsContent value="planning" className="space-y-4">
          <ProductionPlanning />
        </TabsContent>
        
        <TabsContent value="orders" className="space-y-4">
          <ProductionOrders />
        </TabsContent>
        
        <TabsContent value="materials" className="space-y-4">
          <MaterialsManagement />
        </TabsContent>
        
        <TabsContent value="quality" className="space-y-4">
          <QualityControl />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { CashFlowTableRow } from "./CashFlowTableRow";
import { cashFlowData, months } from "../utils/cashFlowUtils";

interface CashFlowTableProps {
  expandedSections: Record<string, boolean>;
  toggleSection: (section: string) => void;
}

export const CashFlowTable: React.FC<CashFlowTableProps> = ({
  expandedSections,
  toggleSection
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader className="bg-gray-50">
          <TableRow>
            <TableHead className="w-48 font-bold">Kategoriler</TableHead>
            {months.map(month => (
              <TableHead key={month} className="text-center font-medium">{month}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {/* Income Section */}
          <CashFlowTableRow
            title={cashFlowData.income.title}
            bgColor={cashFlowData.income.bgColor}
            isExpandable={true}
            isExpanded={expandedSections.income}
            onToggle={() => toggleSection('income')}
            data={cashFlowData.income.data}
          />

          {/* Expenses Section */}
          <CashFlowTableRow
            title={cashFlowData.expenses.title}
            bgColor={cashFlowData.expenses.bgColor}
            isExpandable={true}
            isExpanded={expandedSections.expenses}
            onToggle={() => toggleSection('expenses')}
            data={cashFlowData.expenses.data}
          />

          {/* Expense subcategories */}
          {expandedSections.expenses && cashFlowData.expenses.subcategories.map(subcategory => (
            <CashFlowTableRow
              key={subcategory.id}
              title={subcategory.title}
              data={subcategory.data}
              indented={true}
            />
          ))}

          {/* Total row */}
          <CashFlowTableRow
            title={cashFlowData.total.title}
            bgColor={cashFlowData.total.bgColor}
            data={cashFlowData.total.data}
          />
        </TableBody>
      </Table>
    </div>
  );
};


import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { CashFlowManagement } from "@/components/billing/CashFlowManagement";
import { BankAccountManagement } from "@/components/billing/BankAccountManagement";
import { IncomeExpenseTracker } from "@/components/billing/IncomeExpenseTracker";

const Nakit = () => {
  const tabs: TabItem[] = [
    { id: "cashflow", label: "Nakit Akışı", component: <CashFlowManagement /> },
    { id: "bank-accounts", label: "Banka/Kasa", component: <BankAccountManagement /> },
    { id: "income-expense", label: "Gelir-Gider", component: <IncomeExpenseTracker /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Nakit ve Banka</h2>
          <p className="text-muted-foreground">
            Nakit akış<PERSON>, banka hesapları ve gelir-gider takibi
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Nakit;

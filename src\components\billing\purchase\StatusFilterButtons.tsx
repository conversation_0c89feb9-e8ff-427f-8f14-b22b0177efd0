
import { Button } from "@/components/ui/button";

type StatusFilterButtonsProps = {
  statusFilter: string | null;
  setStatusFilter: (status: string | null) => void;
};

export const StatusFilterButtons = ({ 
  statusFilter, 
  setStatusFilter 
}: StatusFilterButtonsProps) => {
  return (
    <div className="flex space-x-4">
      <Button 
        variant={statusFilter === null ? "default" : "outline"}
        onClick={() => setStatusFilter(null)}
      >
        Tümü
      </Button>
      <Button 
        variant={statusFilter === "pending" ? "default" : "outline"}
        onClick={() => setStatusFilter("pending")}
      >
        Ödemesi Bekleyenler
      </Button>
      <Button 
        variant={statusFilter === "paid" ? "default" : "outline"}
        onClick={() => setStatusFilter("paid")}
      >
        Ödenenler
      </Button>
    </div>
  );
};

import React from 'react';
import { Card } from '@/components/ui/card';

interface UretimOzetiProps {
    categories: {
        kategori: string;
        toplamUzunluk: number;
        ortalamaAgirlik: number;
        adet: number;
    }[];
}

const UretimOzeti: React.FC<UretimOzetiProps> = ({ categories }) => {
    // Kategorileri ve renk sınıflarını hazırla
    const categoryStyles: Record<string, any> = {
        'Delikli': {
            bg: 'bg-blue-50',
            text: 'text-blue-700',
            badge: 'bg-blue-500',
            icon: 'text-blue-500'
        },
        'Deliksiz': {
            bg: 'bg-green-50',
            text: 'text-green-700',
            badge: 'bg-green-500',
            icon: 'text-green-500'
        },
        'Keçeli': {
            bg: 'bg-amber-50',
            text: 'text-amber-700',
            badge: 'bg-amber-500',
            icon: 'text-amber-500'
        }
    };

    return (
        <div className="bg-white p-3 rounded-lg shadow-sm">
            <div className="flex items-center mb-3 text-blue-600">
                <div className="h-3.5 w-1 bg-blue-600 rounded-full mr-1.5"></div>
                <span className="font-medium text-sm">Üretim Özeti: Tümü</span>
            </div>

            <div className="grid grid-cols-3 gap-3">
                {categories.map((category, index) => {
                    const styles = categoryStyles[category.kategori] || categoryStyles['Delikli'];
                    return (
                        <Card
                            key={index}
                            className={`p-3 ${styles.bg} border-none shadow-sm h-full flex flex-col justify-between`}
                        >
                            <div className="flex justify-between items-center mb-1">
                                <h3 className={`text-sm font-bold ${styles.text}`}>
                                    {category.kategori.toUpperCase()}
                                </h3>
                                <span className={`${styles.badge} text-white rounded-full px-2 py-0.5 text-xs`}>
                                    {category.adet} adet
                                </span>
                            </div>

                            <div className="grid grid-cols-2 gap-2">
                                <div className="flex items-center">
                                    <div className="w-5 h-5 flex items-center justify-center mr-1">
                                        <svg viewBox="0 0 24 24" className={`w-4 h-4 ${styles.text}`} stroke="currentColor" fill="none" strokeWidth={2}>
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-xs text-gray-500">Uzunluk</div>
                                        <div className="text-xs font-semibold text-gray-800">
                                            {category.toplamUzunluk.toFixed(1)} m
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center">
                                    <div className="w-5 h-5 flex items-center justify-center mr-1">
                                        <svg viewBox="0 0 24 24" className={`w-4 h-4 ${styles.text}`} stroke="currentColor" fill="none" strokeWidth={2}>
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-xs text-gray-500">Ağırlık/m</div>
                                        <div className="text-xs font-semibold text-gray-800">
                                            {category.ortalamaAgirlik.toFixed(4)} kg
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    );
                })}
            </div>
        </div>
    );
};

export default UretimOzeti;

import axios from 'axios';
import { SiloData } from '@/models/SiloData';
import { UrunModel } from '@/models/UrunModel';

// API temel URL'si
// const API_BASE_URL = ''; // <PERSON><PERSON> bırakırsak, göreli yollar kullanılır
// const API_BASE_URL = 'https://173.249.0.120:3000'; // HTTP yerine HTTPS kullanılıyor
const API_BASE_URL = ''; // Göreceli yolları kullanıyoruz

// API_URL'yi kaldırıyorum, doğrudan göreli yollar kullanacağız
console.log("API Service initialized with base URL:", API_BASE_URL || 'relative paths');

// Müşteri (Cari) hizmetleri
export const musteriService = {
  getAll: async () => {
    const response = await axios.get(`/api/musteri`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/musteri/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/musteri`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/musteri/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/musteri/${id}`);
    return response.data;
  }
};

// Fatura hizmetleri
export const faturaService = {
  getAll: async () => {
    const response = await axios.get(`/api/fatura`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/fatura/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/fatura`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/fatura/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/fatura/${id}`);
    return response.data;
  }
};

// Ürün hizmetleri
export const urunService = {
  getAll: async () => {
    const response = await axios.get(`/api/urun`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/urun/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/urun`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/urun/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/urun/${id}`);
    return response.data;
  }
};

// Ödeme hizmetleri
export const odemeService = {
  getAll: async () => {
    const response = await axios.get(`/api/odeme`);
    return response.data;
  },

  getById: async (id: string) => {
    const response = await axios.get(`/api/odeme/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await axios.post(`/api/odeme`, data);
    return response.data;
  },

  update: async (id: string, data: any) => {
    const response = await axios.put(`/api/odeme/${id}`, data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await axios.delete(`/api/odeme/${id}`);
    return response.data;
  }
};

export class ApiService {
  // Silo verilerini getiren metot
  static async fetchSiloData(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      console.log("fetchSiloData çağrıldı - URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      console.log("fetchSiloData yanıtı:", data.length, "kayıt alındı");

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => {
        // Tarih düzeltmesi (saat problemi için)
        const tarihStr = item.tarih;
        const tarihObj = new Date(tarihStr);

        return {
          ...item,
          id: item.ID,
          tarih: tarihObj,
          boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          hedefAgirlik: item.hedef_agirlik,
          uzunluk: item.uzunluk,
          yeniStokKodu: item.yeni_stok_kodu,
          saat: item.saat || tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
        }
      });
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Ürün modellerini getiren metot
  static async fetchUrunler(): Promise<UrunModel[]> {
    try {
      const response = await fetch(`/api/urun-modelleri`);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();

      return data.map((item: any) => ({
        stokKodu: item.stok_kodu,
        stokAdi: item.stok_adi,
        uzunluk: item.uzunluk,
        hedefAgirlik: item.hedef_agirlik,
        toleransYuzde: item.tolerans_yuzde,
        aktif: item.aktif === 1
      }));
    } catch (error) {
      console.error('Ürün modelleri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Aktif üretim bilgisini getiren metot
  static async fetchAktifUretim() {
    try {
      const response = await fetch(`/api/aktif-uretim`);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Aktif üretim bilgisi alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata kaydı silen metot
  static async deleteSiloData(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata kaydı güncelleyen metot
  static async updateSiloData(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Yeni stok kodu kaydetme metodu (istenirse yeni bir kayıt ID'si olmadan)
  static async saveStokKodu(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stok_kodu: stokKodu,
          BoruNo: 1, // Default boru numarası
          tarih: new Date().toISOString()
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API yanıt hatası: ${response.status}${errorData.error ? ` - ${errorData.error}` : ''}`);
      }

      const responseData = await response.json();
      console.log("API yanıtı:", responseData);
      return true;
    } catch (error) {
      console.error('Stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }

  // ID'ye göre ürün bilgisini getiren metot
  static async fetchProductById(id: string): Promise<SiloData> {
    try {
      const url = `/api/silodata/${id}`;
      console.log("Ürün bilgisi alınıyor, URL:", url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();

      return {
        ...data,
        id: data.ID || data.id,
        tarih: new Date(data.tarih),
        boruAd: data.boru_ad || `Ø ${data.stok_adi?.match(/\d{2,3}/)?.length > 0 ? data.stok_adi.match(/\d{2,3}/)[0] : ''} ${data.stok_adi}`,
        boruAg: typeof data.boru_ag === 'string' ? parseFloat(data.boru_ag) : data.boru_ag,
        stokKodu: data.stok_kodu,
        stokAdi: data.stok_adi,
        saat: data.saat || new Date(data.tarih).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
      };
    } catch (error) {
      console.error('Ürün bilgisi alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Ürün dönüştürme metodu
  static async convertProduct(id: string): Promise<any> {
    try {
      const url = `/api/silodata/convert/${id}`;
      console.log("Ürün dönüştürme isteği, URL:", url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Dönüşüm hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Ürün dönüştürme işlemi sırasında hata oluştu:', error);
      throw error;
    }
  }

  // Delikli boru kategorilerini getiren metot
  static async fetchDelikliBoru(): Promise<{ stokKodu: string, stokAdi: string }[]> {
    try {
      const url = `/api/delikli-boru-kategorileri`;
      console.log("Delikli boru kategorileri alınıyor, URL:", url);

      const response = await fetch(url);

      if (!response.ok) {
        console.error(`API yanıt hatası: ${response.status}`);
        // Hata durumunda sabit kategorileri döndür
        return [
          { stokKodu: 'MRZ-ST00661', stokAdi: 'Ø 80 DELİKLİ BORU' },
          { stokKodu: 'MGS0055', stokAdi: 'Ø 100 DELİKLİ BORU' },
          { stokKodu: 'MRZ-ST00665', stokAdi: 'Ø 125 DELİKLİ BORU' },
          { stokKodu: 'MGS0053', stokAdi: 'Ø 160 DELİKLİ BORU' },
          { stokKodu: 'MGS0056', stokAdi: 'Ø 200 DELİKLİ BORU' }
        ];
      }

      const data = await response.json();

      // Eğer veri boşsa veya dizi değilse varsayılan listeyi kullan
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('API boş veri döndürdü, varsayılan kategorileri kullanıyorum');
        return [
          { stokKodu: 'MRZ-ST00661', stokAdi: 'Ø 80 DELİKLİ BORU' },
          { stokKodu: 'MGS0055', stokAdi: 'Ø 100 DELİKLİ BORU' },
          { stokKodu: 'MRZ-ST00665', stokAdi: 'Ø 125 DELİKLİ BORU' },
          { stokKodu: 'MGS0053', stokAdi: 'Ø 160 DELİKLİ BORU' },
          { stokKodu: 'MGS0056', stokAdi: 'Ø 200 DELİKLİ BORU' }
        ];
      }

      // API yanıtındaki snake_case anahtarları camelCase'e dönüştür
      const transformed = data.map((item: any) => ({
        stokKodu: item.stok_kodu,
        stokAdi: item.stok_adi
      }));
      return transformed;
    } catch (error) {
      console.error('Delikli boru kategorileri alınırken hata oluştu:', error);
      // Hata durumunda sabit kategorileri döndür
      return [
        { stokKodu: 'MRZ-ST00661', stokAdi: 'Ø 80 DELİKLİ BORU' },
        { stokKodu: 'MGS0055', stokAdi: 'Ø 100 DELİKLİ BORU' },
        { stokKodu: 'MRZ-ST00665', stokAdi: 'Ø 125 DELİKLİ BORU' },
        { stokKodu: 'MGS0053', stokAdi: 'Ø 160 DELİKLİ BORU' },
        { stokKodu: 'MGS0056', stokAdi: 'Ø 200 DELİKLİ BORU' }
      ];
    }
  }

  // Keçeli boru karşılıklarını getiren metot
  static async getKeceliBoru(delikliBoru: string): Promise<string> {
    // Bu fonksiyon normalde API'den keçeli boru karşılıklarını alabilir
    // Şimdilik sabit eşleşmeleri kullanıyoruz
    const eslesme: { [key: string]: string } = {
      'MRZ-ST00661': 'MRZ-ST00663', // Ø 80 DELİKLİ BORU -> Ø 80 KEÇELİ BORU
      'MGS0055': 'MRZ-ST02549',     // Ø 100 DELİKLİ BORU -> Ø 100 KEÇELİ BORU
      'MRZ-ST00665': 'MRZ-ST00666', // Ø 125 DELİKLİ BORU -> Ø 125 KEÇELİ BORU
      'MGS0053': 'MRZ-ST02550',     // Ø 160 DELİKLİ BORU -> Ø 160 KEÇELİ BORU
      'MGS0056': 'MRZ-ST02551'      // Ø 200 DELİKLİ BORU -> Ø 200 KEÇELİ BORU
    };

    return eslesme[delikliBoru] || '';
  }

  // Keçeli boru listesini getiren metot
  static async fetchKeceliBoru(keceliBoru: string, adet: number): Promise<SiloData[]> {
    try {
      // URL'yi düzeltiyoruz - keçeli boru yerine stok koduna göre sorgu yapacağız
      const url = `/api/silodata?stokKodu=${keceliBoru}&durum=aktif&limit=${adet}`;
      console.log("Keçeli boru listesi alınıyor, URL:", url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();

      // Veri dönüşümü
      return data.map((item: any) => ({
        ...item,
        id: item.ID || item.id,
        tarih: new Date(item.tarih),
        stokKodu: item.stok_kodu,
        stokAdi: item.stok_adi,
        durum: item.durum
      }));
    } catch (error) {
      console.error('Keçeli boru listesi alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Toplu dönüşüm işlemi yapan metot
  static async bulkConvertProducts(delikliBoruId: string, keceliBoruId: string): Promise<any> {
    try {
      const url = `/api/silodata/bulk-convert`;
      console.log("Toplu dönüşüm isteği, URL:", url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          delikliBoruId,
          keceliBoruId
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Dönüşüm hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Toplu dönüşüm işlemi sırasında hata oluştu:', error);
      throw error;
    }
  }

  // Üretim Hattı 2 - Silo verilerini getiren metot
  static async fetchSiloData2(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata2`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      console.log("fetchSiloData2 çağrıldı - URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      console.log("fetchSiloData2 yanıtı:", data.length, "kayıt alındı");

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => {
        // Tarih düzeltmesi (saat problemi için)
        const tarihStr = item.tarih;
        const tarihObj = new Date(tarihStr);

        return {
          ...item,
          id: item.ID,
          tarih: tarihObj,
          boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          hedefAgirlik: item.hedef_agirlik,
          uzunluk: item.uzunluk,
          yeniStokKodu: item.yeni_stok_kodu,
          saat: item.saat || tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
        }
      });
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata2 kaydı silen metot
  static async deleteSiloData2(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata2/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata2 kaydı güncelleyen metot
  static async updateSiloData2(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata2/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata2 için yeni stok kodu kaydetme
  static async saveStokKodu2(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata2`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stok_kodu: stokKodu,
          BoruNo: 1, // Default boru numarası
          tarih: new Date().toISOString()
        })
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API yanıt hatası: ${response.status}${errorData.error ? ` - ${errorData.error}` : ''}`);
      }
      const responseData = await response.json();
      console.log("API yanıtı:", responseData);
      return true;
    } catch (error) {
      console.error('Silodata2 stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }

  // Üretim Hattı 3 - Silo verilerini getiren metot
  static async fetchSiloData3(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata3`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      console.log("fetchSiloData3 çağrıldı - URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      console.log("fetchSiloData3 yanıtı:", data.length, "kayıt alındı");

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => {
        // Tarih düzeltmesi (saat problemi için)
        const tarihStr = item.tarih;
        const tarihObj = new Date(tarihStr);

        return {
          ...item,
          id: item.ID,
          tarih: tarihObj,
          boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          hedefAgirlik: item.hedef_agirlik,
          uzunluk: item.uzunluk,
          yeniStokKodu: item.yeni_stok_kodu,
          saat: item.saat || tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
        }
      });
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata3 kaydı silen metot
  static async deleteSiloData3(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata3/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata3 kaydı güncelleyen metot
  static async updateSiloData3(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata3/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata3 için yeni stok kodu kaydetme
  static async saveStokKodu3(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata3`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stok_kodu: stokKodu,
          BoruNo: 1, // Default boru numarası
          tarih: new Date().toISOString()
        })
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API yanıt hatası: ${response.status}${errorData.error ? ` - ${errorData.error}` : ''}`);
      }
      const responseData = await response.json();
      console.log("API yanıtı:", responseData);
      return true;
    } catch (error) {
      console.error('Silodata3 stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }

  // silodata_genel tablosundan ID ile ürün getirme
  static async fetchSiloDataGenelById(id: string): Promise<SiloData> {
    try {
      const response = await fetch(`/api/silodata_genel/${id}`);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      if (!data) {
        throw new Error('Ürün bulunamadı');
      }

      // SiloData formatına dönüştür
      return {
        id: data.ID,
        tarih: new Date(data.tarih),
        boruAd: data.boru_ad || '',
        boruAg: data.boru_ag || 0,
        buyukSAg: data.buyuk_s_ag || 0,
        stokKodu: data.stok_kodu || '',
        stokAdi: data.stok_adi || '',
        durum: data.durum || '',
        hat: data.hat || '',
        donusumId: data.donusum_id || null,
        donusumTarihi: data.donusum_tarihi ? new Date(data.donusum_tarihi) : null
      };
    } catch (error) {
      console.error('Ürün getirme hatası:', error);
      throw error;
    }
  }

  // silodata_genel tablosundan stok kodu ve hat ile ürün listesi getirme
  static async fetchSiloDataGenelByStok(stokKodu: string, adet: number): Promise<SiloData[]> {
    try {
      const url = `/api/silodata_genel?stokKodu=${stokKodu}&durum=aktif&limit=${adet}`;
      console.log('fetchSiloDataGenelByStok URL:', url);
      const response = await fetch(url);

      console.log('fetchSiloDataGenelByStok Response Status:', response.status);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('fetchSiloDataGenelByStok Error Response:', errorText);
        throw new Error(`API yanıt hatası: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('fetchSiloDataGenelByStok Data:', data);

      // SiloData[] formatına dönüştür
      return data.map((item: any) => ({
        id: item.ID,
        tarih: new Date(item.tarih),
        boruAd: item.boru_ad || '',
        boruAg: item.boru_ag || 0,
        buyukSAg: item.buyuk_s_ag || 0,
        stokKodu: item.stok_kodu || '',
        stokAdi: item.stok_adi || '',
        durum: item.durum || '',
        hat: item.Hat_no || '',
        donusumId: item.donusum_id || null,
        donusumTarihi: item.donusum_tarihi ? new Date(item.donusum_tarihi) : null
      }));
    } catch (error) {
      console.error('Ürün listesi getirme hatası:', error);
      throw error;
    }
  }

  // silodata_genel tablosunda tekli dönüşüm yapma
  static async convertSiloDataGenel(id: string): Promise<any> {
    try {
      const response = await fetch(`/api/silodata_genel/convert/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Dönüşüm hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Dönüşüm hatası:', error);
      throw error;
    }
  }

  // silodata_genel tablosunda toplu dönüşüm yapma
  static async bulkConvertSiloDataGenel(delikliBoruId: string, keceliBoruId: string): Promise<any> {
    try {
      const response = await fetch(`/api/silodata_genel/bulk-convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          delikliBoruId,
          keceliBoruId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Toplu dönüşüm hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Toplu dönüşüm hatası:', error);
      throw error;
    }
  }

  // silodata_genel tablosuna yeni kayıt ekleme
  static async addSiloDataGenel(tarih: string, stokKodu: string, boruAd: string): Promise<any> {
    try {
      const response = await fetch('/api/silodata_genel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tarih,
          stok_kodu: stokKodu,
          boru_ad: boruAd
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Kayıt ekleme hatası: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Kayıt ekleme hatası:', error);
      throw error;
    }
  }

  // Üretim Hattı 4 - Silo verilerini getiren metot
  static async fetchSiloData4(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata4`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      console.log("fetchSiloData4 çağrıldı - URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      console.log("fetchSiloData4 yanıtı:", data.length, "kayıt alındı");

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => {
        // Tarih düzeltmesi (saat problemi için)
        const tarihStr = item.tarih;
        const tarihObj = new Date(tarihStr);

        return {
          ...item,
          id: item.ID,
          tarih: tarihObj,
          boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          hedefAgirlik: item.hedef_agirlik,
          uzunluk: item.uzunluk,
          yeniStokKodu: item.yeni_stok_kodu,
          saat: item.saat || tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
        }
      });
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata4 kaydı silen metot
  static async deleteSiloData4(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata4/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata4 kaydı güncelleyen metot
  static async updateSiloData4(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata4/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata4 için yeni stok kodu kaydetme
  static async saveStokKodu4(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata4`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stok_kodu: stokKodu,
          BoruNo: 1, // Default boru numarası
          tarih: new Date().toISOString()
        })
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API yanıt hatası: ${response.status}${errorData.error ? ` - ${errorData.error}` : ''}`);
      }
      const responseData = await response.json();
      console.log("API yanıtı:", responseData);
      return true;
    } catch (error) {
      console.error('Silodata4 stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }

  // Üretim Hattı 5 - Silo verilerini getiren metot
  static async fetchSiloData5(dateFilter?: string, startDate?: string, endDate?: string): Promise<SiloData[]> {
    let url = `/api/getSilodata5`;
    const params = new URLSearchParams();

    if (dateFilter) {
      params.append('date', dateFilter);
    } else if (startDate && endDate) {
      params.append('startDate', startDate);
      params.append('endDate', endDate);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    try {
      console.log("fetchSiloData5 çağrıldı - URL:", url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      const data = await response.json();
      console.log("fetchSiloData5 yanıtı:", data.length, "kayıt alındı");

      // Tarih formatını düzeltme ve veri dönüşümü
      return data.map((item: any) => {
        // Tarih düzeltmesi (saat problemi için)
        const tarihStr = item.tarih;
        const tarihObj = new Date(tarihStr);

        return {
          ...item,
          id: item.ID,
          tarih: tarihObj,
          boruAd: item.boru_ad || `Ø ${item.stok_adi?.match(/\d{2,3}/)?.length > 0 ? item.stok_adi.match(/\d{2,3}/)[0] : ''} ${item.stok_adi}`,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          hedefAgirlik: item.hedef_agirlik,
          uzunluk: item.uzunluk,
          yeniStokKodu: item.yeni_stok_kodu,
          saat: item.saat || tarihObj.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })
        }
      });
    } catch (error) {
      console.error('Veri alınırken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata5 kaydı silen metot
  static async deleteSiloData5(id: number): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata5/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt silinirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata5 kaydı güncelleyen metot
  static async updateSiloData5(id: number, stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata5/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ stokKodu })
      });

      if (!response.ok) {
        throw new Error(`API yanıt hatası: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Kayıt güncellenirken hata oluştu:', error);
      throw error;
    }
  }

  // Silodata5 için yeni stok kodu kaydetme
  static async saveStokKodu5(stokKodu: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/silodata5`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stok_kodu: stokKodu,
          BoruNo: 1, // Default boru numarası
          tarih: new Date().toISOString()
        })
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API yanıt hatası: ${response.status}${errorData.error ? ` - ${errorData.error}` : ''}`);
      }
      const responseData = await response.json();
      console.log("API yanıtı:", responseData);
      return true;
    } catch (error) {
      console.error('Silodata5 stok kodu kaydedilirken hata oluştu:', error);
      throw error;
    }
  }
}


import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Receipt, CreditCard, Wallet, BarChart3, TrendingUp } from "lucide-react";

// Import all components
import { BillingOverview } from "./BillingOverview";
import { IncomeExpenseTracker } from "./IncomeExpenseTracker";
import { EInvoiceManagement } from "./EInvoiceManagement";
import { BankAccountManagement } from "./BankAccountManagement";
import { EGovernmentIntegration } from "./EGovernmentIntegration";
import { OrderManagement } from "./OrderManagement";
import { FinancialReports } from "./FinancialReports";
import { SalesAndInvoicing } from "./SalesAndInvoicing";
import { ReceivablesManagement } from "./ReceivablesManagement";
import { ExpenseAndPayment } from "./ExpenseAndPayment";
import { InventoryManagement } from "./InventoryManagement";
import { CashFlowManagement } from "./CashFlowManagement";
import { ReportingAndAnalysis } from "./ReportingAndAnalysis";

// Finance menu structure
const financeMenuStructure = [
  {
    category: "Faturalama",
    icon: FileText,
    tabs: [
      { id: "invoices", label: "Faturalar", component: <BillingOverview /> },
      { id: "e-invoice", label: "E-Fatura/E-Arşiv", component: <EInvoiceManagement /> },
      { id: "sales", label: "Satış ve Faturalama", component: <SalesAndInvoicing /> },
    ]
  },
  {
    category: "Alacak ve Borç Yönetimi",
    icon: Receipt,
    tabs: [
      { id: "receivables", label: "Alacak Yönetimi", component: <ReceivablesManagement /> },
      { id: "expenses", label: "Gider ve Ödeme", component: <ExpenseAndPayment /> },
    ]
  },
  {
    category: "Envanter",
    icon: CreditCard,
    tabs: [
      { id: "inventory", label: "Stok ve Sipariş", component: <InventoryManagement /> },
      { id: "orders", label: "Siparişler", component: <OrderManagement /> },
    ]
  },
  {
    category: "Nakit ve Banka",
    icon: Wallet,
    tabs: [
      { id: "cashflow", label: "Nakit Akışı", component: <CashFlowManagement /> },
      { id: "bank-accounts", label: "Banka/Kasa", component: <BankAccountManagement /> },
      { id: "income-expense", label: "Gelir-Gider", component: <IncomeExpenseTracker /> },
    ]
  },
  {
    category: "Raporlar",
    icon: BarChart3,
    tabs: [
      { id: "reports", label: "Finansal Raporlar", component: <FinancialReports /> },
      { id: "analysis", label: "Raporlama ve Analiz", component: <ReportingAndAnalysis /> },
    ]
  },
  {
    category: "E-Devlet İşlemleri",
    icon: TrendingUp,
    tabs: [
      { id: "e-government", label: "E-Devlet", component: <EGovernmentIntegration /> },
    ]
  },
];

export const FinancialManagementTabs = () => {
  const location = useLocation();
  const [selectedCategory, setSelectedCategory] = useState(financeMenuStructure[0].category);
  const [activeTab, setActiveTab] = useState(financeMenuStructure[0].tabs[0].id);

  // Parse URL parameters to get the selected category
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const categoryParam = params.get('category');
    
    if (categoryParam) {
      const foundCategory = financeMenuStructure.find(
        item => item.category === categoryParam
      );
      
      if (foundCategory) {
        setSelectedCategory(foundCategory.category);
        setActiveTab(foundCategory.tabs[0].id);
      }
    }
  }, [location.search]);

  const handleCategorySelect = (category: string, firstTabId: string) => {
    setSelectedCategory(category);
    setActiveTab(firstTabId);
  };

  // Find the current category object
  const currentCategory = financeMenuStructure.find(item => item.category === selectedCategory);
  
  return (
    <div className="space-y-4">
      {/* Main Navigation Menu */}
      <div className="mb-6 border-b">
        <div className="flex overflow-x-auto">
          {financeMenuStructure.map(item => (
            <div
              key={item.category}
              className={`
                cursor-pointer group flex items-center text-base py-2 px-4 transition-colors whitespace-nowrap
                ${selectedCategory === item.category 
                  ? "border-b-2 border-blue-600 text-blue-600 font-medium" 
                  : "text-gray-600 hover:text-blue-600"}
              `}
              onClick={() => handleCategorySelect(item.category, item.tabs[0].id)}
            >
              <item.icon className="mr-2 h-4 w-4" />
              <span>{item.category}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Category Tabs */}
      {currentCategory && (
        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab} 
          className="space-y-4"
        >
          <TabsList className="flex flex-wrap">
            {currentCategory.tabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {/* Content for selected tab */}
          {currentCategory.tabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="space-y-4">
              {tab.component}
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
};


import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { UserPlus, Users, Search, Mail, Phone, FileText } from "lucide-react";

export const CustomerList = () => {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Müşteri Listesi</CardTitle>
            <CardDescription>Müşteri bilgilerini yönetin ve borç/alacak durumunu takip edin</CardDescription>
          </div>
          <Button className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Müşteri Ekle
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Müşteri ara..."
              className="pl-8"
            />
          </div>
        </div>
        
        <div className="border rounded-md divide-y">
          <div className="grid grid-cols-6 gap-4 p-3 bg-muted/50 font-medium text-sm">
            <div className="col-span-2">Müşteri</div>
            <div>Vergi No</div>
            <div>Bakiye</div>
            <div>Son İşlem</div>
            <div>İşlemler</div>
          </div>
          
          <div className="grid grid-cols-6 gap-4 p-3 items-center hover:bg-muted/20">
            <div className="col-span-2 flex items-center gap-3">
              <div className="bg-blue-100 p-2 rounded-md">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium">Ahmet Yılmaz</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            <div>1234567890</div>
            <div className="text-green-600 font-medium">₺12,450.00</div>
            <div className="text-xs">25 Mar 2024</div>
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <FileText className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Mail className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Phone className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-6 gap-4 p-3 items-center hover:bg-muted/20">
            <div className="col-span-2 flex items-center gap-3">
              <div className="bg-purple-100 p-2 rounded-md">
                <Users className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="font-medium">Ayşe Demir</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            <div>9876543210</div>
            <div className="text-destructive font-medium">-₺3,245.00</div>
            <div className="text-xs">24 Mar 2024</div>
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <FileText className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Mail className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Phone className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-6 gap-4 p-3 items-center hover:bg-muted/20">
            <div className="col-span-2 flex items-center gap-3">
              <div className="bg-green-100 p-2 rounded-md">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="font-medium">Mehmet Kaya</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            <div>5678901234</div>
            <div className="text-green-600 font-medium">₺28,750.00</div>
            <div className="text-xs">22 Mar 2024</div>
            <div className="flex gap-1">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <FileText className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Mail className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Phone className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

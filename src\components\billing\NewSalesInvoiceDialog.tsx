
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { CreateSalesInvoice } from "./CreateSalesInvoice";
import { FileText } from "lucide-react";

interface NewSalesInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const NewSalesInvoiceDialog = ({ open, onOpenChange }: NewSalesInvoiceDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Yeni Fatura Oluştur
          </DialogTitle>
        </DialogHeader>
        
        <CreateSalesInvoice />
      </DialogContent>
    </Dialog>
  );
};

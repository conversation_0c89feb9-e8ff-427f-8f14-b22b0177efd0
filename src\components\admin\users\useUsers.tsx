
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { User } from "../types";

export const useUsers = (users: User[], setUsers: (users: User[]) => void) => {
  const { toast } = useToast();
  const [showAddUserDialog, setShowAddUserDialog] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "Kullanıcı",
    companyId: 0
  });

  const handleAddUser = () => {
    if (!newUser.name || !newUser.email || !newUser.companyId) {
      toast({
        title: "Hata",
        description: "Lütfen tüm alanları doldurun",
        variant: "destructive",
      });
      return;
    }

    const newId = Math.max(...users.map(u => u.id)) + 1;
    const avatarInitials = newUser.name
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase();
    
    const user = {
      id: newId,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      avatar: avatarInitials,
      companyId: newUser.companyId,
      permissions: newUser.role === "Müdür" ? ["finans", "stok", "musteri", "insan_kaynaklari"] : ["finans"]
    };

    setUsers([...users, user]);
    setNewUser({ name: "", email: "", role: "Kullanıcı", companyId: 0 });
    setShowAddUserDialog(false);

    toast({
      title: "Kullanıcı eklendi",
      description: "Yeni kullanıcı başarıyla eklendi",
      variant: "default",
    });
  };

  return {
    showAddUserDialog,
    setShowAddUserDialog,
    newUser,
    setNewUser,
    handleAddUser
  };
};

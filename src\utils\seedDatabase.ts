
import { query } from './db';
import { v4 as uuidv4 } from 'uuid';

/**
 * Veritabanını test verileri ile dolduran fonksiyon
 */
export const seedDatabase = async () => {
  try {
    console.log('Veritabanına test verileri ekleniyor...');
    
    // Tabloları oluştur (eğer yoksa)
    await createTablesIfNotExist();
    
    // Müşteri verileri ekle
    const musteriIds = await addTestCustomers();
    
    // Ürün verileri ekle
    const urunIds = await addTestProducts();
    
    // Fatura ve kalem verileri ekle
    await addTestInvoices(musteriIds, urunIds);
    
    // Ödeme verileri ekle
    await addTestPayments(musteriIds);
    
    console.log('Test verileri başarıyla eklendi!');
    return true;
  } catch (error) {
    console.error('Test verileri eklenirken hata oluştu:', error);
    return false;
  }
};

/**
 * <PERSON><PERSON><PERSON><PERSON> tabloları oluşturur
 */
const createTablesIfNotExist = async () => {
  // Cari hesaplar tablosu
  await query(`
    CREATE TABLE IF NOT EXISTS cari_hesaplar (
      id VARCHAR(36) PRIMARY KEY,
      kod VARCHAR(20) NOT NULL,
      unvan VARCHAR(100) NOT NULL,
      vknTckn VARCHAR(20),
      tip VARCHAR(20) NOT NULL,
      telefon VARCHAR(20),
      email VARCHAR(100),
      vergi_dairesi VARCHAR(100),
      adres TEXT,
      ilce VARCHAR(50),
      il VARCHAR(50),
      postaKodu VARCHAR(10),
      iban VARCHAR(50),
      notlar TEXT,
      bakiye DECIMAL(15,2) DEFAULT 0,
      olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      kayitTarihi DATE
    )
  `);
  
  // Ürünler tablosu
  await query(`
    CREATE TABLE IF NOT EXISTS urunler (
      id VARCHAR(36) PRIMARY KEY,
      kod VARCHAR(20) NOT NULL,
      isim VARCHAR(100) NOT NULL,
      aciklama TEXT,
      birim VARCHAR(20) NOT NULL,
      fiyat DECIMAL(15,2) NOT NULL,
      kdv_oran DECIMAL(5,2) NOT NULL,
      stok INT DEFAULT 0,
      tur VARCHAR(20) NOT NULL,
      olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `);
  
  // Faturalar tablosu
  await query(`
    CREATE TABLE IF NOT EXISTS faturalar (
      id VARCHAR(36) PRIMARY KEY,
      numara VARCHAR(20) NOT NULL,
      cari_id VARCHAR(36) NOT NULL,
      para_birimi VARCHAR(3) NOT NULL DEFAULT 'TRY',
      ara_toplam DECIMAL(15,2) NOT NULL,
      toplam_vergi DECIMAL(15,2) NOT NULL,
      toplam_iskonto DECIMAL(15,2) DEFAULT 0,
      toplam DECIMAL(15,2) NOT NULL,
      durum VARCHAR(20) NOT NULL,
      odeme_yontemi VARCHAR(20),
      taksit_sayisi INT DEFAULT 1,
      vade_tarihi DATE NOT NULL,
      notlar TEXT,
      e_fatura BOOLEAN DEFAULT false,
      olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (cari_id) REFERENCES cari_hesaplar(id)
    )
  `);
  
  // Fatura kalemleri tablosu
  await query(`
    CREATE TABLE IF NOT EXISTS fatura_kalemleri (
      id VARCHAR(36) PRIMARY KEY,
      fatura_id VARCHAR(36) NOT NULL,
      urun_id VARCHAR(36),
      aciklama TEXT NOT NULL,
      miktar DECIMAL(15,3) NOT NULL,
      birim VARCHAR(20) NOT NULL,
      birim_fiyat DECIMAL(15,2) NOT NULL,
      kdv_oran DECIMAL(5,2) NOT NULL,
      kdv_tutar DECIMAL(15,2) NOT NULL,
      iskonto_oran DECIMAL(5,2) DEFAULT 0,
      iskonto_tutar DECIMAL(15,2) DEFAULT 0,
      satir_toplam DECIMAL(15,2) NOT NULL,
      sira_no INT NOT NULL,
      FOREIGN KEY (fatura_id) REFERENCES faturalar(id),
      FOREIGN KEY (urun_id) REFERENCES urunler(id)
    )
  `);
  
  // Ödemeler tablosu
  await query(`
    CREATE TABLE IF NOT EXISTS odemeler (
      id VARCHAR(36) PRIMARY KEY,
      cari_id VARCHAR(36) NOT NULL,
      fatura_id VARCHAR(36),
      tutar DECIMAL(15,2) NOT NULL,
      odeme_tarihi DATE NOT NULL,
      odeme_yontemi VARCHAR(20) NOT NULL,
      aciklama TEXT,
      referans_no VARCHAR(50),
      olusturma_tarihi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (cari_id) REFERENCES cari_hesaplar(id),
      FOREIGN KEY (fatura_id) REFERENCES faturalar(id)
    )
  `);
};

/**
 * Test müşterilerini ekler
 */
const addTestCustomers = async () => {
  const musteriIds = [];
  
  const customers = [
    {
      kod: "120.01.0001",
      unvan: "Anadolu Ticaret A.Ş.",
      vknTckn: "1234567890",
      tip: "kurumsal",
      telefon: "0212 555 1234",
      email: "<EMAIL>",
      vergi_dairesi: "Kadıköy",
      adres: "Bağdat Caddesi No:123",
      ilce: "Kadıköy",
      il: "İstanbul",
      postaKodu: "34722",
      iban: "**************************",
      notlar: "Sürekli müşteri",
      bakiye: 15000.00
    },
    {
      kod: "120.01.0002",
      unvan: "Mavi Deniz Ltd. Şti.",
      vknTckn: "9876543210",
      tip: "kurumsal",
      telefon: "0216 444 5678",
      email: "<EMAIL>",
      vergi_dairesi: "Beşiktaş",
      adres: "Barbaros Bulvarı No:45",
      ilce: "Beşiktaş",
      il: "İstanbul",
      postaKodu: "34357",
      iban: "**************************",
      notlar: "Net 30 gün vade",
      bakiye: -5000.00
    },
    {
      kod: "120.02.0001",
      unvan: "Mehmet Yılmaz",
      vknTckn: "12345678901",
      tip: "bireysel",
      telefon: "0535 123 4567",
      email: "<EMAIL>",
      vergi_dairesi: "Çankaya",
      adres: "Hoşdere Caddesi No:156/7",
      ilce: "Çankaya",
      il: "Ankara",
      postaKodu: "06540",
      iban: "**************************",
      notlar: "Düzenli ödemeler",
      bakiye: 2500.00
    }
  ];
  
  for (const customer of customers) {
    const id = uuidv4();
    const kayitTarihi = new Date().toISOString().split('T')[0];
    
    await query(
      `INSERT INTO cari_hesaplar 
      (id, kod, unvan, vknTckn, tip, telefon, email, vergi_dairesi, adres, ilce, il, postaKodu, iban, notlar, bakiye, kayitTarihi) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, customer.kod, customer.unvan, customer.vknTckn, customer.tip, customer.telefon, customer.email, 
       customer.vergi_dairesi, customer.adres, customer.ilce, customer.il, customer.postaKodu, 
       customer.iban, customer.notlar, customer.bakiye, kayitTarihi]
    );
    
    musteriIds.push(id);
  }
  
  return musteriIds;
};

/**
 * Test ürünlerini ekler
 */
const addTestProducts = async () => {
  const urunIds = [];
  
  const products = [
    {
      kod: "ST0001",
      isim: "Ofis Sandalyesi",
      aciklama: "Ergonomik ofis sandalyesi",
      birim: "Adet",
      fiyat: 1200.00,
      kdv_oran: 18.00,
      stok: 25,
      tur: "ürün"
    },
    {
      kod: "ST0002",
      isim: "Toplantı Masası",
      aciklama: "10 kişilik toplantı masası",
      birim: "Adet",
      fiyat: 4500.00,
      kdv_oran: 18.00,
      stok: 8,
      tur: "ürün"
    },
    {
      kod: "HZ0001",
      isim: "Yazılım Danışmanlığı",
      aciklama: "Saatlik yazılım danışmanlık hizmeti",
      birim: "Saat",
      fiyat: 850.00,
      kdv_oran: 18.00,
      stok: 100,
      tur: "hizmet"
    }
  ];
  
  for (const product of products) {
    const id = uuidv4();
    
    await query(
      `INSERT INTO urunler 
      (id, kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, product.kod, product.isim, product.aciklama, product.birim, 
       product.fiyat, product.kdv_oran, product.stok, product.tur]
    );
    
    urunIds.push(id);
  }
  
  return urunIds;
};

/**
 * Test faturalarını ekler
 */
const addTestInvoices = async (musteriIds: string[], urunIds: string[]) => {
  const invoices = [
    {
      numara: "FTR-2024-0001",
      cari_id: musteriIds[0],
      para_birimi: "TRY",
      ara_toplam: 4500.00,
      toplam_vergi: 810.00,
      toplam_iskonto: 0.00,
      toplam: 5310.00,
      durum: "odendi",
      odeme_yontemi: "havale",
      taksit_sayisi: 1,
      vade_tarihi: "2024-05-15",
      notlar: "İlk sipariş",
      e_fatura: true,
      items: [
        {
          urun_id: urunIds[0],
          aciklama: "Ofis Sandalyesi - Ergonomik Model",
          miktar: 3,
          birim: "Adet",
          birim_fiyat: 1200.00,
          kdv_oran: 18.00,
          kdv_tutar: 648.00,
          iskonto_oran: 0.00,
          iskonto_tutar: 0.00,
          satir_toplam: 3600.00
        },
        {
          urun_id: urunIds[2],
          aciklama: "Yazılım Danışmanlığı - Kurulum",
          miktar: 1,
          birim: "Saat",
          birim_fiyat: 900.00,
          kdv_oran: 18.00,
          kdv_tutar: 162.00,
          iskonto_oran: 0.00,
          iskonto_tutar: 0.00,
          satir_toplam: 900.00
        }
      ]
    },
    {
      numara: "FTR-2024-0002",
      cari_id: musteriIds[1],
      para_birimi: "TRY",
      ara_toplam: 4500.00,
      toplam_vergi: 810.00,
      toplam_iskonto: 225.00,
      toplam: 5085.00,
      durum: "bekliyor",
      odeme_yontemi: "kredi_karti",
      taksit_sayisi: 3,
      vade_tarihi: "2024-05-30",
      notlar: "Yeni ofis açılışı",
      e_fatura: true,
      items: [
        {
          urun_id: urunIds[1],
          aciklama: "Toplantı Masası",
          miktar: 1,
          birim: "Adet",
          birim_fiyat: 4500.00,
          kdv_oran: 18.00,
          kdv_tutar: 810.00,
          iskonto_oran: 5.00,
          iskonto_tutar: 225.00,
          satir_toplam: 4275.00
        }
      ]
    },
    {
      numara: "FTR-2024-0003",
      cari_id: musteriIds[2],
      para_birimi: "TRY",
      ara_toplam: 3400.00,
      toplam_vergi: 612.00,
      toplam_iskonto: 0.00,
      toplam: 4012.00,
      durum: "bekliyor",
      odeme_yontemi: "nakit",
      taksit_sayisi: 1,
      vade_tarihi: "2024-06-15",
      notlar: "",
      e_fatura: false,
      items: [
        {
          urun_id: urunIds[0],
          aciklama: "Ofis Sandalyesi",
          miktar: 2,
          birim: "Adet",
          birim_fiyat: 1200.00,
          kdv_oran: 18.00,
          kdv_tutar: 432.00,
          iskonto_oran: 0.00,
          iskonto_tutar: 0.00,
          satir_toplam: 2400.00
        },
        {
          urun_id: urunIds[2],
          aciklama: "Yazılım Danışmanlığı",
          miktar: 1,
          birim: "Saat",
          birim_fiyat: 1000.00,
          kdv_oran: 18.00,
          kdv_tutar: 180.00,
          iskonto_oran: 0.00,
          iskonto_tutar: 0.00,
          satir_toplam: 1000.00
        }
      ]
    }
  ];
  
  for (const invoice of invoices) {
    const faturaId = uuidv4();
    
    await query(
      `INSERT INTO faturalar 
      (id, numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto, 
       toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, e_fatura) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [faturaId, invoice.numara, invoice.cari_id, invoice.para_birimi, invoice.ara_toplam, 
       invoice.toplam_vergi, invoice.toplam_iskonto, invoice.toplam, invoice.durum, 
       invoice.odeme_yontemi, invoice.taksit_sayisi, invoice.vade_tarihi, invoice.notlar, invoice.e_fatura]
    );
    
    // Fatura kalemlerini ekle
    for (let i = 0; i < invoice.items.length; i++) {
      const item = invoice.items[i];
      const kalemId = uuidv4();
      
      await query(
        `INSERT INTO fatura_kalemleri 
        (id, fatura_id, urun_id, aciklama, miktar, birim, birim_fiyat, kdv_oran, kdv_tutar, 
         iskonto_oran, iskonto_tutar, satir_toplam, sira_no) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [kalemId, faturaId, item.urun_id, item.aciklama, item.miktar, item.birim, 
         item.birim_fiyat, item.kdv_oran, item.kdv_tutar, item.iskonto_oran, 
         item.iskonto_tutar, item.satir_toplam, i + 1]
      );
    }
  }
};

/**
 * Test ödemeleri ekler
 */
const addTestPayments = async (musteriIds: string[]) => {
  const payments = [
    {
      cari_id: musteriIds[0],
      fatura_id: null, // İlgili fatura ile ilişkilendirilebilir
      tutar: 5310.00,
      odeme_tarihi: "2024-05-10",
      odeme_yontemi: "havale",
      aciklama: "FTR-2024-0001 no'lu fatura ödemesi",
      referans_no: "HVL-123456"
    },
    {
      cari_id: musteriIds[1],
      fatura_id: null,
      tutar: 3000.00,
      odeme_tarihi: "2024-05-05",
      odeme_yontemi: "kredi_karti",
      aciklama: "Kısmi ödeme",
      referans_no: "KK-789012"
    },
    {
      cari_id: musteriIds[2],
      fatura_id: null,
      tutar: 1500.00,
      odeme_tarihi: "2024-05-20",
      odeme_yontemi: "nakit",
      aciklama: "Avans ödemesi",
      referans_no: "NKT-345678"
    }
  ];
  
  for (const payment of payments) {
    const id = uuidv4();
    
    await query(
      `INSERT INTO odemeler 
      (id, cari_id, fatura_id, tutar, odeme_tarihi, odeme_yontemi, aciklama, referans_no) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, payment.cari_id, payment.fatura_id, payment.tutar, payment.odeme_tarihi, 
       payment.odeme_yontemi, payment.aciklama, payment.referans_no]
    );
  }
};

// Seed database fonksiyonunu doğrudan çağırırsak
if (require.main === module) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}

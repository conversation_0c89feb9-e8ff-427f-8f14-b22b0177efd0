
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Printer, Send, Save, CheckCircle, UploadCloud } from "lucide-react";

type TemplateType = "standard" | "detailed" | "simple" | "custom";

interface Template {
  id: string;
  name: string;
  type: TemplateType;
  content: string;
  header: string;
  footer: string;
  isDefault: boolean;
}

export const CustomInvoiceTemplates = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<TemplateType>("standard");
  
  // Example templates
  const [templates, setTemplates] = useState<Template[]>([
    {
      id: "template-1",
      name: "Standart Fatura",
      type: "standard",
      content: "Standart fatura içeriği ve şablonu",
      header: "FATURA",
      footer: "Teşekkür ederiz!",
      isDefault: true
    },
    {
      id: "template-2",
      name: "Detaylı Fatura",
      type: "detailed",
      content: "Detaylı fatura içeriği ve şablonu",
      header: "DETAYLI FATURA",
      footer: "İş birliğiniz için teşekkür ederiz!",
      isDefault: false
    },
    {
      id: "template-3",
      name: "Basit Fatura",
      type: "simple",
      content: "Basit fatura içeriği ve şablonu",
      header: "BASİT FATURA",
      footer: "Teşekkürler!",
      isDefault: false
    }
  ]);
  
  const [customTemplate, setCustomTemplate] = useState<Omit<Template, "id" | "type">>({
    name: "Yeni Şablon",
    content: "",
    header: "ÖZEL FATURA",
    footer: "Teşekkürler!",
    isDefault: false
  });
  
  const saveTemplate = () => {
    // In a real app, this would save to a database
    const newTemplate: Template = {
      id: `template-${Date.now()}`,
      type: "custom",
      ...customTemplate
    };
    
    setTemplates([...templates, newTemplate]);
    
    toast({
      title: "Şablon Kaydedildi",
      description: "Özel fatura şablonu başarıyla kaydedildi.",
    });
  };
  
  const setDefaultTemplate = (id: string) => {
    const updatedTemplates = templates.map(template => ({
      ...template,
      isDefault: template.id === id
    }));
    
    setTemplates(updatedTemplates);
    
    toast({
      title: "Varsayılan Şablon Güncellendi",
      description: "Yeni varsayılan şablon ayarlandı.",
    });
  };
  
  const handleSendInvoice = () => {
    toast({
      title: "Fatura Gönderiliyor",
      description: "Seçili şablon ile fatura müşteriye gönderiliyor.",
    });
    
    // Simulate sending with a success message
    setTimeout(() => {
      toast({
        title: "Fatura Gönderildi",
        description: "Fatura başarıyla müşteriye gönderildi.",
      });
    }, 1500);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Fatura Şablonları</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TemplateType)}>
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="standard">Standart</TabsTrigger>
            <TabsTrigger value="detailed">Detaylı</TabsTrigger>
            <TabsTrigger value="simple">Basit</TabsTrigger>
            <TabsTrigger value="custom">Özel</TabsTrigger>
          </TabsList>
          
          {/* Standard, Detailed, and Simple templates */}
          {["standard", "detailed", "simple"].map((type) => (
            <TabsContent key={type} value={type} className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="bg-gray-100 p-6 rounded-lg mb-4 min-h-[200px]">
                  <div className="text-center font-bold text-xl mb-6">
                    {templates.find(t => t.type === type)?.header}
                  </div>
                  <div className="text-sm">
                    [Fatura İçeriği Önizlemesi]
                    <div className="mt-2">
                      {templates.find(t => t.type === type)?.content}
                    </div>
                  </div>
                  <div className="text-center mt-6 text-sm">
                    {templates.find(t => t.type === type)?.footer}
                  </div>
                </div>
                
                <div className="flex justify-between mt-4">
                  <Button variant="outline" className="gap-2">
                    <Printer className="h-4 w-4" />
                    Yazdır
                  </Button>
                  <div className="space-x-2">
                    {!templates.find(t => t.type === type)?.isDefault && (
                      <Button 
                        variant="outline" 
                        onClick={() => setDefaultTemplate(templates.find(t => t.type === type)?.id || "")}
                      >
                        Varsayılan Yap
                      </Button>
                    )}
                    {templates.find(t => t.type === type)?.isDefault && (
                      <Button variant="outline" className="text-green-600" disabled>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Varsayılan
                      </Button>
                    )}
                    <Button onClick={handleSendInvoice}>
                      <Send className="h-4 w-4 mr-2" />
                      Gönder
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
          
          {/* Custom template editor */}
          <TabsContent value="custom" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="templateName">Şablon Adı</Label>
                  <Input 
                    id="templateName" 
                    value={customTemplate.name}
                    onChange={(e) => setCustomTemplate({...customTemplate, name: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="header">Başlık</Label>
                  <Input 
                    id="header" 
                    value={customTemplate.header}
                    onChange={(e) => setCustomTemplate({...customTemplate, header: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="content">İçerik</Label>
                  <Textarea 
                    id="content" 
                    rows={6}
                    value={customTemplate.content}
                    onChange={(e) => setCustomTemplate({...customTemplate, content: e.target.value})}
                    placeholder="Fatura içeriği..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="footer">Alt Bilgi</Label>
                  <Input 
                    id="footer" 
                    value={customTemplate.footer}
                    onChange={(e) => setCustomTemplate({...customTemplate, footer: e.target.value})}
                  />
                </div>
                
                <div className="flex justify-between">
                  <Button variant="outline" className="gap-2">
                    <UploadCloud className="h-4 w-4" />
                    Logo Yükle
                  </Button>
                  <Button onClick={saveTemplate}>
                    <Save className="h-4 w-4 mr-2" />
                    Şablonu Kaydet
                  </Button>
                </div>
              </div>
              
              <div>
                <Label>Önizleme</Label>
                <div className="border rounded-lg p-4 mt-2">
                  <div className="bg-gray-100 p-6 rounded-lg min-h-[300px]">
                    <div className="text-center font-bold text-xl mb-6">
                      {customTemplate.header}
                    </div>
                    <div className="text-sm whitespace-pre-line">
                      {customTemplate.content || "[Fatura İçeriği Buraya Gelecek]"}
                    </div>
                    <div className="text-center mt-6 text-sm">
                      {customTemplate.footer}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

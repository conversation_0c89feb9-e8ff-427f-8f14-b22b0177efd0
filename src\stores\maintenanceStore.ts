
import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';

export type MaintenanceType = 'periodic' | 'repair' | 'inspection' | 'other';
export type MaintenanceStatus = 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
export type MaintenancePriority = 'low' | 'medium' | 'high' | 'critical';

export interface MaintenanceItem {
  id: string;
  title: string;
  description: string;
  vehicleId: string;
  type: MaintenanceType;
  status: MaintenanceStatus;
  priority: MaintenancePriority;
  scheduledDate: string;
  completedDate?: string;
  cost: number;
  technician: string;
  notes: string;
  parts: MaintenancePart[];
  createdAt: string;
}

export interface MaintenancePart {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface MaintenanceSchedule {
  id: string;
  vehicleId: string;
  title: string;
  description: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  intervalDays?: number;
  nextDate: string;
  reminderDays: number;
  estimatedCost: number;
  isActive: boolean;
  createdAt: string;
}

interface MaintenanceStore {
  maintenanceItems: MaintenanceItem[];
  maintenanceSchedules: MaintenanceSchedule[];
  addMaintenanceItem: (item: Omit<MaintenanceItem, 'id' | 'createdAt'>) => MaintenanceItem;
  updateMaintenanceItem: (id: string, updates: Partial<MaintenanceItem>) => void;
  deleteMaintenanceItem: (id: string) => void;
  addMaintenanceSchedule: (schedule: Omit<MaintenanceSchedule, 'id' | 'createdAt'>) => MaintenanceSchedule;
  updateMaintenanceSchedule: (id: string, updates: Partial<MaintenanceSchedule>) => void;
  deleteMaintenanceSchedule: (id: string) => void;
  generateDemoData: () => void;
}

export const useMaintenanceStore = create<MaintenanceStore>((set) => ({
  maintenanceItems: [],
  maintenanceSchedules: [],
  
  addMaintenanceItem: (item) => {
    const newItem = {
      ...item,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      maintenanceItems: [...state.maintenanceItems, newItem],
    }));
    return newItem;
  },
  
  updateMaintenanceItem: (id, updates) => {
    set((state) => ({
      maintenanceItems: state.maintenanceItems.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  },
  
  deleteMaintenanceItem: (id) => {
    set((state) => ({
      maintenanceItems: state.maintenanceItems.filter((item) => item.id !== id),
    }));
  },
  
  addMaintenanceSchedule: (schedule) => {
    const newSchedule = {
      ...schedule,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      maintenanceSchedules: [...state.maintenanceSchedules, newSchedule],
    }));
    return newSchedule;
  },
  
  updateMaintenanceSchedule: (id, updates) => {
    set((state) => ({
      maintenanceSchedules: state.maintenanceSchedules.map((schedule) =>
        schedule.id === id ? { ...schedule, ...updates } : schedule
      ),
    }));
  },
  
  deleteMaintenanceSchedule: (id) => {
    set((state) => ({
      maintenanceSchedules: state.maintenanceSchedules.filter((schedule) => schedule.id !== id),
    }));
  },
  
  generateDemoData: () => {
    const items: MaintenanceItem[] = [
      {
        id: uuidv4(),
        title: 'Yağ Değişimi',
        description: 'Rutin motor yağı değişimi',
        vehicleId: 'vehicle-1',
        type: 'periodic',
        status: 'completed',
        priority: 'medium',
        scheduledDate: '2023-10-15T10:00:00Z',
        completedDate: '2023-10-15T11:30:00Z',
        cost: 850,
        technician: 'Ahmet Yılmaz',
        notes: 'Filtre de değiştirildi',
        parts: [
          {
            id: uuidv4(),
            name: 'Motor Yağı (5L)',
            quantity: 1,
            unitPrice: 650,
            totalPrice: 650,
          },
          {
            id: uuidv4(),
            name: 'Yağ Filtresi',
            quantity: 1,
            unitPrice: 200,
            totalPrice: 200,
          },
        ],
        createdAt: '2023-10-10T08:30:00Z',
      },
      {
        id: uuidv4(),
        title: 'Fren Balataları Değişimi',
        description: 'Ön fren balatalarının değiştirilmesi',
        vehicleId: 'vehicle-2',
        type: 'repair',
        status: 'scheduled',
        priority: 'high',
        scheduledDate: '2023-11-20T14:00:00Z',
        cost: 1200,
        technician: 'Mehmet Demir',
        notes: '',
        parts: [
          {
            id: uuidv4(),
            name: 'Ön Fren Balataları (Set)',
            quantity: 1,
            unitPrice: 850,
            totalPrice: 850,
          },
          {
            id: uuidv4(),
            name: 'Fren Hidroliği',
            quantity: 1,
            unitPrice: 350,
            totalPrice: 350,
          },
        ],
        createdAt: '2023-11-10T09:15:00Z',
      },
      {
        id: uuidv4(),
        title: 'Periyodik Bakım',
        description: '50.000 KM bakımı',
        vehicleId: 'vehicle-3',
        type: 'periodic',
        status: 'in-progress',
        priority: 'medium',
        scheduledDate: '2023-11-18T09:00:00Z',
        cost: 3500,
        technician: 'Kemal Yıldız',
        notes: 'Triger kayışı değişimi de yapılacak',
        parts: [
          {
            id: uuidv4(),
            name: 'Filtre Seti',
            quantity: 1,
            unitPrice: 1200,
            totalPrice: 1200,
          },
          {
            id: uuidv4(),
            name: 'Triger Seti',
            quantity: 1,
            unitPrice: 1800,
            totalPrice: 1800,
          },
          {
            id: uuidv4(),
            name: 'Motor Yağı',
            quantity: 1,
            unitPrice: 500,
            totalPrice: 500,
          },
        ],
        createdAt: '2023-11-05T11:20:00Z',
      },
    ];
    
    const schedules: MaintenanceSchedule[] = [
      {
        id: uuidv4(),
        vehicleId: 'vehicle-1',
        title: 'Yağ Değişimi',
        description: 'Rutin motor yağı ve filtre değişimi',
        frequency: 'monthly',
        nextDate: '2023-12-15T10:00:00Z',
        reminderDays: 3,
        estimatedCost: 850,
        isActive: true,
        createdAt: '2023-10-15T11:30:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-2',
        title: 'Mevsimlik Lastik Değişimi',
        description: 'Yaz/Kış lastik değişimi',
        frequency: 'custom',
        intervalDays: 180,
        nextDate: '2024-04-15T09:00:00Z',
        reminderDays: 7,
        estimatedCost: 200,
        isActive: true,
        createdAt: '2023-10-20T14:15:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-3',
        title: 'Kilometre Bakımı',
        description: '15.000 KM periyodik bakım',
        frequency: 'custom',
        intervalDays: 120,
        nextDate: '2024-01-20T13:00:00Z',
        reminderDays: 5,
        estimatedCost: 3500,
        isActive: true,
        createdAt: '2023-09-10T10:45:00Z',
      },
    ];
    
    set({ maintenanceItems: items, maintenanceSchedules: schedules });
  },
}));

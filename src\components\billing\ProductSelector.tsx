
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

// Ensure ProductType definition matches what CreateSalesInvoice.tsx expects
export interface ProductType {
  id: string;
  kod: string;
  isim: string;
  aciklama?: string;
  birim: string;
  fiyat: number;
  kdv_oran: number;
  stok: number | null;
}

interface ProductSelectorProps {
  selectedProduct: any;
  onProductSelect: (product: ProductType) => void;
  onChange: (value: string) => void;
  value: string;
}

export const ProductSelector = ({ selectedProduct, onProductSelect, onChange, value }: ProductSelectorProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [products, setProducts] = useState<ProductType[]>([]);

  // Simulate product data - in a real app, this would come from an API
  useEffect(() => {
    // Mock data
    const mockProducts: ProductType[] = [
      {
        id: "1",
        kod: "P001",
        isim: "Laptop",
        aciklama: "High performance laptop",
        birim: "Adet",
        fiyat: 15000,
        kdv_oran: 18,
        stok: 10
      },
      {
        id: "2",
        kod: "P002",
        isim: "Monitör",
        aciklama: "27 inç monitör",
        birim: "Adet",
        fiyat: 3500,
        kdv_oran: 18,
        stok: 15
      },
      {
        id: "3",
        kod: "S001",
        isim: "Teknik Destek",
        aciklama: "1 saatlik teknik destek hizmeti",
        birim: "Saat",
        fiyat: 500,
        kdv_oran: 18,
        stok: null
      }
    ];
    
    setProducts(mockProducts);
  }, []);

  const handleSelect = (product: ProductType) => {
    onProductSelect(product);
    setSearchTerm("");
  };

  return (
    <div className="relative">
      <Input
        placeholder="Ürün/Hizmet Ara veya Ekle"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="h-9"
      />
    </div>
  );
};

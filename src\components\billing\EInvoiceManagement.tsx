
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Upload, Download, Archive } from "lucide-react";

export const EInvoiceManagement = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <FileText className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">E-Faturalar</p>
              <h3 className="text-2xl font-semibold">124</h3>
              <p className="text-sm text-success">Bu ay</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Archive className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-muted-foreground">E-Arşiv</p>
              <h3 className="text-2xl font-semibold">348</h3>
              <p className="text-sm text-muted-foreground">Toplam</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Upload className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Gönderilen</p>
              <h3 className="text-2xl font-semibold">28</h3>
              <p className="text-sm text-success">Bu hafta</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Download className="h-8 w-8 text-orange-500" />
            <div>
              <p className="text-sm text-muted-foreground">Alınan</p>
              <h3 className="text-2xl font-semibold">36</h3>
              <p className="text-sm text-success">Bu hafta</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>E-Fatura Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                E-Fatura oluşturma, gönderme ve takip etme işlemlerinizi bu bölümden yönetebilirsiniz.
              </p>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Yeni E-Fatura
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  E-Fatura Gönder
                </Button>
              </div>
              <div className="rounded-md bg-muted p-4 mt-4">
                <h4 className="text-sm font-medium mb-2">Son Faaliyetler</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex justify-between">
                    <span>E-Fatura #EB2023-421 gönderildi</span>
                    <span className="text-muted-foreground">1 saat önce</span>
                  </li>
                  <li className="flex justify-between">
                    <span>E-Fatura #EB2023-420 reddedildi</span>
                    <span className="text-muted-foreground">3 saat önce</span>
                  </li>
                  <li className="flex justify-between">
                    <span>E-Fatura #EB2023-419 onaylandı</span>
                    <span className="text-muted-foreground">5 saat önce</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>E-Arşiv Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                E-Arşiv faturalarınızı oluşturabilir, saklayabilir ve yönetebilirsiniz.
              </p>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <Archive className="h-4 w-4" />
                  Yeni E-Arşiv
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  E-Arşiv İndir
                </Button>
              </div>
              <div className="rounded-md bg-muted p-4 mt-4">
                <h4 className="text-sm font-medium mb-2">E-Arşiv Saklama Durumu</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Kullanılan Alan</span>
                    <span className="text-sm font-medium">4.2 GB / 10 GB</span>
                  </div>
                  <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: "42%" }}></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    E-Arşiv faturaları yasal olarak 10 yıl saklanır.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

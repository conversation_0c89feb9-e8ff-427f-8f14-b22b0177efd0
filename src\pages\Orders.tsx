
import MainLayout from "@/components/layout/MainLayout";
import { OrderList } from "@/components/orders/OrderList";
import { CreateOrderButton } from "@/components/orders/CreateOrderButton";
import { useOrderStore } from "@/stores/orderStore";
import { useEffect } from "react";
import { OrderStats } from "@/components/orders/OrderStats";

const Orders = () => {
  const { orders, loadDemoData } = useOrderStore();

  // Load demo data if there are no orders
  useEffect(() => {
    if (orders.length === 0) {
      loadDemoData();
    }
  }, [orders.length, loadDemoData]);

  return (
    <MainLayout>
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON><PERSON><PERSON></h2>
            <p className="text-muted-foreground">
              Si<PERSON>iş<PERSON><PERSON>, takip edin ve yönetin
            </p>
          </div>
          <CreateOrderButton />
        </div>
        <OrderStats />
        <OrderList />
      </div>
    </MainLayout>
  );
};

export default Orders;

import { toast } from "sonner";

// API_URL'yi <PERSON>, do<PERSON><PERSON>an göreli yollar kullanacağız

// API yanıtlarını işlemek için yardımcı fonksiyon
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Bilinmeyen hata' }));
    throw new Error(errorData.error || `Hata kodu: ${response.status}`);
  }
  return response.json();
};

// API isteklerinde hata yönetimi için yardımcı fonksiyon
const handleApiError = (error: any, errorMessage: string) => {
  console.error(errorMessage, error);
  toast.error(error.message || errorMessage);
  throw error;
};

// Cari hesaplar için API fonksiyonları
export const cariApi = {
  getAll: async () => {
    try {
      const response = await fetch(`/api/cari`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari hesaplar alınırken bir hata oluştu');
    }
  },

  getById: async (id: string) => {
    try {
      const response = await fetch(`/api/cari/${id}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari hesap alınırken bir hata oluştu');
    }
  },

  create: async (data: any) => {
    try {
      const response = await fetch(`/api/cari`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari hesap oluşturulurken bir hata oluştu');
    }
  },

  update: async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/cari/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari hesap güncellenirken bir hata oluştu');
    }
  },

  delete: async (id: string) => {
    try {
      const response = await fetch(`/api/cari/${id}`, {
        method: 'DELETE'
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari hesap silinirken bir hata oluştu');
    }
  }
};

// Ürünler için API fonksiyonları
export const urunApi = {
  getAll: async () => {
    try {
      const response = await fetch(`/api/urun`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ürünler alınırken bir hata oluştu');
    }
  },

  getById: async (id: string) => {
    try {
      const response = await fetch(`/api/urun/${id}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ürün alınırken bir hata oluştu');
    }
  },

  create: async (data: any) => {
    try {
      const response = await fetch(`/api/urun`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ürün oluşturulurken bir hata oluştu');
    }
  },

  update: async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/urun/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ürün güncellenirken bir hata oluştu');
    }
  },

  delete: async (id: string) => {
    try {
      const response = await fetch(`/api/urun/${id}`, {
        method: 'DELETE'
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ürün silinirken bir hata oluştu');
    }
  }
};

// Faturalar için API fonksiyonları
export const faturaApi = {
  getAll: async () => {
    try {
      const response = await fetch(`/api/fatura`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Faturalar alınırken bir hata oluştu');
    }
  },

  getById: async (id: string) => {
    try {
      const response = await fetch(`/api/fatura/${id}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Fatura alınırken bir hata oluştu');
    }
  },

  create: async (data: any) => {
    try {
      const response = await fetch(`/api/fatura`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Fatura oluşturulurken bir hata oluştu');
    }
  },

  update: async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/fatura/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Fatura güncellenirken bir hata oluştu');
    }
  },

  delete: async (id: string) => {
    try {
      const response = await fetch(`/api/fatura/${id}`, {
        method: 'DELETE'
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Fatura silinirken bir hata oluştu');
    }
  }
};

// Ödemeler için API fonksiyonları
export const odemeApi = {
  getAll: async () => {
    try {
      const response = await fetch(`/api/odeme`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ödemeler alınırken bir hata oluştu');
    }
  },

  getById: async (id: string) => {
    try {
      const response = await fetch(`/api/odeme/${id}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ödeme alınırken bir hata oluştu');
    }
  },

  getByCariId: async (cariId: string) => {
    try {
      const response = await fetch(`/api/odeme/cari/${cariId}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Cari ödemeleri alınırken bir hata oluştu');
    }
  },

  getByFaturaId: async (faturaId: string) => {
    try {
      const response = await fetch(`/api/odeme/fatura/${faturaId}`);
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Fatura ödemeleri alınırken bir hata oluştu');
    }
  },

  create: async (data: any) => {
    try {
      const response = await fetch(`/api/odeme`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ödeme oluşturulurken bir hata oluştu');
    }
  },

  update: async (id: string, data: any) => {
    try {
      const response = await fetch(`/api/odeme/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ödeme güncellenirken bir hata oluştu');
    }
  },

  delete: async (id: string) => {
    try {
      const response = await fetch(`/api/odeme/${id}`, {
        method: 'DELETE'
      });
      return handleResponse(response);
    } catch (error) {
      return handleApiError(error, 'Ödeme silinirken bir hata oluştu');
    }
  }
};

// Müşteri seçimini uygulamadan kullanabilmek için:
export const getCustomerOptions = async () => {
  try {
    const cariHesaplar = await cariApi.getAll();
    return cariHesaplar.map((cari: any) => ({
      id: cari.id,
      kod: cari.kod,
      unvan: cari.unvan,
      vknTckn: cari.vknTckn,
      tip: cari.tip,
      il: cari.il
    }));
  } catch (error) {
    console.error('Müşteri seçenekleri alınırken hata oluştu:', error);
    return [];
  }
};

// Ürün seçimini uygulamadan kullanabilmek için:
export const getProductOptions = async () => {
  try {
    const urunler = await urunApi.getAll();
    return urunler.map((urun: any) => ({
      id: urun.id,
      kod: urun.kod,
      isim: urun.isim,
      birim: urun.birim,
      fiyat: urun.fiyat,
      kdv_oran: urun.kdv_oran,
      stok: urun.stok
    }));
  } catch (error) {
    console.error('Ürün seçenekleri alınırken hata oluştu:', error);
    return [];
  }
};

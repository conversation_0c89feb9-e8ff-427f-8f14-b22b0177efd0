
import { useReportsStore } from "@/stores/reportsStore";
import { useEffect, useState } from "react";
import { Pie<PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts";

export const OrderStatusChart = () => {
  const { ordersData, generateDemoData } = useReportsStore();
  const [chartData, setChartData] = useState<any[]>([]);
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#FF6B6B'];
  
  useEffect(() => {
    if (ordersData.length === 0) {
      generateDemoData();
    } else {
      // Format data for chart
      setChartData(ordersData.map(item => ({
        name: mapStatusName(item.status),
        value: item.count
      })));
    }
  }, [ordersData, generateDemoData]);
  
  // Map status codes to Turkish names
  const mapStatusName = (status: string) => {
    switch (status) {
      case 'pending': return 'Beklemede';
      case 'processing': return 'İşlemde';
      case 'shipped': return 'Kargoda';
      case 'delivered': return 'Teslim Edildi';
      case 'cancelled': return 'İptal Edildi';
      case 'returned': return 'İade Edildi';
      default: return status;
    }
  };

  if (chartData.length === 0) {
    return <div className="flex justify-center items-center h-60">Yükleniyor...</div>;
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => [value, 'Adet']} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

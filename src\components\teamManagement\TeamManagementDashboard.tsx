
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, CheckCircle, Clock, Users, AlertTriangle, Calendar as CalendarIcon } from "lucide-react";

export const TeamManagementDashboard = () => {
  return (
    <Tabs defaultValue="overview" className="space-y-4">
      <TabsList>
        <TabsTrigger value="overview"><PERSON><PERSON> Bakış</TabsTrigger>
        <TabsTrigger value="tasks">Görev Yönetimi</TabsTrigger>
        <TabsTrigger value="projects">Proje <PERSON></TabsTrigger>
        <TabsTrigger value="communication">Ekip İletişimi</TabsTrigger>
        <TabsTrigger value="shifts">Vardiya Yönetimi</TabsTrigger>
        <TabsTrigger value="permissions"><PERSON><PERSON> Yönetimi</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="p-4">
            <div className="flex items-center gap-4">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Tamamlanan Görevler</p>
                <h3 className="text-2xl font-semibold">24</h3>
                <p className="text-sm text-success">+5 bugün</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-4">
              <Clock className="h-8 w-8 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">Bekleyen Görevler</p>
                <h3 className="text-2xl font-semibold">18</h3>
                <p className="text-sm text-warning">3 gecikmiş</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-4">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Aktif Projeler</p>
                <h3 className="text-2xl font-semibold">8</h3>
                <p className="text-sm text-info">2 tamamlanmak üzere</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-4">
              <CalendarIcon className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm text-muted-foreground">İzin Talepleri</p>
                <h3 className="text-2xl font-semibold">5</h3>
                <p className="text-sm text-muted-foreground">Onay bekliyor</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Yaklaşan Görevler</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { id: 1, title: "Rapor Hazırlanması", dueDate: "Bugün", priority: "Yüksek", assignee: "Ahmet Y." },
                  { id: 2, title: "Müşteri Toplantısı", dueDate: "Yarın, 14:00", priority: "Orta", assignee: "Mehmet K." },
                  { id: 3, title: "Sunum Hazırlığı", dueDate: "21 Haziran", priority: "Yüksek", assignee: "Ayşe T." },
                  { id: 4, title: "Haftalık Rapor", dueDate: "22 Haziran", priority: "Düşük", assignee: "Ali R." },
                ].map(task => (
                  <div key={task.id} className="flex items-center justify-between border-b pb-2">
                    <div>
                      <div className="font-medium">{task.title}</div>
                      <div className="text-sm text-muted-foreground">{task.dueDate} • {task.assignee}</div>
                    </div>
                    <Badge className={
                      task.priority === "Yüksek" ? "bg-red-500" : 
                      task.priority === "Orta" ? "bg-yellow-500" : "bg-green-500"
                    }>
                      {task.priority}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Proje Durumları</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { id: 1, title: "Web Sitesi Yenileme", progress: 85, team: "Yazılım Ekibi", deadline: "28 Haziran" },
                  { id: 2, title: "Mobil Uygulama Geliştirme", progress: 60, team: "Mobil Ekip", deadline: "15 Temmuz" },
                  { id: 3, title: "Ürün Kataloğu Çekimi", progress: 40, team: "Pazarlama", deadline: "10 Temmuz" },
                  { id: 4, title: "CRM Entegrasyonu", progress: 20, team: "IT Ekibi", deadline: "5 Ağustos" },
                ].map(project => (
                  <div key={project.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{project.title}</div>
                      <div className="text-sm">{project.progress}%</div>
                    </div>
                    <div className="h-2 w-full bg-muted overflow-hidden rounded-full">
                      <div 
                        className={`h-full ${
                          project.progress > 75 ? "bg-green-500" : 
                          project.progress > 50 ? "bg-blue-500" : 
                          project.progress > 25 ? "bg-yellow-500" : "bg-red-500"
                        }`} 
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {project.team} • Bitiş: {project.deadline}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>İzin ve Mesai Durumu</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Bugün İzinli Personel</h4>
                    <p className="text-sm text-muted-foreground">5 personel izinde</p>
                  </div>
                  <Button variant="outline" size="sm">Tümünü Gör</Button>
                </div>
                <div className="space-y-2">
                  {["Ali Yılmaz", "Ayşe Kaya", "Mehmet Demir", "Zeynep Şahin", "Ahmet Çelik"].map((name, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 rounded-full bg-blue-500" />
                      <span>{name}</span>
                      <span className="text-muted-foreground ml-auto">Yıllık İzin</span>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 pt-4 border-t">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium">Fazla Mesai Bilgileri</h4>
                      <p className="text-sm text-muted-foreground">Bu haftaki toplam: 36 saat</p>
                    </div>
                    <Button variant="outline" size="sm">Onay Bekleyen</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Vardiya Planlaması</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Bugünkü Vardiyalar</h4>
                    <p className="text-sm text-muted-foreground">21 Haziran, Çarşamba</p>
                  </div>
                  <Button variant="outline" size="sm">Takvimi Gör</Button>
                </div>
                
                <div className="space-y-2">
                  {[
                    { time: "08:00 - 16:00", team: "Satış Ekibi", count: 8 },
                    { time: "10:00 - 18:00", team: "Müşteri Hizmetleri", count: 5 },
                    { time: "16:00 - 00:00", team: "Teknik Destek", count: 3 },
                  ].map((shift, i) => (
                    <div key={i} className="flex items-center justify-between border p-2 rounded-md">
                      <div>
                        <div className="font-medium">{shift.time}</div>
                        <div className="text-sm text-muted-foreground">{shift.team}</div>
                      </div>
                      <Badge variant="outline">{shift.count} kişi</Badge>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 flex justify-between items-center pt-4 border-t">
                  <Badge variant="outline" className="flex items-center gap-2">
                    <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    <span>Yarın için 2 eksik personel</span>
                  </Badge>
                  <Button size="sm">Vardiya Ekle</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      
      {/* Diğer tab içerikleri sonradan uygulanacak */}
      <TabsContent value="tasks" className="space-y-4">
        <Card className="p-6">
          <CardHeader>
            <CardTitle>Görev Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Bu bölüm görevlerin atanması, takibi ve tamamlanmasına olanak tanır.</p>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="projects" className="space-y-4">
        <Card className="p-6">
          <CardHeader>
            <CardTitle>Proje Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Bu bölüm projelerin planlanması, kaynakların yönetilmesi ve ilerlemenin izlenmesini sağlar.</p>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="communication" className="space-y-4">
        <Card className="p-6">
          <CardHeader>
            <CardTitle>Ekip İletişimi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Bu bölüm ekiplerin kolayca iletişim kurmasını ve bilgi paylaşmasını sağlar.</p>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="shifts" className="space-y-4">
        <Card className="p-6">
          <CardHeader>
            <CardTitle>Vardiya Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Bu bölüm çalışanların vardiyalarını planlama ve yönetme imkanı sunar.</p>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="permissions" className="space-y-4">
        <Card className="p-6">
          <CardHeader>
            <CardTitle>İzin Yönetimi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Bu bölüm çalışanların izin taleplerini ve fazla mesai takibini kolaylaştırır.</p>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

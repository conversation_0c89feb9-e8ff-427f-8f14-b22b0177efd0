
import { 
  Card, 
  CardContent
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  MaintenanceItem, 
  MaintenanceStatus,
  MaintenancePriority
} from "@/stores/maintenanceStore";
import { Vehicle } from "@/stores/vehicleStore";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Edit, Trash, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MaintenanceItemsListProps {
  items: MaintenanceItem[];
  vehicles: Vehicle[];
}

export const MaintenanceItemsList = ({ items, vehicles }: MaintenanceItemsListProps) => {
  const { toast } = useToast();
  
  const getVehicleName = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    return vehicle ? `${vehicle.name} (${vehicle.plate})` : 'Bilinmiyor';
  };
  
  const getStatusBadge = (status: MaintenanceStatus) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">Planlandı</Badge>;
      case 'in-progress':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-300">Devam Ediyor</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">Tamamlandı</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">İptal</Badge>;
      default:
        return <Badge variant="outline">Bilinmiyor</Badge>;
    }
  };
  
  const getPriorityBadge = (priority: MaintenancePriority) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-300">Düşük</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">Orta</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-300">Yüksek</Badge>;
      case 'critical':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">Kritik</Badge>;
      default:
        return <Badge variant="outline">Normal</Badge>;
    }
  };
  
  const handleViewDetails = (id: string) => {
    toast({
      title: "Bakım Detayları",
      description: "Bakım detayları görüntüleniyor.",
    });
  };
  
  const handleEdit = (id: string) => {
    toast({
      title: "Düzenleme",
      description: "Bakım kaydı düzenleme özelliği henüz geliştiriliyor.",
    });
  };
  
  const handleDelete = (id: string) => {
    toast({
      title: "Silme",
      description: "Bakım kaydı silme özelliği henüz geliştiriliyor.",
    });
  };
  
  if (items.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Henüz bakım kaydı bulunmuyor.</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Başlık</TableHead>
              <TableHead>Araç</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Öncelik</TableHead>
              <TableHead>Tarih</TableHead>
              <TableHead>Maliyet</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.title}</TableCell>
                <TableCell>{getVehicleName(item.vehicleId)}</TableCell>
                <TableCell>{getStatusBadge(item.status)}</TableCell>
                <TableCell>{getPriorityBadge(item.priority)}</TableCell>
                <TableCell>{format(new Date(item.scheduledDate), 'dd MMM yyyy', { locale: tr })}</TableCell>
                <TableCell>{item.cost.toLocaleString('tr-TR')} ₺</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleViewDetails(item.id)}
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleEdit(item.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="text-red-600"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Demo data
const promissoryNotesData = [
  { id: "1", noteNo: "B123456", issuer: "ABC Ltd. Şti.", dueDate: "2024-08-18", amount: 18500.00, status: "pending", type: "receivable", issueDate: "2024-06-18", description: "Müşteri ödemesi" },
  { id: "2", noteNo: "B234567", issuer: "DEF A.Ş.", dueDate: "2024-08-25", amount: 25750.00, status: "endorsed", type: "receivable", issueDate: "2024-06-25", description: "Müşteri ödemesi" },
  { id: "3", noteNo: "B345678", issuer: "GHI Ltd.", dueDate: "2024-09-01", amount: 22000.00, status: "deposited", type: "receivable", issueDate: "2024-07-01", description: "Müşteri ödemesi" },
  { id: "4", noteNo: "B456789", issuer: "MNO A.Ş.", dueDate: "2024-09-10", amount: 42500.00, status: "pending", type: "payable", issueDate: "2024-07-10", description: "Tedarikçi ödemesi" },
  { id: "5", noteNo: "B567890", issuer: "XYZ Dağıtım A.Ş.", dueDate: "2024-09-15", amount: 38750.00, status: "delivered", type: "payable", issueDate: "2024-07-15", description: "Tedarikçi ödemesi" }
];

// Column definitions
const promissoryNotesColumns = [
  {
    header: "Senet No",
    accessorKey: "noteNo"
  },
  {
    header: "Düzenleyen",
    accessorKey: "issuer"
  },
  {
    header: "Vade",
    accessorKey: "dueDate"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => {
      return <span className="font-medium">₺ {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "Tür",
    accessorKey: "type",
    cell: ({ row }) => {
      const type = row.original.type;
      const badgeVariant = type === "receivable" ? "success" : "warning";
      return <Badge variant={badgeVariant}>{type === "receivable" ? "Alınan Senet" : "Verilen Senet"}</Badge>;
    }
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant: "default" | "secondary" | "success" | "warning" = "default";
      let statusLabel = "";
      
      switch(status) {
        case "pending":
          badgeVariant = "default";
          statusLabel = "Beklemede";
          break;
        case "endorsed":
          badgeVariant = "secondary";
          statusLabel = "Ciro Edildi";
          break;
        case "deposited":
          badgeVariant = "success";
          statusLabel = "Bankaya Verildi";
          break;
        case "delivered":
          badgeVariant = "warning";
          statusLabel = "Teslim Edildi";
          break;
        default:
          statusLabel = status;
      }
      
      return <Badge variant={badgeVariant}>{statusLabel}</Badge>;
    }
  },
  {
    header: "Düzenleme Tarihi",
    accessorKey: "issueDate"
  },
  {
    header: "Açıklama",
    accessorKey: "description"
  }
];

export const PromissoryNoteManagement = () => {
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  
  // Filter data based on selected type
  const filteredData = typeFilter 
    ? promissoryNotesData.filter(note => note.type === typeFilter)
    : promissoryNotesData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={typeFilter === null ? "default" : "outline"}
            onClick={() => setTypeFilter(null)}
          >
            Tüm Senetler
          </Button>
          <Button 
            variant={typeFilter === "receivable" ? "default" : "outline"}
            onClick={() => setTypeFilter("receivable")}
          >
            Alınan Senetler
          </Button>
          <Button 
            variant={typeFilter === "payable" ? "default" : "outline"}
            onClick={() => setTypeFilter("payable")}
          >
            Verilen Senetler
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Senet Ekle
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Senet Yönetimi</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={promissoryNotesColumns} data={filteredData} />
        </CardContent>
      </Card>
    </div>
  );
};

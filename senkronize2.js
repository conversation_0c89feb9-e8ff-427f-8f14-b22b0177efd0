import mysql from 'mysql2/promise'; // Promise tabanlı mysql2 kullanıyoruz

// Veritabanı bağlantı bilgileri
const sourceConfig = {
    host: 'localhost',
    user: 'mehmet',
    password: 'Mb_07112024',
    database: 'borudata'
};

const targetConfig = {
    host: '***********',
    port: 1481,
    user: 'mehmet',
    password: 'Bormeg.mS_07112024s.tech',
    database: 'borudata'
};

// Genel senkronizasyon fonksiyonu - tüm tablolar için kullanılabilir
async function syncStokKodu(tableNumber) {
    let sourceConn = null;
    let targetConn = null;
    const tableName = `silodata${tableNumber}`;

    try {
        console.log(`Senkronizasyon${tableNumber} başlıyor...`, new Date().toISOString());

        // Kaynak veritabanına bağlan
        sourceConn = await mysql.createConnection(sourceConfig);

        // Kaynak veritabanından NULL olmayan en son stok kodunu al
        const [sourceResults] = await sourceConn.query(`
            SELECT id, yeni_stok_kodu
            FROM ${tableName}
            WHERE yeni_stok_kodu IS NOT NULL
            ORDER BY id DESC
            LIMIT 1
        `);

        if (!sourceResults.length) {
            console.log(`${tableName} - Güncellenecek kayıt yok`);
            return;
        }

        // Hedef veritabanına bağlan
        targetConn = await mysql.createConnection(targetConfig);

        // Hedef veritabanındaki en son kaydı güncelle
        const [updateResult] = await targetConn.query(
            `UPDATE ${tableName} SET yeni_stok_kodu = ? WHERE id = (SELECT id FROM (SELECT id FROM ${tableName} ORDER BY id DESC LIMIT 1) AS t)`,
            [sourceResults[0].yeni_stok_kodu]
        );

        if (updateResult.affectedRows > 0) {
            console.log(`Senkronizasyon${tableNumber} başarılı:`, {
                tablo: tableName,
                kaynak_id: sourceResults[0].id,
                stok_kodu: sourceResults[0].yeni_stok_kodu,
                timestamp: new Date().toISOString()
            });
        } else {
            console.log(`${tableName} - Hedef veritabanında güncellenecek kayıt bulunamadı`);
        }

    } catch (error) {
        console.error(`Senkronizasyon${tableNumber} hatası:`, {
            tablo: tableName,
            message: error.message,
            code: error.code,
            timestamp: new Date().toISOString()
        });
    } finally {
        // Bağlantıları kapat
        if (sourceConn) {
            try {
                await sourceConn.end();
            } catch (err) {
                console.error(`${tableName} - Kaynak bağlantı kapatma hatası:`, err);
            }
        }
        if (targetConn) {
            try {
                await targetConn.end();
            } catch (err) {
                console.error(`${tableName} - Hedef bağlantı kapatma hatası:`, err);
            }
        }
    }
}

// Her tablo için ayrı fonksiyonlar
async function syncStokKodu2() {
    await syncStokKodu(2);
}

async function syncStokKodu3() {
    await syncStokKodu(3);
}

async function syncStokKodu4() {
    await syncStokKodu(4);
}

async function syncStokKodu5() {
    await syncStokKodu(5);
}

// Tüm senkronizasyon işlemlerini paralel olarak çalıştıran ana fonksiyon
async function runAllSynchronizations() {
    try {
        // Tüm senkronizasyon işlemlerini paralel olarak çalıştır
        await Promise.allSettled([
            syncStokKodu2(),
            syncStokKodu3(),
            syncStokKodu4(),
            syncStokKodu5()
        ]);
    } catch (error) {
        console.error('Genel senkronizasyon hatası:', error);
    }
}

// Birleşik senkronizasyon servisi başlatılıyor
console.log('='.repeat(60));
console.log('🚀 Birleşik Senkronizasyon Servisi Başlatılıyor...');
console.log('📊 Senkronize edilecek tablolar: silodata2, silodata3, silodata4, silodata5');
console.log('⏱️  Çalışma aralığı: 30 saniye');
console.log('='.repeat(60));

// İlk çalıştırma
runAllSynchronizations();

// 30 saniyede bir tüm senkronizasyon işlemlerini çalıştır
setInterval(runAllSynchronizations, 30000);

// Hata yönetimi
process.on('SIGTERM', () => {
    console.log('\n🛑 Servis durduruluyor... (SIGTERM)');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Servis durduruluyor... (SIGINT)');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Yakalanmamış hata:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ İşlenmeyen Promise reddi:', reason);
    console.error('Promise:', promise);
});
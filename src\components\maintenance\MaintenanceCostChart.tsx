
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts";
import { useMaintenanceStore } from "@/stores/maintenanceStore";
import { useVehicleStore } from "@/stores/vehicleStore";

export const MaintenanceCostChart = () => {
  const { maintenanceItems } = useMaintenanceStore();
  const { vehicles } = useVehicleStore();
  
  // Process data for the chart
  const getVehicleName = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    return vehicle ? vehicle.name : 'Bilinmiyor';
  };
  
  // Group maintenance items by vehicle
  const costsByVehicle = maintenanceItems
    .filter(item => item.status === 'completed')
    .reduce((acc, item) => {
      const vehicleName = getVehicleName(item.vehicleId);
      
      if (!acc[vehicleName]) {
        acc[vehicleName] = {
          name: vehicleName,
          periodic: 0,
          repair: 0,
          inspection: 0,
          other: 0,
          total: 0,
        };
      }
      
      acc[vehicleName][item.type] += item.cost;
      acc[vehicleName].total += item.cost;
      
      return acc;
    }, {} as Record<string, any>);
  
  const chartData = Object.values(costsByVehicle);
  
  return (
    <Card className="col-span-2 card-modern">
      <CardHeader>
        <CardTitle>Bakım Maliyetleri</CardTitle>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="name" />
              <YAxis 
                label={{ 
                  value: 'Maliyet (₺)', 
                  angle: -90, 
                  position: 'insideLeft' 
                }} 
              />
              <Tooltip 
                formatter={(value) => [`${Number(value).toLocaleString('tr-TR')} ₺`, '']}
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '8px',
                  padding: '10px',
                  border: '1px solid #f0f0f0',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
                }}  
              />
              <Legend wrapperStyle={{ paddingTop: '10px' }} />
              <Bar dataKey="periodic" name="Periyodik" fill="#8884d8" radius={[4, 4, 0, 0]} />
              <Bar dataKey="repair" name="Onarım" fill="#82ca9d" radius={[4, 4, 0, 0]} />
              <Bar dataKey="inspection" name="Muayene" fill="#ffc658" radius={[4, 4, 0, 0]} />
              <Bar dataKey="other" name="Diğer" fill="#ff8042" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};


import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { BanknoteIcon, CreditCard, ArrowUpCircle, ArrowDownCircle, Building, Wallet } from "lucide-react";

// Demo data
const recentTransactions = [
  { id: 1, type: "gelir", description: "Müşteri Ödemesi - ABC Ltd.", amount: 12500, date: "15 Haziran", category: "Satış", account: "Yapı Kredi" },
  { id: 2, type: "gider", description: "Kira Ödemesi", amount: 5800, date: "12 Haziran", category: "Ofis Giderleri", account: "İş Bankası" },
  { id: 3, type: "gelir", description: "Müşteri Ödemesi - XYZ A.Ş.", amount: 8750, date: "10 Haziran", category: "Hizmet", account: "Garanti" },
  { id: 4, type: "gider", description: "Elektrik Faturası", amount: 1250, date: "8 Haziran", category: "Ofis Giderleri", account: "İş Bankası" },
  { id: 5, type: "gider", description: "Malzeme Alımı", amount: 3600, date: "5 Haziran", category: "Satın Alma", account: "Yapı Kredi" },
];

export const IncomeExpenseTracker = () => {
  return (
    <div className="space-y-4">
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-3">
          <div className="flex items-center gap-3">
            <ArrowUpCircle className="h-7 w-7 text-green-500" />
            <div>
              <p className="text-xs text-muted-foreground">Toplam Gelir</p>
              <h3 className="text-xl font-semibold">₺329,000</h3>
              <p className="text-xs text-success">+12% geçen aya göre</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-3">
            <ArrowDownCircle className="h-7 w-7 text-red-500" />
            <div>
              <p className="text-xs text-muted-foreground">Toplam Gider</p>
              <h3 className="text-xl font-semibold">₺230,000</h3>
              <p className="text-xs text-danger">+8% geçen aya göre</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-3">
            <Building className="h-7 w-7 text-blue-500" />
            <div>
              <p className="text-xs text-muted-foreground">Banka Bakiyesi</p>
              <h3 className="text-xl font-semibold">₺158,420</h3>
              <p className="text-xs text-muted-foreground">3 banka hesabı</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-3">
            <Wallet className="h-7 w-7 text-purple-500" />
            <div>
              <p className="text-xs text-muted-foreground">Kasa Bakiyesi</p>
              <h3 className="text-xl font-semibold">₺12,580</h3>
              <p className="text-xs text-muted-foreground">2 kasa</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-3 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader className="py-3">
            <CardTitle className="text-lg">Hesap Özeti</CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-sm font-medium">Toplam Varlık</p>
                <p className="text-xl font-bold">₺516,800</p>
              </div>
              <div>
                <p className="text-sm font-medium">Net Pozisyon</p>
                <p className="text-xl font-bold text-green-600">+₺99,000</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span>Toplam Banka Varlığı</span>
                <span className="font-medium">₺158,420</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span>Toplam Kasa Varlığı</span>
                <span className="font-medium">₺12,580</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span>Tahsil Edilecek Alacaklar</span>
                <span className="font-medium">₺345,800</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-lg">Hesap Yönetimi</CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3">
              <h4 className="text-xs font-medium">Banka Hesapları</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center border-b pb-1">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-3 w-3 text-blue-500" />
                    <span className="text-xs">Yapı Kredi - TL</span>
                  </div>
                  <span className="text-xs font-medium">₺84,250</span>
                </div>
                <div className="flex justify-between items-center border-b pb-1">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-3 w-3 text-green-500" />
                    <span className="text-xs">Garanti - TL</span>
                  </div>
                  <span className="text-xs font-medium">₺42,870</span>
                </div>
                <div className="flex justify-between items-center border-b pb-1">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-3 w-3 text-purple-500" />
                    <span className="text-xs">İş Bankası - TL</span>
                  </div>
                  <span className="text-xs font-medium">₺31,300</span>
                </div>
              </div>
              
              <h4 className="text-xs font-medium mt-3">Kasa</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center border-b pb-1">
                  <div className="flex items-center gap-2">
                    <Wallet className="h-3 w-3 text-orange-500" />
                    <span className="text-xs">Ana Kasa - TL</span>
                  </div>
                  <span className="text-xs font-medium">₺10,250</span>
                </div>
                <div className="flex justify-between items-center border-b pb-1">
                  <div className="flex items-center gap-2">
                    <Wallet className="h-3 w-3 text-red-500" />
                    <span className="text-xs">Yedek Kasa - TL</span>
                  </div>
                  <span className="text-xs font-medium">₺2,330</span>
                </div>
              </div>
              
              <div className="flex justify-end mt-3">
                <Button variant="outline" size="sm" className="h-7 text-xs">Hesap Ekle</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader className="py-3">
          <CardTitle className="text-lg">Son İşlemler</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-3">
              <TabsTrigger value="all">Tümü</TabsTrigger>
              <TabsTrigger value="income">Gelirler</TabsTrigger>
              <TabsTrigger value="expense">Giderler</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="space-y-3">
              <div className="relative overflow-x-auto rounded-md border">
                <table className="w-full text-sm">
                  <thead className="bg-muted text-muted-foreground">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs">İşlem</th>
                      <th className="px-3 py-2 text-left text-xs">Kategori</th>
                      <th className="px-3 py-2 text-left text-xs">Hesap</th>
                      <th className="px-3 py-2 text-left text-xs">Tarih</th>
                      <th className="px-3 py-2 text-right text-xs">Tutar</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {recentTransactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-muted/50">
                        <td className="px-3 py-2">
                          <div className="flex items-center gap-2">
                            {transaction.type === "gelir" ? (
                              <ArrowUpCircle className="h-3 w-3 text-green-500" />
                            ) : (
                              <ArrowDownCircle className="h-3 w-3 text-red-500" />
                            )}
                            <span className="text-xs">{transaction.description}</span>
                          </div>
                        </td>
                        <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.category}</td>
                        <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.account}</td>
                        <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.date}</td>
                        <td className={`px-3 py-2 text-right font-medium text-xs ${
                          transaction.type === "gelir" ? "text-green-600" : "text-red-600"
                        }`}>
                          {transaction.type === "gelir" ? "+" : "-"}₺{transaction.amount.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="flex justify-end">
                <Button variant="outline" size="sm" className="h-7 text-xs">Tüm İşlemleri Gör</Button>
              </div>
            </TabsContent>
            
            <TabsContent value="income" className="space-y-3">
              <div className="relative overflow-x-auto rounded-md border">
                <table className="w-full text-sm">
                  <thead className="bg-muted text-muted-foreground">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs">İşlem</th>
                      <th className="px-3 py-2 text-left text-xs">Kategori</th>
                      <th className="px-3 py-2 text-left text-xs">Hesap</th>
                      <th className="px-3 py-2 text-left text-xs">Tarih</th>
                      <th className="px-3 py-2 text-right text-xs">Tutar</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {recentTransactions
                      .filter(t => t.type === "gelir")
                      .map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-muted/50">
                          <td className="px-3 py-2">
                            <div className="flex items-center gap-2">
                              <ArrowUpCircle className="h-3 w-3 text-green-500" />
                              <span className="text-xs">{transaction.description}</span>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.category}</td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.account}</td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.date}</td>
                          <td className="px-3 py-2 text-right font-medium text-xs text-green-600">
                            +₺{transaction.amount.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
            
            <TabsContent value="expense" className="space-y-3">
              <div className="relative overflow-x-auto rounded-md border">
                <table className="w-full text-sm">
                  <thead className="bg-muted text-muted-foreground">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs">İşlem</th>
                      <th className="px-3 py-2 text-left text-xs">Kategori</th>
                      <th className="px-3 py-2 text-left text-xs">Hesap</th>
                      <th className="px-3 py-2 text-left text-xs">Tarih</th>
                      <th className="px-3 py-2 text-right text-xs">Tutar</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {recentTransactions
                      .filter(t => t.type === "gider")
                      .map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-muted/50">
                          <td className="px-3 py-2">
                            <div className="flex items-center gap-2">
                              <ArrowDownCircle className="h-3 w-3 text-red-500" />
                              <span className="text-xs">{transaction.description}</span>
                            </div>
                          </td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.category}</td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.account}</td>
                          <td className="px-3 py-2 text-muted-foreground text-xs">{transaction.date}</td>
                          <td className="px-3 py-2 text-right font-medium text-xs text-red-600">
                            -₺{transaction.amount.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

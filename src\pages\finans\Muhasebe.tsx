
import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { AccountingAccounts } from "@/components/billing/AccountingAccounts";
import { AccountingTransactions } from "@/components/billing/AccountingTransactions";

const Muhasebe = () => {
  const tabs: TabItem[] = [
    { id: "accounts", label: "Hesap Planı", component: <AccountingAccounts /> },
    { id: "transactions", label: "Muhasebe İşlemleri", component: <AccountingTransactions /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON><PERSON> Hesapları</h2>
          <p className="text-muted-foreground">
            Muhasebe hesap planı ve hesap hareketleri
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default Muhasebe;

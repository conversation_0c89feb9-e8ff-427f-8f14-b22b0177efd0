
import { Card } from "@/components/ui/card";
import { Users, UserCheck, FileText } from "lucide-react";

export const CustomerStatistics = () => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <Users className="h-8 w-8 text-blue-500" />
          <div>
            <p className="text-sm text-muted-foreground">Toplam Müşteri</p>
            <h3 className="text-2xl font-semibold">1,234</h3>
            <p className="text-sm text-success">+12 bu ay</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <UserCheck className="h-8 w-8 text-green-500" />
          <div>
            <p className="text-sm text-muted-foreground">Toplam Tedarikçi</p>
            <h3 className="text-2xl font-semibold">245</h3>
            <p className="text-sm text-muted-foreground">Aktif</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <FileText className="h-8 w-8 text-purple-500" />
          <div>
            <p className="text-sm text-muted-foreground">Açık Bakiye</p>
            <h3 className="text-2xl font-semibold">₺145,678.90</h3>
            <p className="text-sm text-amber-500">Alacak</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <FileText className="h-8 w-8 text-red-500" />
          <div>
            <p className="text-sm text-muted-foreground">Açık Bakiye</p>
            <h3 className="text-2xl font-semibold">₺78,456.32</h3>
            <p className="text-sm text-destructive">Borç</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

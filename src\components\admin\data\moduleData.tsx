
import { 
  FileText, 
  Package, 
  Users, 
  Building, 
  Factory,
  ShoppingCart,
  TrendingUp,
  Truck,
  Construction,
  Briefcase,
  Wrench,
  HeartPulse,
  Hotel
} from "lucide-react";
import { ModuleInfo } from "../types";

export const basicModules: ModuleInfo[] = [
  { id: "finans", name: "Finans ve Muhasebe", icon: FileText, type: "Temel", color: "text-green-500", bgColor: "bg-green-100" },
  { id: "stok", name: "Stok Yönetimi", icon: Package, type: "Temel", color: "text-amber-500", bgColor: "bg-amber-100" },
  { id: "insan_kaynaklari", name: "<PERSON><PERSON><PERSON>", icon: Users, type: "Te<PERSON>", color: "text-purple-500", bgColor: "bg-purple-100" },
  { id: "musteri", name: "Müşteri ve Tedarikçi", icon: Building, type: "Temel", color: "text-blue-500", bgColor: "bg-blue-100" },
];

export const optionalModules: ModuleInfo[] = [
  { id: "uretim", name: "<PERSON>retim <PERSON>", icon: Factory, type: "Seçimli", color: "text-orange-500", bgColor: "bg-orange-100", 
    features: ["Mühendislik Değişiklik Yönetimi", "Üretim Yürütme Sistemleri", "Gelişmiş Planlama ve Çizelgeleme"] },
  { id: "satis", name: "Satış Paketi", icon: ShoppingCart, type: "Seçimli", color: "text-pink-500", bgColor: "bg-pink-100",
    features: ["Perakende Satış Merkezi", "Toptan Satış Paneli", "E-ticaret Kontrol Merkezi"] },
  { id: "proje", name: "Proje Yönetimi", icon: TrendingUp, type: "Seçimli", color: "text-indigo-500", bgColor: "bg-indigo-100",
    features: ["Genel Proje Dashboard", "İnşaat Projeleri Paneli", "Kaynak Yönetimi"] },
  { id: "tedarik", name: "Tedarik Zinciri", icon: Truck, type: "Seçimli", color: "text-teal-500", bgColor: "bg-teal-100",
    features: ["Ham Madde Tedarik Takibi", "Lojistik Yönetim Ekranı", "Sevkiyat Planlama"] },
  { id: "insaat", name: "İnşaat ve Mühendislik", icon: Construction, type: "Seçimli", color: "text-yellow-500", bgColor: "bg-yellow-100",
    features: ["Proje Maliyet Yönetimi", "Hakediş Yönetimi", "Alt Yüklenici Yönetimi"] },
  { id: "profesyonel", name: "Profesyonel Hizmetler", icon: Briefcase, type: "Seçimli", color: "text-cyan-500", bgColor: "bg-cyan-100",
    features: ["Proje/Görev Yönetimi", "Zaman ve Masraf Yönetimi", "Kaynak Yönetimi ve Planlama"] },
  { id: "servis", name: "Saha Servis Hizmetleri", icon: Wrench, type: "Seçimli", color: "text-red-500", bgColor: "bg-red-100",
    features: ["Servis Yönetimi", "Planlama ve Atama", "Mobil Saha Servis Uygulaması"] },
  { id: "saglik", name: "Sağlık Sektörü", icon: HeartPulse, type: "Seçimli", color: "text-emerald-500", bgColor: "bg-emerald-100",
    features: ["Elektronik Sağlık Kaydı Entegrasyonu", "Hasta Muhasebesi ve Faturalama", "Randevu Yönetimi"] },
  { id: "konaklama", name: "Konaklama (Otelcilik)", icon: Hotel, type: "Seçimli", color: "text-sky-500", bgColor: "bg-sky-100",
    features: ["Ön Büro / Resepsiyon Entegrasyonu", "Etkinlik ve Banket Yönetimi", "Satış Noktası Entegrasyonu"] },
];


import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, Calendar, Users, ClipboardList } from "lucide-react";
import { Progress } from "@/components/ui/progress";

const ProjeYonetimi = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight"><PERSON>je <PERSON></h2>
          <p className="text-muted-foreground">
            Genel proje dashboard, inşaat projeleri paneli ve kaynak yönetimi
          </p>
        </div>
        
        <Tabs defaultValue="dashboard" className="space-y-4">
          <TabsList>
            <TabsTrigger value="dashboard">Genel Dashboard</TabsTrigger>
            <TabsTrigger value="construction">İnşaat Projeleri</TabsTrigger>
            <TabsTrigger value="resources">Kaynak Yönetimi</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Aktif Projeler</CardTitle>
                  <ClipboardList className="h-4 w-4 text-indigo-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Görevler</CardTitle>
                  <Calendar className="h-4 w-4 text-indigo-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">48</div>
                  <p className="text-xs text-muted-foreground">18 gecikmiş görev</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Proje Ekibi</CardTitle>
                  <Users className="h-4 w-4 text-indigo-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Bütçe Kullanımı</CardTitle>
                  <TrendingUp className="h-4 w-4 text-indigo-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">68%</div>
                  <Progress value={68} className="h-2" />
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Proje Durumu</CardTitle>
                <CardDescription>
                  Tüm projelerinizin genel durum ve ilerleme bilgileri
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {["Yeni Şube Açılışı", "Web Sitesi Yenileme", "ERP Sistem Entegrasyonu", "Mobil Uygulama Geliştirme"].map((project, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{project}</p>
                        <p className="text-xs text-muted-foreground">Son Güncelleme: Bugün</p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="w-32">
                          <Progress value={[75, 45, 90, 30][index]} className="h-2" />
                        </div>
                        <div className="font-medium">{[75, 45, 90, 30][index]}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="construction" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>İnşaat Projeleri Paneli</CardTitle>
                <CardDescription>
                  İnşaat projelerinize özel yönetim ekranı
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">İnşaat projeleri verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="resources" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Kaynak Yönetimi</CardTitle>
                <CardDescription>
                  Proje kaynaklarınızı etkin şekilde yönetin ve atayın
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">Kaynak yönetimi araçları burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default ProjeYonetimi;


import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";

interface Company {
  id: number;
  name: string;
  code: string;
  active: boolean;
  modules: string[];
  optionalModules: string[];
  type: string;
}

interface ModuleInfo {
  id: string;
  name: string;
  icon: any;
  type: string;
  color: string;
  bgColor: string;
  features?: string[];
}

interface ModulesTabProps {
  companies: Company[];
  setCompanies: (companies: Company[]) => void;
  basicModules: ModuleInfo[];
  optionalModules: ModuleInfo[];
}

export const ModulesTab = ({ companies, setCompanies, basicModules, optionalModules }: ModulesTabProps) => {
  const { toast } = useToast();

  const toggleModule = (companyId: number, moduleId: string, isOptional: boolean) => {
    setCompanies(companies.map(company => {
      if (company.id === companyId) {
        if (isOptional) {
          const updatedModules = company.optionalModules.includes(moduleId)
            ? company.optionalModules.filter(m => m !== moduleId)
            : [...company.optionalModules, moduleId];
          
          return {...company, optionalModules: updatedModules};
        } else {
          const updatedModules = company.modules.includes(moduleId)
            ? company.modules.filter(m => m !== moduleId)
            : [...company.modules, moduleId];
          
          return {...company, modules: updatedModules};
        }
      }
      return company;
    }));
    
    toast({
      title: "Modül ayarları güncellendi",
      description: `Modül başarıyla ${isOptional ? "opsiyonel modüllere" : "temel modüllere"} ${companies.find(c => c.id === companyId)?.optionalModules.includes(moduleId) ? "çıkarıldı" : "eklendi"}`,
      variant: "default",
    });
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Modül Yönetimi</h3>
        <div className="flex gap-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700">Temel</Badge>
          <Badge variant="outline" className="bg-amber-50 text-amber-700">Seçimli</Badge>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {companies.map(company => (
          <Card key={company.id} className={!company.active ? "opacity-60" : ""}>
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <CardTitle>{company.name}</CardTitle>
                <Badge variant={company.active ? "default" : "outline"} className={company.active ? "bg-green-500" : ""}>
                  {company.active ? "Aktif" : "Pasif"}
                </Badge>
              </div>
              <CardDescription className="flex items-center gap-2">
                {company.type}
                <Badge variant="outline" className="font-mono">
                  Kurum Kodu: {company.code}
                </Badge>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2 text-sm text-muted-foreground">Temel Modüller</h4>
                  <div className="space-y-2">
                    {basicModules.map(module => (
                      <div key={module.id} className="flex items-center justify-between p-2 rounded-md border">
                        <div className="flex items-center gap-2">
                          <div className={`p-2 rounded-md ${module.bgColor}`}>
                            <module.icon className={`h-5 w-5 ${module.color}`} />
                          </div>
                          <div>
                            <div className="font-medium">{module.name}</div>
                            <div className="text-xs text-muted-foreground">
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                {module.type}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <Switch 
                          checked={company.modules.includes(module.id)}
                          onCheckedChange={() => toggleModule(company.id, module.id, false)}
                          disabled={!company.active}
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2 text-sm text-muted-foreground">Seçimli Modüller</h4>
                  <div className="space-y-2">
                    {optionalModules.map(module => (
                      <div key={module.id} className="flex items-center justify-between p-2 rounded-md border">
                        <div className="flex items-center gap-2">
                          <div className={`p-2 rounded-md ${module.bgColor}`}>
                            <module.icon className={`h-5 w-5 ${module.color}`} />
                          </div>
                          <div>
                            <div className="font-medium">{module.name}</div>
                            <div className="text-xs text-muted-foreground">
                              <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700">
                                {module.type}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <Switch 
                          checked={company.optionalModules.includes(module.id)}
                          onCheckedChange={() => toggleModule(company.id, module.id, true)}
                          disabled={!company.active}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
};


import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { FinancialReports } from "@/components/billing/FinancialReports";
import { ReportingAndAnalysis } from "@/components/billing/ReportingAndAnalysis";
import { FinancialDashboard } from "@/components/billing/FinancialDashboard";

const FinansRaporlar = () => {
  const tabs: TabItem[] = [
    { id: "dashboard", label: "Dashboard", component: <FinancialDashboard /> },
    { id: "reports", label: "Finansal Raporlar", component: <FinancialReports /> },
    { id: "analysis", label: "Raporlama ve Analiz", component: <ReportingAndAnalysis /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Finans Raporları</h2>
          <p className="text-muted-foreground">
            Finansal raporlar, analizler ve gösterge paneli
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default FinansRaporlar;

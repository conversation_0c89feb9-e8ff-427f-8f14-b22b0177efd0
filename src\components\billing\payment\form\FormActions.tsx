
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CardFooter } from "@/components/ui/card";

type FormActionsProps = {
  onCancel: () => void;
  loading: boolean;
};

export const FormActions = ({ onCancel, loading }: FormActionsProps) => {
  return (
    <CardFooter className="flex justify-end gap-2 px-0 py-2">
      <Button 
        variant="outline" 
        type="button"
        onClick={onCancel}
        size="sm"
        className="h-9 text-xs"
      >
        İptal
      </Button>
      <Button 
        type="submit" 
        disabled={loading} 
        size="sm" 
        className="h-9 text-xs"
      >
        {loading ? "Kaydediliyor..." : "Kaydet"}
      </Button>
    </CardFooter>
  );
};

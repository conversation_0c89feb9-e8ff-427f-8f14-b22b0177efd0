
import { useReportsStore } from "@/stores/reportsStore";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart, Table, 
  Download, Calendar, Trash2, Eye 
} from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";

export const SavedReportsList = () => {
  const { savedReports, deleteReport } = useReportsStore();
  const { toast } = useToast();

  // Get icon based on chart type
  const getChartIcon = (chartType: string) => {
    switch (chartType) {
      case 'bar':
        return <BarChart className="h-4 w-4" />;
      case 'line':
        return <LineChart className="h-4 w-4" />;
      case 'pie':
        return <PieChart className="h-4 w-4" />;
      case 'table':
        return <Table className="h-4 w-4" />;
      default:
        return <LineChart className="h-4 w-4" />;
    }
  };

  const handleViewReport = (reportId: string) => {
    toast({
      title: "Rapor Görüntüleniyor",
      description: "Rapor detayları yükleniyor...",
    });
  };

  const handleDownloadReport = (reportId: string) => {
    toast({
      title: "Rapor İndiriliyor",
      description: "Rapor başarıyla indirildi.",
    });
  };

  const handleDeleteReport = (reportId: string) => {
    deleteReport(reportId);
    toast({
      title: "Rapor Silindi",
      description: "Rapor başarıyla silindi.",
    });
  };

  if (savedReports.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Henüz kaydedilmiş rapor bulunmuyor.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {savedReports.map((report) => (
        <Card key={report.id}>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                {getChartIcon(report.chartType)}
                {report.name}
              </CardTitle>
              <div className="text-sm text-muted-foreground">
                {format(new Date(report.createdAt), 'PPP', { locale: tr })}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">{report.description}</p>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => handleViewReport(report.id)}
                >
                  <Eye className="h-3.5 w-3.5" />
                  Görüntüle
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => handleDownloadReport(report.id)}
                >
                  <Download className="h-3.5 w-3.5" />
                  İndir
                </Button>
              </div>
              <div className="flex space-x-2">
                {report.schedule.enabled ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <Calendar className="h-3.5 w-3.5" />
                    Zamanlanmış
                  </Button>
                ) : null}
                <Button 
                  variant="outline" 
                  size="sm"
                  className="flex items-center gap-1 text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteReport(report.id)}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                  Sil
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

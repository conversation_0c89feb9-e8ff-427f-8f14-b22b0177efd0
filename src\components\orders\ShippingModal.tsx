
import { useState, useEffect } from "react";
import { 
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useOrderStore, ShippingCarrier, ShippingMethod } from "@/stores/orderStore";
import { toast } from "sonner";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Truck, Package, Calendar, Map } from "lucide-react";

interface ShippingModalProps {
  orderId: string;
  isOpen: boolean;
  onClose: () => void;
}

export const ShippingModal = ({ orderId, isOpen, onClose }: ShippingModalProps) => {
  const { orders, addShippingInfo, updateShippingStatus } = useOrderStore();
  const order = orders.find(o => o.id === orderId);
  
  const [carrier, setCarrier] = useState<ShippingCarrier>(order?.shipping?.carrier || 'aras');
  const [trackingNumber, setTrackingNumber] = useState(order?.shipping?.trackingNumber || '');
  const [method, setMethod] = useState<ShippingMethod>(order?.shipping?.method || 'standard');
  const [status, setStatus] = useState<'pending' | 'in_transit' | 'delivered' | 'failed'>(
    order?.shipping?.status || 'pending'
  );
  const [estimatedDelivery, setEstimatedDelivery] = useState(
    order?.shipping?.estimatedDelivery 
      ? new Date(order.shipping.estimatedDelivery).toISOString().split('T')[0]
      : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  );
  
  // Reset form when order changes
  useEffect(() => {
    if (order) {
      setCarrier(order.shipping?.carrier || 'aras');
      setTrackingNumber(order.shipping?.trackingNumber || '');
      setMethod(order.shipping?.method || 'standard');
      
      // Ensure status is one of the allowed values
      if (order.shipping?.status && ['pending', 'in_transit', 'delivered', 'failed'].includes(order.shipping.status)) {
        setStatus(order.shipping.status as 'pending' | 'in_transit' | 'delivered' | 'failed');
      } else {
        setStatus('pending');
      }
      
      setEstimatedDelivery(
        order.shipping?.estimatedDelivery 
          ? new Date(order.shipping.estimatedDelivery).toISOString().split('T')[0]
          : new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      );
    }
  }, [order]);

  const handleSubmit = () => {
    if (!order) return;
    
    try {
      if (!order.shipping) {
        // Create new shipping info
        addShippingInfo(orderId, {
          carrier,
          trackingNumber,
          method,
          status,
          estimatedDelivery: new Date(estimatedDelivery),
          address: {
            fullName: order.customerName,
            street: "Otomatik oluşturuldu",
            city: "İstanbul",
            state: "",
            zipCode: "",
            country: "Türkiye",
            phone: "",
          },
          cost: method === 'express' ? 50 : method === 'same_day' ? 100 : 25,
        });
      } else {
        // Update existing shipping info
        updateShippingStatus(orderId, status, {
          carrier,
          trackingNumber,
          method,
          estimatedDelivery: new Date(estimatedDelivery),
        });
      }
      
      toast.success("Kargo bilgileri güncellendi");
      onClose();
    } catch (error) {
      toast.error("Kargo bilgilerini güncellerken bir hata oluştu");
      console.error(error);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Kargo Bilgileri</DialogTitle>
          <DialogDescription>
            {order.number} numaralı siparişin kargo detaylarını güncelleyin
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="carrier" className="text-right">
              Kargo Firması
            </Label>
            <div className="col-span-3">
              <Select value={carrier} onValueChange={(value: ShippingCarrier) => setCarrier(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kargo firması seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aras">Aras Kargo</SelectItem>
                  <SelectItem value="yurtici">Yurtiçi Kargo</SelectItem>
                  <SelectItem value="ups">UPS</SelectItem>
                  <SelectItem value="mng">MNG Kargo</SelectItem>
                  <SelectItem value="ptt">PTT Kargo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="tracking-number" className="text-right">
              Takip Numarası
            </Label>
            <div className="relative col-span-3">
              <Truck className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="tracking-number"
                placeholder="Kargo takip numarası"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="method" className="text-right">
              Gönderim Türü
            </Label>
            <div className="col-span-3">
              <Select value={method} onValueChange={(value: ShippingMethod) => setMethod(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Gönderim türü seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standart Kargo</SelectItem>
                  <SelectItem value="express">Hızlı Kargo</SelectItem>
                  <SelectItem value="same_day">Aynı Gün Teslimat</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Kargo Durumu
            </Label>
            <div className="col-span-3">
              <Select 
                value={status} 
                onValueChange={(value: 'pending' | 'in_transit' | 'delivered' | 'failed') => setStatus(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Kargo durumu seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Hazırlanıyor</SelectItem>
                  <SelectItem value="in_transit">Taşınıyor</SelectItem>
                  <SelectItem value="delivered">Teslim Edildi</SelectItem>
                  <SelectItem value="failed">Teslimat Başarısız</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="delivery-date" className="text-right">
              Tahmini Teslimat
            </Label>
            <div className="relative col-span-3">
              <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="delivery-date"
                type="date"
                value={estimatedDelivery}
                onChange={(e) => setEstimatedDelivery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button type="submit" onClick={handleSubmit}>Kaydet</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

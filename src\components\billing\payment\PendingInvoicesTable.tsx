
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText } from "lucide-react";

type PendingInvoice = {
  id: string;
  invoiceNumber: string;
  supplier: string;
  dueDate: string;
  totalAmount: number;
  currency: string;
};

type PendingInvoicesTableProps = {
  invoices: PendingInvoice[];
  selectedInvoices: string[];
  onToggleSelection: (id: string) => void;
  onFreePaymentOrder: () => void;
};

export const PendingInvoicesTable = ({
  invoices,
  selectedInvoices,
  onToggleSelection,
  onFreePaymentOrder
}: PendingInvoicesTableProps) => {
  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium">Bekleyen Faturalar</h3>
        <Button variant="ghost" size="sm" onClick={onFreePaymentOrder} className="text-xs h-8 px-2">
          <FileText className="h-3.5 w-3.5 mr-1" />
          Serbest Ödeme Emri
        </Button>
      </div>
      <div className="border rounded-md overflow-hidden shadow-sm">
        <table className="w-full text-sm">
          <thead className="bg-muted/70">
            <tr>
              <th className="px-2 py-1.5 text-left font-medium text-xs">Seç</th>
              <th className="px-2 py-1.5 text-left font-medium text-xs">Fatura No</th>
              <th className="px-2 py-1.5 text-left font-medium text-xs">Tedarikçi</th>
              <th className="px-2 py-1.5 text-left font-medium text-xs">Vade</th>
              <th className="px-2 py-1.5 text-right font-medium text-xs">Tutar</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-muted/30">
            {invoices.map((inv) => (
              <tr key={inv.id} className="hover:bg-muted/30">
                <td className="px-2 py-1.5">
                  <input 
                    type="checkbox" 
                    checked={selectedInvoices.includes(inv.id)}
                    onChange={() => onToggleSelection(inv.id)}
                    className="h-3.5 w-3.5 rounded border-gray-300"
                  />
                </td>
                <td className="px-2 py-1.5 text-xs">{inv.invoiceNumber}</td>
                <td className="px-2 py-1.5 text-xs">
                  <div className="max-w-[120px] truncate" title={inv.supplier}>{inv.supplier}</div>
                </td>
                <td className="px-2 py-1.5 text-xs">{inv.dueDate}</td>
                <td className="px-2 py-1.5 text-right text-xs font-medium">
                  {inv.currency === "TRY" ? "₺" : inv.currency === "USD" ? "$" : "€"} 
                  {inv.totalAmount.toLocaleString()}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {selectedInvoices.length > 0 && (
        <div className="flex justify-end mt-1">
          <Badge variant="outline" className="text-xs h-5 bg-muted/50">
            {selectedInvoices.length} fatura seçildi
          </Badge>
        </div>
      )}
    </div>
  );
};


import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Loader2, Database, Server, CheckCircle2, XCircle, LayoutList } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ConnectionStatus {
  status: string;
  message: string;
  db_info?: {
    version: string;
    database_name: string;
    user: string;
  };
  tables?: string[];
  connection_info?: {
    host: string;
    database: string;
    user: string;
    port: string;
  };
  timestamp: string;
}

interface QueryStatus {
  status: string;
  data?: {
    cari_hesaplar: number;
    urunler: number;
    faturalar: number;
  };
  message: string;
  timestamp: string;
}

interface DatabaseConnectionTestProps {
  isDevMode?: boolean;
}

export const DatabaseConnectionTest = ({ isDevMode = false }: DatabaseConnectionTestProps) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [queryStatus, setQueryStatus] = useState<QueryStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [queryLoading, setQueryLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);

    // Eğer geliştirme modundaysak (Lovable online editör veya localhost)
    if (isDevMode) {
      // Mock verileri 2 saniye gecikmeyle gösterelim
      setTimeout(() => {
        const mockConnectionData: ConnectionStatus = {
          status: 'connected',
          message: 'Veritabanı bağlantısı başarılı (Demo)',
          db_info: {
            version: 'MySQL 8.0.31',
            database_name: 'finans_db',
            user: 'mehmet@localhost'
          },
          tables: ['cari_hesaplar', 'faturalar', 'urunler', 'odemeler', 'stok_hareketleri', 'kullanicilar'],
          connection_info: {
            host: '*************',
            database: 'finans_db',
            user: 'mehmet',
            port: '3306'
          },
          timestamp: new Date().toISOString()
        };

        setConnectionStatus(mockConnectionData);
        toast.success('Veritabanı bağlantısı başarılı (Demo modu)');
        setLoading(false);
      }, 2000);
      return;
    }

    try {
      const response = await fetch('/api/test/connection');

      if (!response.ok) {
        throw new Error(`API yanıt vermedi: ${response.status}`);
      }

      const data = await response.json();
      setConnectionStatus(data);

      if (data.status === 'connected') {
        toast.success('Veritabanı bağlantısı başarılı');
      } else {
        toast.error('Veritabanı bağlantısı başarısız');
      }
    } catch (error) {
      console.error('Bağlantı test hatası:', error);
      toast.error('Bağlantı testi sırasında bir hata oluştu');
      setConnectionStatus({
        status: 'error',
        message: 'Bağlantı hatası: ' + String(error),
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const testQuery = async () => {
    setQueryLoading(true);

    // Eğer geliştirme modundaysak (Lovable online editör veya localhost)
    if (isDevMode) {
      // Mock verileri 2 saniye gecikmeyle gösterelim
      setTimeout(() => {
        const mockQueryData: QueryStatus = {
          status: 'success',
          data: {
            cari_hesaplar: 145,
            urunler: 217,
            faturalar: 1834
          },
          message: 'Test sorgusu başarıyla çalıştırıldı (Demo)',
          timestamp: new Date().toISOString()
        };

        setQueryStatus(mockQueryData);
        toast.success('Test sorgusu başarıyla çalıştırıldı (Demo modu)');
        setQueryLoading(false);
      }, 1500);
      return;
    }

    try {
      const response = await fetch('/api/test/query');

      if (!response.ok) {
        throw new Error(`API yanıt vermedi: ${response.status}`);
      }

      const data = await response.json();
      setQueryStatus(data);

      if (data.status === 'success') {
        toast.success('Test sorgusu başarıyla çalıştırıldı');
      } else {
        toast.error('Test sorgusu başarısız oldu');
      }
    } catch (error) {
      console.error('Sorgu test hatası:', error);
      toast.error('Sorgu testi sırasında bir hata oluştu');
      setQueryStatus({
        status: 'error',
        message: 'Sorgu hatası: ' + String(error),
        timestamp: new Date().toISOString()
      });
    } finally {
      setQueryLoading(false);
    }
  };

  useEffect(() => {
    // İlk yüklemede bağlantıyı test et
    testConnection();
  }, [isDevMode]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Veritabanı Bağlantı Testi</CardTitle>
              <CardDescription>
                {isDevMode ?
                  "Demo modunda MySQL veritabanı bağlantısı" :
                  "MySQL veritabanı bağlantı durumunu kontrol edin"}
              </CardDescription>
            </div>
            <Badge
              variant={connectionStatus?.status === 'connected' ? 'success' : 'destructive'}
              className="px-2 py-1 text-sm"
            >
              {connectionStatus?.status === 'connected' ? 'Bağlı' : 'Bağlantı Kesildi'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {connectionStatus ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Server className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Sunucu Bilgisi:</span>
                {connectionStatus.db_info ? (
                  <span>{connectionStatus.db_info.version}</span>
                ) : (
                  <span className="text-muted-foreground">Bilgi alınamadı</span>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Database className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Veritabanı:</span>
                {connectionStatus.db_info ? (
                  <span>{connectionStatus.db_info.database_name}</span>
                ) : (
                  <span className="text-muted-foreground">Bilgi alınamadı</span>
                )}
              </div>

              {connectionStatus.connection_info && (
                <div className="grid grid-cols-2 gap-2 text-sm p-3 bg-muted rounded-md">
                  <div>Host: <span className="font-medium">{connectionStatus.connection_info.host}</span></div>
                  <div>Port: <span className="font-medium">{connectionStatus.connection_info.port}</span></div>
                  <div>Kullanıcı: <span className="font-medium">{connectionStatus.connection_info.user}</span></div>
                  <div>Veritabanı: <span className="font-medium">{connectionStatus.connection_info.database}</span></div>
                </div>
              )}

              {connectionStatus.tables && connectionStatus.tables.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <LayoutList className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Tablolar ({connectionStatus.tables.length}):</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    {connectionStatus.tables.map((table, index) => (
                      <Badge key={index} variant="outline" className="justify-start">
                        {table}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <div className="flex justify-center py-8 text-muted-foreground">
              Bağlantı durumu bilgisi yok
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <span className="text-xs text-muted-foreground">
            Son kontrol: {connectionStatus?.timestamp ? new Date(connectionStatus.timestamp).toLocaleString('tr-TR') : 'Hiç'}
            {isDevMode && <span className="ml-1">(Demo)</span>}
          </span>
          <Button
            onClick={testConnection}
            disabled={loading}
            size="sm"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Bağlantıyı Test Et
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Sorgu Testi</CardTitle>
              <CardDescription>
                {isDevMode ?
                  "Demo modunda veritabanı sorguları" :
                  "Veritabanı üzerinde test sorguları çalıştırın"}
              </CardDescription>
            </div>
            {queryStatus && (
              <Badge
                variant={queryStatus.status === 'success' ? 'success' : 'destructive'}
                className="px-2 py-1 text-sm"
              >
                {queryStatus.status === 'success' ? 'Başarılı' : 'Başarısız'}
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {queryStatus?.data ? (
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold">{queryStatus.data.cari_hesaplar}</div>
                  <div className="text-sm text-muted-foreground">Cari Hesap</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold">{queryStatus.data.urunler}</div>
                  <div className="text-sm text-muted-foreground">Ürün</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold">{queryStatus.data.faturalar}</div>
                  <div className="text-sm text-muted-foreground">Fatura</div>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">Durum:</span>
                {queryStatus.status === 'success' ? (
                  <span className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Sorgu başarılı {isDevMode && "(Demo)"}
                  </span>
                ) : (
                  <span className="flex items-center text-red-600">
                    <XCircle className="h-4 w-4 mr-1" />
                    Sorgu başarısız
                  </span>
                )}
              </div>
            </div>
          ) : queryLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <div className="flex justify-center py-8 text-muted-foreground">
              Henüz sorgu çalıştırılmadı
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <span className="text-xs text-muted-foreground">
            Son sorgu: {queryStatus?.timestamp ? new Date(queryStatus.timestamp).toLocaleString('tr-TR') : 'Hiç'}
            {isDevMode && queryStatus && <span className="ml-1">(Demo)</span>}
          </span>
          <Button
            onClick={testQuery}
            disabled={queryLoading || connectionStatus?.status !== 'connected'}
            size="sm"
          >
            {queryLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sorgu Çalıştır
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

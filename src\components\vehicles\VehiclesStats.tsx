
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useVehicleStore } from "@/stores/vehicleStore";
import { Car, Truck, UserCheck, Fuel } from "lucide-react";

export const VehiclesStats = () => {
  const vehicles = useVehicleStore((state) => state.vehicles);
  const drivers = useVehicleStore((state) => state.drivers);
  const fuelRecords = useVehicleStore((state) => state.fuelRecords);

  const activeVehicles = vehicles.filter(v => v.status === 'active').length;
  const totalDrivers = drivers.length;
  const activeDrivers = drivers.filter(d => d.status === 'active').length;
  const totalFuelCost = fuelRecords.reduce((acc, record) => acc + record.totalCost, 0);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Aktif <PERSON></CardTitle>
          <Car className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeVehicles} <span className="text-sm text-muted-foreground">/ {vehicles.length}</span></div>
          <p className="text-xs text-muted-foreground mt-1">Toplam araç filosu</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Aktif Sürücüler</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeDrivers} <span className="text-sm text-muted-foreground">/ {totalDrivers}</span></div>
          <p className="text-xs text-muted-foreground mt-1">Toplam sürücü sayısı</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Araç Filosu</CardTitle>
          <Truck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{vehicles.length}</div>
          <p className="text-xs text-muted-foreground mt-1">Toplam araç sayısı</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Yakıt Maliyeti</CardTitle>
          <Fuel className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₺{totalFuelCost.toLocaleString('tr-TR', {maximumFractionDigits: 2})}</div>
          <p className="text-xs text-muted-foreground mt-1">Toplam yakıt harcaması</p>
        </CardContent>
      </Card>
    </div>
  );
};

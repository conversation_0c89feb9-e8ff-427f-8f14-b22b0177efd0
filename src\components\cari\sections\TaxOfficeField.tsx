
import { FileText, AlertCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface TaxOfficeFieldProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export const TaxOfficeField = ({ value, onChange, error }: TaxOfficeFieldProps) => {
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-1">
        <FileText className="h-4 w-4 text-primary" />
        <Label htmlFor="vergiDairesi" className="font-medium text-sm">
          Vergi Dairesi <span className="text-red-500">*</span>
        </Label>
      </div>
      <Input 
        id="vergiDairesi" 
        placeholder="Vergi dairesi" 
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
      />
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
};


import MainLayout from "@/components/layout/MainLayout";
import { TabsNavigation, TabItem } from "@/components/layout/TabsNavigation";
import { ReceivablesManagement } from "@/components/billing/ReceivablesManagement";
import { ExpenseAndPayment } from "@/components/billing/ExpenseAndPayment";

const AlacakBorc = () => {
  const tabs: TabItem[] = [
    { id: "receivables", label: "Alacak Yönetimi", component: <ReceivablesManagement /> },
    { id: "expenses", label: "Gider ve Ödeme", component: <ExpenseAndPayment /> }
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Alacak ve Borç Yönetimi</h2>
          <p className="text-muted-foreground">
            Müşteri alacakları ve tedarikçi borçlarını yönetin
          </p>
        </div>
        
        <TabsNavigation items={tabs} />
      </div>
    </MainLayout>
  );
};

export default AlacakBorc;

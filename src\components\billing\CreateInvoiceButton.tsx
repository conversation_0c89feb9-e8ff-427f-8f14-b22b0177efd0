
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, FileText, FileCheck, FileSpreadsheet } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomInvoiceTemplates } from "./CustomInvoiceTemplates";
import { FreelanceReceipt } from "./FreelanceReceipt";

export const CreateInvoiceButton = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("invoice");
  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Yeni Fatura
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => {
            setActiveTab("invoice");
            setDialogOpen(true);
          }}>
            <FileText className="mr-2 h-4 w-4" />
            Standart Fatura
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {
            setActiveTab("e-invoice");
            setDialogOpen(true);
          }}>
            <FileCheck className="mr-2 h-4 w-4" />
            e-Fatura
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {
            setActiveTab("e-archive");
            setDialogOpen(true);
          }}>
            <FileText className="mr-2 h-4 w-4" />
            e-Arşiv Fatura
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => {
            setActiveTab("freelance");
            setDialogOpen(true);
          }}>
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            Serbest Meslek Makbuzu
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-5xl">
          <DialogHeader>
            <DialogTitle>
              {activeTab === "invoice" && "Yeni Fatura Oluştur"}
              {activeTab === "e-invoice" && "Yeni e-Fatura Oluştur"}
              {activeTab === "e-archive" && "Yeni e-Arşiv Fatura Oluştur"}
              {activeTab === "freelance" && "Serbest Meslek Makbuzu Oluştur"}
            </DialogTitle>
          </DialogHeader>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="invoice">Standart Fatura</TabsTrigger>
              <TabsTrigger value="e-invoice">e-Fatura</TabsTrigger>
              <TabsTrigger value="e-archive">e-Arşiv Fatura</TabsTrigger>
              <TabsTrigger value="freelance">SM Makbuzu</TabsTrigger>
            </TabsList>
            
            <TabsContent value="invoice">
              <CustomInvoiceTemplates />
            </TabsContent>
            
            <TabsContent value="e-invoice">
              <div className="p-6 text-center">
                <FileCheck className="mx-auto h-12 w-12 text-primary mb-4" />
                <h3 className="text-xl font-medium mb-2">e-Fatura</h3>
                <p className="text-muted-foreground mb-4">
                  e-Fatura, Gelir İdaresi Başkanlığı sistemine entegre elektronik fatura oluşturma özelliğidir.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm text-left mb-4">
                  <p className="font-medium mb-1">e-Fatura Kullanımı İçin:</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>GİB Portal entegrasyonu yapılmalıdır</li>
                    <li>e-Fatura mükellef listesi güncel olmalıdır</li>
                    <li>Mali mühür veya e-imza sertifikanız hazır olmalıdır</li>
                  </ol>
                </div>
                <Button>e-Fatura Entegrasyonu Başlat</Button>
              </div>
            </TabsContent>
            
            <TabsContent value="e-archive">
              <div className="p-6 text-center">
                <FileText className="mx-auto h-12 w-12 text-primary mb-4" />
                <h3 className="text-xl font-medium mb-2">e-Arşiv Fatura</h3>
                <p className="text-muted-foreground mb-4">
                  e-Arşiv Fatura, e-Fatura mükellefi olmayan firma ve kişilere elektronik ortamda fatura gönderme özelliğidir.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm text-left mb-4">
                  <p className="font-medium mb-1">e-Arşiv Fatura Kullanımı İçin:</p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>GİB Portal entegrasyonu yapılmalıdır</li>
                    <li>Mali mühür veya e-imza sertifikanız hazır olmalıdır</li>
                    <li>e-Arşiv için gerekli beyanname ayarları yapılmalıdır</li>
                  </ol>
                </div>
                <Button>e-Arşiv Entegrasyonu Başlat</Button>
              </div>
            </TabsContent>
            
            <TabsContent value="freelance">
              <FreelanceReceipt />
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
};

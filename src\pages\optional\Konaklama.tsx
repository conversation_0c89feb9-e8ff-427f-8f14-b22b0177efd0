
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Hotel, Calendar, ShoppingCart, Users } from "lucide-react";

const Konaklama = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Konaklama (Otelcilik)</h2>
          <p className="text-muted-foreground">
            Ön büro/resepsiyon entegrasyonu, etkinlik ve banket yönetimi, satış noktası entegrasyonu
          </p>
        </div>
        
        <Tabs defaultValue="reception" className="space-y-4">
          <TabsList>
            <TabsTrigger value="reception"><PERSON><PERSON></TabsTrigger>
            <TabsTrigger value="events">Etkinlik/Banket</TabsTrigger>
            <TabsTrigger value="pos">Satış Noktası</TabsTrigger>
          </TabsList>
          
          <TabsContent value="reception" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Ön Büro / Resepsiyon Entegrasyonu</CardTitle>
                <CardDescription>
                  Rezervasyon, check-in/check-out ve oda durumu yönetimi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Hotel className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Ön büro yönetimi araçları burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="events" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Etkinlik ve Banket Yönetimi</CardTitle>
                <CardDescription>
                  Salon rezervasyonları ve etkinlik planlaması
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Calendar className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Etkinlik ve banket yönetimi araçları burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="pos" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Satış Noktası Entegrasyonu</CardTitle>
                <CardDescription>
                  Restoran, bar ve diğer harcama noktaları entegrasyonu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <ShoppingCart className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Satış noktası entegrasyonu burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Konaklama;


import { useState } from "react";
import { Card } from "@/components/ui/card";
import { DataTable } from "@/components/billing/DataTable";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash, Plus, Search, FileText, RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CariHesap } from "@/types/cari";
import { cariService } from "@/services/cariService";
import { toast } from "sonner";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";

interface CariListViewProps {
  cariHesaplar: CariHesap[];
  onAddNewClick: () => void;
  onRefresh: () => Promise<void>;
}

export const CariListView = ({ cariHesaplar, onAddNewClick, onRefresh }: CariListViewProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterTip, setFilterTip] = useState("tumu");
  const [refreshing, setRefreshing] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCariId, setSelectedCariId] = useState<string | null>(null);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await onRefresh();
      toast.success("Cari hesap listesi yenilendi");
    } catch (error) {
      console.error("Yenileme hatası:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedCariId) return;
    
    try {
      await cariService.delete(selectedCariId);
      toast.success("Cari hesap başarıyla silindi");
      onRefresh();
    } catch (error) {
      console.error("Silme hatası:", error);
      toast.error("Cari hesap silinirken bir hata oluştu");
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedCariId(null);
    }
  };

  const confirmDelete = (id: string) => {
    setSelectedCariId(id);
    setIsDeleteDialogOpen(true);
  };

  const filteredCariHesaplar = cariHesaplar.filter(cari => {
    const matchesSearch = cari.unvan?.toLowerCase().includes(searchTerm.toLowerCase()) || 
                        cari.kod?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        cari.vknTckn?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterTip === "tumu" || 
                        (filterTip === "alici" && cari.tip?.includes("Alıcı")) ||
                        (filterTip === "satici" && cari.tip?.includes("Satıcı"));
    
    return matchesSearch && matchesFilter;
  });

  const columns: ColumnDef<CariHesap>[] = [
    {
      accessorKey: "kod",
      header: "Cari Kodu",
    },
    {
      accessorKey: "unvan",
      header: "Firma/Şahıs Adı",
    },
    {
      accessorKey: "vknTckn",
      header: "VKN/TCKN",
    },
    {
      accessorKey: "telefon",
      header: "Telefon",
    },
    {
      accessorKey: "bakiye",
      header: "Güncel Bakiye",
      cell: ({ row }) => {
        const bakiye = parseFloat(String(row.original.bakiye || 0));
        return (
          <span className={bakiye >= 0 ? "text-green-600" : "text-red-600"}>
            {bakiye.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
          </span>
        );
      }
    },
    {
      id: "actions",
      header: "İşlemler",
      cell: ({ row }) => {
        const cari = row.original;
        return (
          <div className="flex gap-2">
            <Button variant="ghost" size="icon" title="Detay Görüntüle">
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Düzenle">
              <Edit className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              title="Sil"
              onClick={() => confirmDelete(cari.id)}
            >
              <Trash className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" title="Ekstre">
              <FileText className="h-4 w-4" />
            </Button>
          </div>
        );
      }
    },
  ];

  return (
    <>
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
          <div className="flex gap-2 items-center">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari adı veya kodu ara..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={filterTip} onValueChange={setFilterTip}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Tüm Hesaplar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tumu">Tüm Hesaplar</SelectItem>
                <SelectItem value="alici">Alıcılar</SelectItem>
                <SelectItem value="satici">Satıcılar</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={handleRefresh} 
              disabled={refreshing}
              title="Listeyi Yenile"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          <Button className="flex items-center gap-1" onClick={onAddNewClick}>
            <Plus className="h-4 w-4" />
            Yeni Cari Hesap
          </Button>
        </div>

        {filteredCariHesaplar.length > 0 ? (
          <DataTable columns={columns} data={filteredCariHesaplar} />
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            {searchTerm || filterTip !== "tumu" ? 
              "Arama kriterlerine uygun cari hesap bulunamadı." : 
              "Henüz cari hesap bulunmuyor. Yeni bir cari hesap ekleyin."}
          </div>
        )}
      </Card>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cari hesabı silmek istediğinize emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu cari hesaba bağlı faturalar ve diğer kayıtlar etkilenmeyecektir,
              ancak ilişkili raporlarda sorunlar oluşabilir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};


import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Map, AlertCircle, Phone, Mail } from "lucide-react";

interface CariAddressSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

export const CariAddressSection = ({ formData, updateFormData, errors }: CariAddressSectionProps) => {
  return (
    <div className="space-y-2 bg-white p-3 rounded-lg border shadow-sm">
      <h4 className="text-md font-semibold text-primary border-b pb-1">Adres Bilgileri</h4>
      
      <div className="space-y-2">
        <div className="space-y-1">
          <div className="flex items-center gap-1">
            <Map className="h-4 w-4 text-primary" />
            <Label htmlFor="adres" className="font-medium text-sm">
              Adres <span className="text-red-500">*</span>
            </Label>
          </div>
          <Textarea 
            id="adres" 
            placeholder="Fatura adresi" 
            rows={2}
            value={formData.adres || ""}
            onChange={(e) => updateFormData("adres", e.target.value)}
            className="border-primary/20 focus-visible:ring-primary text-sm min-h-[60px]"
          />
          {errors.adres && (
            <p className="text-xs text-red-500 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {errors.adres}
            </p>
          )}
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label htmlFor="ilce" className="font-medium text-sm">İlçe</Label>
            <Input 
              id="ilce" 
              placeholder="İlçe" 
              value={formData.ilce || ""}
              onChange={(e) => updateFormData("ilce", e.target.value)}
              className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
            />
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="il" className="font-medium text-sm">İl</Label>
            <Input 
              id="il" 
              placeholder="İl" 
              value={formData.il || ""}
              onChange={(e) => updateFormData("il", e.target.value)}
              className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
            />
          </div>
        </div>
        
        <div className="space-y-1">
          <Label htmlFor="postaKodu" className="font-medium text-sm">Posta Kodu</Label>
          <Input 
            id="postaKodu" 
            placeholder="Posta Kodu" 
            value={formData.postaKodu || ""}
            onChange={(e) => updateFormData("postaKodu", e.target.value)}
            className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
          />
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Phone className="h-4 w-4 text-primary" />
              <Label htmlFor="telefon" className="font-medium text-sm">Telefon</Label>
            </div>
            <Input 
              id="telefon" 
              placeholder="Telefon numarası" 
              value={formData.telefon || ""}
              onChange={(e) => updateFormData("telefon", e.target.value)}
              className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
            />
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Mail className="h-4 w-4 text-primary" />
              <Label htmlFor="email" className="font-medium text-sm">E-posta</Label>
            </div>
            <Input 
              id="email" 
              type="email" 
              placeholder="E-posta adresi" 
              value={formData.email || ""}
              onChange={(e) => updateFormData("email", e.target.value)}
              className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
            />
            {errors.email && (
              <p className="text-xs text-red-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.email}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Package, PackagePlus, PackageMinus, AlertTriangle, Search } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import UrunDonusum from "@/components/production/uretim-verileri/UrunDonusum";

// Demo products data
const products = [
  { id: 1, code: "PRD001", name: "Dizüstü Bilgisayar X5", category: "Bilgisayarlar", stockQuantity: 8, minStockLevel: 10, purchasePrice: 12500, salePrice: 15800, vatRate: 18 },
  { id: 2, code: "PRD002", name: "Ofis Koltuk Pro", category: "Mobilya", stockQuantity: 12, minStockLevel: 5, purchasePrice: 2200, salePrice: 3500, vatRate: 18 },
  { id: 3, code: "PRD003", name: "27\" LED Monitör", category: "Bilgisayar Ekipmanları", stockQuantity: 3, minStockLevel: 5, purchasePrice: 4500, salePrice: 5900, vatRate: 18 },
  { id: 4, code: "PRD004", name: "A4 Kağıt (500 sayfa)", category: "Kırtasiye", stockQuantity: 25, minStockLevel: 30, purchasePrice: 120, salePrice: 180, vatRate: 18 },
  { id: 5, code: "PRD005", name: "Akıllı Telefon Y12", category: "Telefonlar", stockQuantity: 2, minStockLevel: 8, purchasePrice: 9800, salePrice: 12500, vatRate: 18 },
];

// Get critical stock items
const criticalStockItems = products.filter(product => product.stockQuantity < product.minStockLevel);

export const StockManagement = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Package className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">Toplam Ürün</p>
              <h3 className="text-2xl font-semibold">{products.length}</h3>
              <p className="text-sm text-muted-foreground">Kayıtlı ürün sayısı</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <div>
              <p className="text-sm text-muted-foreground">Kritik Stok</p>
              <h3 className="text-2xl font-semibold">{criticalStockItems.length}</h3>
              <p className="text-sm text-danger">Stok eşiğinin altında</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <PackagePlus className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Stok Girişi</p>
              <h3 className="text-2xl font-semibold">18</h3>
              <p className="text-sm text-muted-foreground">Bu ay</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <PackageMinus className="h-8 w-8 text-orange-500" />
            <div>
              <p className="text-sm text-muted-foreground">Stok Çıkışı</p>
              <h3 className="text-2xl font-semibold">32</h3>
              <p className="text-sm text-muted-foreground">Bu ay</p>
            </div>
          </div>
        </Card>
      </div>

      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-red-800 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
            Kritik Stok Uyarıları
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative overflow-x-auto rounded-md border border-red-200 bg-white">
            <table className="w-full text-sm">
              <thead className="bg-red-100 text-red-900">
                <tr>
                  <th className="px-4 py-3 text-left">Ürün Kodu</th>
                  <th className="px-4 py-3 text-left">Ürün Adı</th>
                  <th className="px-4 py-3 text-center">Mevcut Stok</th>
                  <th className="px-4 py-3 text-center">Minimum Stok</th>
                  <th className="px-4 py-3 text-center">Sipariş Miktarı</th>
                  <th className="px-4 py-3 text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-red-100">
                {criticalStockItems.map(product => (
                  <tr key={product.id} className="hover:bg-red-50">
                    <td className="px-4 py-3 font-medium">{product.code}</td>
                    <td className="px-4 py-3">{product.name}</td>
                    <td className="px-4 py-3 text-center font-bold text-red-600">{product.stockQuantity}</td>
                    <td className="px-4 py-3 text-center">{product.minStockLevel}</td>
                    <td className="px-4 py-3 text-center">{product.minStockLevel - product.stockQuantity}</td>
                    <td className="px-4 py-3 text-right">
                      <Button size="sm" variant="outline" className="border-red-200 text-red-700 hover:bg-red-100 hover:text-red-800">
                        Sipariş Oluştur
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Stok Yönetimi</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="products">
            <TabsList className="mb-4">
              <TabsTrigger value="products">Ürünler</TabsTrigger>
              <TabsTrigger value="stock-movements">Stok Hareketleri</TabsTrigger>
              <TabsTrigger value="categories">Kategoriler</TabsTrigger>
              <TabsTrigger value="donusum">Dönüşüm</TabsTrigger>
            </TabsList>

            <TabsContent value="products">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2 w-72 relative">
                  <Search className="h-4 w-4 absolute left-3 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Ürün ara..."
                    className="pl-9 pr-4 py-2 w-full border rounded-md"
                  />
                </div>
                <Button className="flex items-center gap-2">
                  <PackagePlus className="h-4 w-4" />
                  Yeni Ürün Ekle
                </Button>
              </div>

              <div className="relative overflow-x-auto rounded-md border">
                <table className="w-full text-sm">
                  <thead className="bg-muted text-muted-foreground">
                    <tr>
                      <th className="px-4 py-3 text-left">Ürün Kodu</th>
                      <th className="px-4 py-3 text-left">Ürün Adı</th>
                      <th className="px-4 py-3 text-left">Kategori</th>
                      <th className="px-4 py-3 text-center">Stok Adedi</th>
                      <th className="px-4 py-3 text-right">Alış Fiyatı</th>
                      <th className="px-4 py-3 text-right">Satış Fiyatı</th>
                      <th className="px-4 py-3 text-right">KDV Oranı</th>
                      <th className="px-4 py-3 text-center">İşlemler</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {products.map(product => (
                      <tr key={product.id} className="hover:bg-muted/50">
                        <td className="px-4 py-3 font-medium">{product.code}</td>
                        <td className="px-4 py-3">{product.name}</td>
                        <td className="px-4 py-3">{product.category}</td>
                        <td className={`px-4 py-3 text-center font-medium ${product.stockQuantity < product.minStockLevel ? 'text-red-600' : ''
                          }`}>
                          {product.stockQuantity}
                          {product.stockQuantity < product.minStockLevel && (
                            <AlertTriangle className="h-4 w-4 inline ml-1 text-red-500" />
                          )}
                        </td>
                        <td className="px-4 py-3 text-right">₺{product.purchasePrice.toLocaleString()}</td>
                        <td className="px-4 py-3 text-right">₺{product.salePrice.toLocaleString()}</td>
                        <td className="px-4 py-3 text-right">%{product.vatRate}</td>
                        <td className="px-4 py-3 text-center">
                          <Button variant="ghost" size="sm">Düzenle</Button>
                          <Button variant="ghost" size="sm">Stok Ekle</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>

            <TabsContent value="stock-movements">
              <div className="p-4 bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Stok hareketleri burada listelenecek.</p>
              </div>
            </TabsContent>

            <TabsContent value="categories">
              <div className="p-4 bg-muted/20 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Kategoriler burada listelenecek.</p>
              </div>
            </TabsContent>

            <TabsContent value="donusum" className="w-full max-w-full mx-0 p-0">
              <UrunDonusum />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

server {
    listen 80;
    server_name chat.bormeg.fun;

    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name chat.bormeg.fun;

    ssl_certificate /etc/letsencrypt/live/chat.bormeg.fun/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chat.bormeg.fun/privkey.pem;

    location / {
        proxy_pass http://localhost:4000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
}

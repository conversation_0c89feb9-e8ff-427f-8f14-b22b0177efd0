import React, { useState, useEffect, useRef } from 'react';
import { AlertTriangle, Barcode, RotateCw, Check, List, PlusCircle, PackagePlus } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { SiloData } from "@/models/SiloData";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ApiService } from '@/services/apiService';
import UretimVerileriTablosu from "@/components/production/uretim-verileri/UretimVerileriTablosu";
import { UrunModel } from '@/models/UrunModel';

const UrunDonusum: React.FC = () => {
  // Barkod tarama
  const [barcodeValue, setBarcodeValue] = useState<string>('');
  const [scannedProduct, setScannedProduct] = useState<SiloData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirm, setShowConfirm] = useState<boolean>(false);
  const [converting, setConverting] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Kategori ve toplu dönüşüm
  const [delikliBoru, setDelikliBoru] = useState<{ stokKodu: string, stokAdi: string }[]>([]);
  const [seciliDelikliBoru, setSeciliDelikliBoru] = useState<string>('');
  const [donusumAdet, setDonusumAdet] = useState<number>(1);
  const [keceliBoruListesi, setKeceliBoruListesi] = useState<SiloData[]>([]);
  const [loading2, setLoading2] = useState<boolean>(false);
  const [topluIslemOnay, setTopluIslemOnay] = useState<boolean>(false);

  // Toplu dönüşüm için barkod eşleştirme
  const [bulkBarcodeValue, setBulkBarcodeValue] = useState<string>('');
  const [matchedPairs, setMatchedPairs] = useState<{ delikliId: number; keceliId: number }[]>([]);

  // Yeni ürün ekleme için state'ler
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [newBarcodeValue, setNewBarcodeValue] = useState<string>('');
  const [addingProduct, setAddingProduct] = useState<boolean>(false);

  // Conversion records list
  const [conversionRecords, setConversionRecords] = useState<SiloData[]>([]);
  const [loadingRecords, setLoadingRecords] = useState<boolean>(false);

  // Ürün modelleri ve ad eşlemesi
  const [urunler, setUrunler] = useState<UrunModel[]>([]);
  const [urunAdlari, setUrunAdlari] = useState<Record<string, string>>({});

  // Kategori seçenekleri
  const categoryOptions = [
    { value: 'MRZ-ST00661', label: 'Ø 80 DELİKLİ BORU' },
    { value: 'MGS0055', label: 'Ø 100 DELİKLİ BORU' },
    { value: 'MRZ-ST00665', label: 'Ø 125 DELİKLİ BORU' },
    { value: 'MGS0053', label: 'Ø 160 DELİKLİ BORU' },
    { value: 'MGS0056', label: 'Ø 200 DELİKLİ BORU' }
  ];

  // Delikli boru kategorilerini yükle
  useEffect(() => {
    // Ürün modellerini yükle
    const fetchUrunler = async () => {
      try {
        const data = await ApiService.fetchUrunler();
        setUrunler(data);
        const map: Record<string, string> = {};
        data.forEach(u => {
          map[u.stokKodu] = u.stokAdi;
        });
        setUrunAdlari(map);
      } catch (err) {
        console.error('Urun modelleri yüklenirken hata:', err);
      }
    };
    fetchUrunler();

    const fetchDelikliBoru = async () => {
      try {
        const data = await ApiService.fetchDelikliBoru();
        console.log("Delikli boru kategorileri:", data);
        setDelikliBoru(data);
      } catch (error) {
        console.error('Delikli boru kategorileri yüklenirken hata:', error);
      }
    };

    fetchDelikliBoru();
  }, []);

  // Function to process barcode
  const processBarcodeInput = async (value: string) => {
    // Reset states
    setError(null);
    setScannedProduct(null);

    // Extract ID from barcode (PKT-12345 format)
    const match = value.match(/PKT-(\d+)/);
    if (!match) {
      setError('Geçersiz barkod formatı. Barkod "PKT-12345" formatında olmalıdır.');
      return;
    }

    const productId = match[1];
    console.log(`ID çıkarıldı: ${productId} (Orijinal barkod: ${value})`);
    await fetchProduct(productId);
  };

  // Handle barcode input change
  const handleBarcodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBarcodeValue(e.target.value);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (barcodeValue) {
      processBarcodeInput(barcodeValue);
    }
  };

  // Handle barcode scan
  const handleBarcodeScan = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Typically barcode scanners end with Enter key
    if (e.key === 'Enter' && barcodeValue) {
      processBarcodeInput(barcodeValue);
      // Clear the input after processing
      setBarcodeValue('');
    }
  };

  // Fetch product by ID
  const fetchProduct = async (id: string) => {
    setLoading(true);
    console.log(`Ürün bilgisi getiriliyor - ID: ${id}`);
    try {
      // silodata_genel tablosundan ürün bilgisini getir
      const data = await ApiService.fetchSiloDataGenelById(id);
      console.log(`Ürün bilgisi başarıyla alındı:`, data);

      if (data) {
        setScannedProduct(data);
        // If product status is active, show confirmation dialog
        if (data.durum === 'aktif') {
          setShowConfirm(true);
        } else {
          setError('Bu ürün dönüştürülemez. Mevcut durumu: ' + (data.durum || 'Bilinmiyor'));
        }
      } else {
        setError('Ürün bulunamadı');
      }
    } catch (error: any) {
      console.error('Ürün getirme hatası:', error);
      // Kayıt bulunamadı veya 404 durumu
      if (error.message.includes('404') || error.message === 'Ürün bulunamadı') {
        setError('Dönüşüm yapılacak uygun kayıt bulunamadı');
      } else {
        setError('Ürün getirme sırasında bir hata oluştu. Lütfen bağlantınızı kontrol edin.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Convert product
  const convertProduct = async () => {
    if (!scannedProduct || !scannedProduct.id) return;

    const productId = String(scannedProduct.id);
    console.log(`Ürün dönüştürme işlemi başlatılıyor - ID: ${productId}`);
    setConverting(true);

    try {
      // silodata_genel tablosunda dönüşüm yap
      await ApiService.convertSiloDataGenel(productId);
      console.log(`Ürün başarıyla dönüştürüldü - ID: ${productId}`);

      toast({
        title: "Dönüşüm Başarılı",
        description: `Ürün ID: ${productId} başarıyla dönüştürüldü.`,
        variant: "default",
      });

      // Reset states after successful conversion
      setScannedProduct(null);
      setBarcodeValue('');
      setShowConfirm(false);

      // Focus back to input for next scan
      if (inputRef.current) {
        inputRef.current.focus();
      }
    } catch (error: any) {
      console.error('Dönüşüm hatası:', error);

      toast({
        title: "Dönüşüm Hatası",
        description: error.message || 'Dönüşüm sırasında bir hata oluştu',
        variant: "destructive",
      });
    } finally {
      setConverting(false);
    }
  };

  // Handle cancel confirmation
  const handleCancelConfirm = () => {
    setShowConfirm(false);
    setBarcodeValue('');
    setScannedProduct(null);
    // Focus back to input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Delikli boru seçimi değiştiğinde
  const handleDelikliBorSec = async (value: string) => {
    setSeciliDelikliBoru(value);
    setKeceliBoruListesi([]);
    setError(null);
  };

  // Adet değiştiğinde
  const handleAdetDegistir = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 1;
    setDonusumAdet(Math.max(1, Math.min(100, value))); // 1-100 arası değer
  };

  // Keçeli boru listesini getir
  const handleKeceliBoruGetir = async () => {
    if (!seciliDelikliBoru || donusumAdet < 1) return;

    setLoading2(true);
    setKeceliBoruListesi([]);
    setError(null);

    try {
      // Seçili delikli borunun keçeli boru karşılığını bul
      const keceliBoruKodu = await ApiService.getKeceliBoru(seciliDelikliBoru);

      if (!keceliBoruKodu) {
        setError('Bu delikli borunun keçeli boru karşılığı bulunamadı.');
        return;
      }

      // Keçeli boru listesini silodata_genel tablosundan getir
      const keceliBoruler = await ApiService.fetchSiloDataGenelByStok(keceliBoruKodu, donusumAdet);

      if (!keceliBoruler || keceliBoruler.length === 0) {
        setError(`${keceliBoruKodu} kodlu keçeli borudan yeterli sayıda aktif ürün bulunamadı.`);
        return;
      }

      if (keceliBoruler.length < donusumAdet) {
        setError(`Yalnızca ${keceliBoruler.length} adet aktif keçeli boru bulunabildi, ${donusumAdet} adet isteniyordu.`);
      }

      // Keçeli boru listesini ayarla
      setKeceliBoruListesi(keceliBoruler);

    } catch (error: any) {
      console.error('Keçeli boru listesi alınırken hata:', error);
      setError(error.message || 'Keçeli boru listesi alınırken bir hata oluştu.');
    } finally {
      setLoading2(false);
    }
  };

  // Toplu dönüşüm işlemini başlat
  const handleTopluDonusumBaslat = () => {
    if (keceliBoruListesi.length > 0) {
      setTopluIslemOnay(true);
    }
  };

  // Toplu dönüşüm işlemini onayla ve yap
  const handleTopluDonusumOnayla = async () => {
    setTopluIslemOnay(false);
    setLoading2(true);

    toast({
      title: "İşlem Başladı",
      description: `${keceliBoruListesi.length} adet ürün dönüştürülecek.`,
      variant: "default",
    });

    try {
      // Toplu işlemleri başlat
      let basariliIslemler = 0;
      let hataliIslemler = 0;

      // Önce delikli boru için silodata_genel tablosundan aktif ürün bulmalıyız
      const delikliBoruler = await ApiService.fetchSiloDataGenelByStok(seciliDelikliBoru, keceliBoruListesi.length);

      if (!delikliBoruler || delikliBoruler.length === 0) {
        throw new Error('Dönüştürülecek uygun kayıt bulunamadı');
      }

      if (delikliBoruler.length < keceliBoruListesi.length) {
        toast({
          title: "Uyarı",
          description: `Yalnızca ${delikliBoruler.length} adet aktif delikli boru bulunabildi, ${keceliBoruListesi.length} adet keçeli boru için dönüşüm yapılacak.`,
          variant: "default",
        });
      }

      for (let i = 0; i < keceliBoruListesi.length; i++) {
        if (i >= delikliBoruler.length) break;

        const delikliBoruId = delikliBoruler[i].id.toString();
        const keceliBoruId = keceliBoruListesi[i].id.toString();

        try {
          // Her bir delikli boru - keçeli boru çifti için dönüşüm işlemi yap (silodata_genel tablosunda)
          await ApiService.bulkConvertSiloDataGenel(delikliBoruId, keceliBoruId);
          basariliIslemler++;
        } catch (err) {
          console.error(`Dönüşüm hatası (Delikli: ${delikliBoruId}, Keçeli: ${keceliBoruId}):`, err);
          hataliIslemler++;
        }
      }

      // İşlem sonucu bildirimi
      if (basariliIslemler > 0) {
        toast({
          title: "Dönüşüm Tamamlandı",
          description: `${basariliIslemler} adet ürün başarıyla dönüştürüldü.${hataliIslemler > 0 ? ` ${hataliIslemler} adet işlemde hata oluştu.` : ''}`,
          variant: "default",
        });
      } else {
        toast({
          title: "Dönüşüm Hatası",
          description: "Hiçbir ürün dönüştürülemedi. Lütfen tekrar deneyin.",
          variant: "destructive",
        });
      }

      // İşlem tamamlandıktan sonra sıfırla
      setSeciliDelikliBoru('');
      setDonusumAdet(1);
      setKeceliBoruListesi([]);
    } catch (error: any) {
      console.error('Toplu dönüşüm işlemi sırasında hata oluştu:', error);
      toast({
        title: "Dönüşüm Hatası",
        description: error.message || "Toplu dönüşüm işlemi sırasında bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setLoading2(false);
    }
  };

  // Barkoddan ID al, DB'de kontrol et ve eşleştir
  const handleBulkBarcodeScan = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && bulkBarcodeValue) {
      e.preventDefault();
      const match = bulkBarcodeValue.match(/PKT-(\d+)/);
      if (!match) {
        toast({ title: 'Hata', description: 'Geçersiz barkod formatı', variant: 'destructive' });
        setBulkBarcodeValue('');
        return;
      }
      const delikliId = parseInt(match[1], 10);
      // Delikli ID silodata_genel tablosunda aktif ve doğru kategoride mi kontrol et
      try {
        const record = await ApiService.fetchSiloDataGenelById(String(delikliId));
        if (record.stokKodu !== seciliDelikliBoru || record.durum !== 'aktif') {
          toast({ title: 'Hata', description: 'Dönüştürülmeye uygun delikli boru bulunamadı', variant: 'destructive' });
          setBulkBarcodeValue('');
          return;
        }
      } catch (err) {
        toast({ title: 'Hata', description: 'Delikli boru bulunamadı', variant: 'destructive' });
        setBulkBarcodeValue('');
        return;
      }
      const nextIndex = matchedPairs.length;
      if (nextIndex >= keceliBoruListesi.length) {
        toast({ title: 'Uyarı', description: 'Tüm keçeli borular eşleştirildi', variant: 'default' });
        setBulkBarcodeValue('');
        return;
      }
      const keceliId = keceliBoruListesi[nextIndex].id;
      setMatchedPairs([...matchedPairs, { delikliId, keceliId }]);
      setBulkBarcodeValue('');
      toast({ title: 'Eşleştirildi', description: `Delikli: ${delikliId}, Keçeli: ${keceliId}`, variant: 'default' });
    }
  };

  // Sırayla dönüştür ve dönüştürülen keçeliyi listeden kaldır
  const handleConvertNext = async () => {
    if (matchedPairs.length === 0) return;
    const { delikliId, keceliId } = matchedPairs[0];
    try {
      await ApiService.bulkConvertSiloDataGenel(String(delikliId), String(keceliId));
      toast({ title: 'Dönüştürüldü', description: `Delikli ${delikliId} -> Keçeli ${keceliId}`, variant: 'default' });
      // Eşleşen çiftten sonraki için matchedPairs güncelle
      setMatchedPairs(prev => prev.slice(1));
      // Başarıyla dönüştürülen keçeliyi listeden kaldır
      setKeceliBoruListesi(prev => prev.filter(item => item.id !== keceliId));
    } catch (error: any) {
      toast({ title: 'Hata', description: error.message || 'Dönüştürme sırasında hata', variant: 'destructive' });
    }
  };

  // Tümünü dönüştür ve dönüştürülen keçelileri listeden kaldır
  const handleConvertAll = async () => {
    for (let pair of matchedPairs) {
      try {
        await ApiService.bulkConvertSiloDataGenel(String(pair.delikliId), String(pair.keceliId));
        // Her başarılı dönüşümde keçeliyi listeden çıkar
        setKeceliBoruListesi(prev => prev.filter(item => item.id !== pair.keceliId));
      } catch (error) {
        console.error('Toplu dönüştürme hatası (ID %s):', pair.keceliId, error);
      }
    }
    toast({ title: 'Tamamlandı', description: `${matchedPairs.length} çift dönüştürüldü`, variant: 'default' });
    setMatchedPairs([]);
  };

  // Yeni ürün ekleme fonksiyonları
  const handleNewBarcodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewBarcodeValue(e.target.value);
  };

  const handleNewProductSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCategory || !newBarcodeValue) {
      toast({
        title: "Hata",
        description: "Lütfen kategori seçin ve barkod numarası girin.",
        variant: "destructive",
      });
      return;
    }

    // Barkod formatını kontrol et (PKT-12345)
    const match = newBarcodeValue.match(/PKT-(\d+)/);
    if (!match) {
      toast({
        title: "Hata",
        description: 'Barkod "PKT-12345" formatında olmalıdır.',
        variant: "destructive",
      });
      return;
    }

    const boruAd = match[1]; // PKT-37437 -> 37437
    const currentDate = new Date().toISOString().slice(0, 19).replace('T', ' '); // YYYY-MM-DD HH:mm:ss

    setAddingProduct(true);

    try {
      await ApiService.addSiloDataGenel(currentDate, selectedCategory, boruAd);

      toast({
        title: "Başarılı",
        description: `Ürün başarıyla eklendi. Barkod: ${newBarcodeValue}`,
        variant: "default",
      });

      // Form'u temizle
      setSelectedCategory('');
      setNewBarcodeValue('');
    } catch (error: any) {
      console.error('Ürün ekleme hatası:', error);
      toast({
        title: "Hata",
        description: error.message || 'Ürün eklenirken bir hata oluştu.',
        variant: "destructive",
      });
    } finally {
      setAddingProduct(false);
    }
  };

  useEffect(() => {
    const fetchConversionRecords = async () => {
      setLoadingRecords(true);
      try {
        const response = await fetch('/api/silodata_genel');
        if (!response.ok) throw new Error(`API hata: ${response.status}`);
        const data = await response.json();
        const mapped: SiloData[] = data.map((item: any) => ({
          id: item.ID,
          tarih: new Date(item.tarih),
          boruAd: item.boru_ad,
          boruAg: typeof item.boru_ag === 'string' ? parseFloat(item.boru_ag) : item.boru_ag,
          buyukSAg: item.buyuk_s_ag,
          stokKodu: item.stok_kodu,
          stokAdi: item.stok_adi,
          durum: item.durum,
          hat: item.hat,
          donusumId: item.donusum_id,
          donusumTarihi: item.donusum_tarihi ? new Date(item.donusum_tarihi) : undefined,
        }));
        setConversionRecords(mapped);
      } catch (error) {
        console.error('Conversion records fetch error:', error);
      } finally {
        setLoadingRecords(false);
      }
    };

    fetchConversionRecords();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h2 className="text-2xl font-bold">Ürün Dönüşüm</h2>
        <p className="text-gray-500">
          Delikli borudan keçeli boruya dönüşüm işlemleri
        </p>
      </div>

      {/* Yeni Ürün Ekleme Bölümü */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <PackagePlus size={20} className="text-green-500" />
            <div className="text-lg font-medium">Eski Ürün Kaydı</div>
          </div>
          <p className="text-sm text-gray-600">
            Stokta bulunan eski ürünlerin sisteme kaydedilmesi için barkod giriş alanı
          </p>

          <form onSubmit={handleNewProductSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Kategori seçimi */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Ürün Kategorisi:</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Kategori seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Barkod girişi */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Barkod Numarası:</label>
                <Input
                  type="text"
                  placeholder="PKT-37437"
                  value={newBarcodeValue}
                  onChange={handleNewBarcodeChange}
                  disabled={!selectedCategory}
                />
                <p className="text-xs text-gray-500">
                  Barkod formatı: PKT-37437 (37437 boru_ad sütununa kaydedilir)
                </p>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={!selectedCategory || !newBarcodeValue || addingProduct}
              >
                {addingProduct ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    Ekleniyor...
                  </>
                ) : (
                  <>
                    <PackagePlus className="mr-2 h-4 w-4" />
                    Ürün Ekle
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </Card>

      {/* Tekli Dönüşüm Bölümü */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Barcode size={20} className="text-purple-500" />
            <div className="text-lg font-medium">Tekli Dönüşüm</div>
          </div>
          <p className="text-sm text-gray-600">
            Barkod tarayarak tekli ürün dönüşümü yapın
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Barkod Tarama:</label>
              <Input
                ref={inputRef}
                type="text"
                placeholder="PKT-12345 formatında barkod girin"
                value={barcodeValue}
                onChange={handleBarcodeChange}
                onKeyDown={handleBarcodeScan}
                disabled={loading}
              />
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={!barcodeValue || loading}>
                {loading ? (
                  <>
                    <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                    Aranıyor...
                  </>
                ) : (
                  <>
                    <Barcode className="mr-2 h-4 w-4" />
                    Ürün Ara
                  </>
                )}
              </Button>
            </div>
          </form>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Hata</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {scannedProduct && (
            <div className="border rounded-md p-4 bg-green-50">
              <h3 className="font-semibold mb-2">Bulunan Ürün</h3>
              <div className="text-sm space-y-1">
                <p><span className="font-medium">ID:</span> {scannedProduct.id}</p>
                <p><span className="font-medium">Stok Kodu:</span> {scannedProduct.stokKodu}</p>
                <p><span className="font-medium">Stok Adı:</span> {scannedProduct.stokAdi || 'Belirtilmemiş'}</p>
                <p><span className="font-medium">Durum:</span> {scannedProduct.durum}</p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Toplu dönüşüm bölümü */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <List size={20} className="text-blue-500" />
            <div className="text-lg font-medium">Toplu Dönüşüm</div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Delikli boru seçimi */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Delikli Boru Kategorisi:</label>
              <Select value={seciliDelikliBoru} onValueChange={handleDelikliBorSec}>
                <SelectTrigger>
                  <SelectValue placeholder="Kategori seçin" />
                </SelectTrigger>
                <SelectContent>
                  {delikliBoru.length > 0 ? (
                    delikliBoru.map((item) => (
                      <SelectItem key={item.stokKodu} value={item.stokKodu}>
                        {item.stokAdi} ({item.stokKodu})
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-data" disabled>
                      Kategori bulunamadı
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Adet girişi */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Dönüştürülecek Adet:</label>
              <Input
                type="number"
                min="1"
                max="100"
                value={donusumAdet}
                onChange={handleAdetDegistir}
                disabled={!seciliDelikliBoru}
              />
            </div>
          </div>

          {/* Keçeli boruları getir butonu */}
          <div className="flex justify-end">
            <Button
              onClick={handleKeceliBoruGetir}
              disabled={!seciliDelikliBoru || loading2}
            >
              {loading2 ? (
                <>
                  <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                  Yükleniyor...
                </>
              ) : (
                <>
                  <List className="mr-2 h-4 w-4" />
                  Keçeli Boruları Getir
                </>
              )}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Hata</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Keçeli boru listesi */}
          {keceliBoruListesi.length > 0 && (
            <div className="space-y-4">
              <div className="border rounded-md p-4 bg-blue-50">
                <h3 className="font-semibold mb-4">Dönüştürülecek Keçeli Borular ({keceliBoruListesi.length} adet)</h3>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-blue-100">
                      <tr>
                        <th className="p-2 text-left">ID</th>
                        <th className="p-2 text-left">Stok Kodu</th>
                        <th className="p-2 text-left">Stok Adı</th>
                        <th className="p-2 text-left">Durum</th>
                      </tr>
                    </thead>
                    <tbody>
                      {keceliBoruListesi.map((item) => (
                        <tr key={item.id} className="border-t">
                          <td className="p-2">{item.id}</td>
                          <td className="p-2">{item.stokKodu}</td>
                          <td className="p-2">{item.stokAdi || 'Belirtilmemiş'}</td>
                          <td className="p-2">{item.durum || 'Belirtilmemiş'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Keçeli boru listesi eşleştirme alanı */}
          {keceliBoruListesi.length > 0 && (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Delikli Boru Barkod Tara:</label>
                <Input
                  type="text"
                  placeholder="PKT-12345"
                  value={bulkBarcodeValue}
                  onChange={e => setBulkBarcodeValue(e.target.value)}
                  onKeyDown={handleBulkBarcodeScan}
                  disabled={bulkBarcodeValue === undefined}
                />
              </div>
              {matchedPairs.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-semibold">Eşleşen Çiftler:</h4>
                  <ul className="list-disc pl-5 text-sm">
                    {matchedPairs.map((p, i) => (
                      <li key={i}>Delikli ID: {p.delikliId} → Keçeli ID: {p.keceliId}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="flex space-x-2">
                <Button
                  onClick={handleConvertNext}
                  disabled={matchedPairs.length === 0}
                >
                  Sırayla Dönüştür
                </Button>
                <Button
                  onClick={handleConvertAll}
                  disabled={matchedPairs.length === 0}
                >
                  Tümünü Dönüştür
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Tekli dönüşüm onay dialog */}
      <AlertDialog open={showConfirm} onOpenChange={setShowConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Ürün Dönüşümü Onayı</AlertDialogTitle>
            <AlertDialogDescription>
              {scannedProduct?.stokKodu && (
                <>
                  Bu ürünü
                  {(() => {
                    // Delikli boru stok koduna göre keçeli boru adını belirle
                    const stokKodu = scannedProduct.stokKodu;
                    switch (stokKodu) {
                      case 'MRZ-ST00661': return 'Ø 80 KEÇELİ BORU';
                      case 'MGS0055': return 'Ø 100 KEÇELİ BORU';
                      case 'MRZ-ST00665': return 'Ø 125 KEÇELİ BORU';
                      case 'MGS0053': return 'Ø 160 KEÇELİ BORU';
                      case 'MGS0056': return 'Ø 200 KEÇELİ BORU';
                      default: return 'KEÇELİ BORU';
                    }
                  })()}
                  {" "}olarak dönüştürmek istediğinizden emin misiniz?
                </>
              )}
              <br /><br />
              <span className="font-semibold">Ürün ID:</span> {scannedProduct?.id}
              <br />
              <span className="font-semibold">Stok Kodu:</span> {scannedProduct?.stokKodu}
              <br />
              <span className="font-semibold">Ürün Adı:</span> {scannedProduct?.stokAdi || 'Belirtilmemiş'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelConfirm}>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={convertProduct} disabled={converting}>
              {converting ? (
                <>
                  <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                  Dönüştürülüyor...
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Dönüştür
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toplu dönüşüm onay dialog */}
      <AlertDialog open={topluIslemOnay} onOpenChange={setTopluIslemOnay}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Toplu Dönüşüm Onayı</AlertDialogTitle>
            <AlertDialogDescription>
              Seçtiğiniz {keceliBoruListesi.length} adet keçeli boruyu dönüştürmek istediğinizden emin misiniz?
              <br /><br />
              Bu işlem geri alınamaz ve tüm seçili ürünlerin durumları güncellenir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setTopluIslemOnay(false)}>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={handleTopluDonusumOnayla}>
              <Check className="mr-2 h-4 w-4" />
              Onayla ve Dönüştür
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Son Üretim Verileri Listesi */}
      <div>
        <div className="mb-2">
          <h3 className="text-lg font-bold text-blue-700">Son Üretim Verileri</h3>
          <p className="text-sm text-gray-600">En son üretim yapılan gündeki tüm veriler aşağıda listelenmiştir.</p>
        </div>
        <UretimVerileriTablosu
          veriler={conversionRecords}
          getUrunAdi={(stokKodu) => urunAdlari[stokKodu] || ''}
          getUrunCap={() => ''}
          formatBoruAdi={(item) => urunAdlari[item.stokKodu] || ''}
          loading={loadingRecords}
          showConversionColumns={true}
        />
      </div>
    </div>
  );
};

export default UrunDonusum;

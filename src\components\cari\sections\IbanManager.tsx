
import { AlertCircle, CreditCard, Building, Plus } from "lucide-react";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface IbanItem {
  iban: string;
  bankName: string;
}

interface IbanManagerProps {
  initialIban: string;
  updateIban: (value: string) => void;
  error?: string;
}

export const IbanManager = ({ initialIban, updateIban, error }: IbanManagerProps) => {
  const [ibanList, setIbanList] = useState<IbanItem[]>([{ 
    iban: initialIban || "", 
    bankName: "" 
  }]);

  const addNewIban = () => {
    setIbanList([...ibanList, { iban: "", bankName: "" }]);
  };

  const handleIbanChange = (index: number, field: keyof IbanItem, value: string) => {
    const newList = [...ibanList];
    newList[index] = { ...newList[index], [field]: value };
    setIbanList(newList);
    updateIban(newList[0].iban); // Save the first IBAN to form data
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-1">
        <CreditCard className="h-4 w-4 text-primary" />
        <Label htmlFor="iban" className="font-medium text-sm">Banka Bilgileri</Label>
      </div>
      
      {ibanList.map((item, index) => (
        <div key={index} className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="flex-1">
              <div className="flex items-center gap-1 mb-1">
                <Building className="h-3 w-3 text-primary/70" />
                <Label htmlFor={`bank-name-${index}`} className="text-xs">Banka Adı</Label>
              </div>
              <Input 
                id={`bank-name-${index}`} 
                placeholder="Banka adı" 
                value={item.bankName}
                onChange={(e) => handleIbanChange(index, "bankName", e.target.value)}
                className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
              />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-1 mb-1">
                <CreditCard className="h-3 w-3 text-primary/70" />
                <Label htmlFor={`iban-${index}`} className="text-xs">IBAN</Label>
              </div>
              <Input 
                id={`iban-${index}`} 
                placeholder="TR00 0000 0000 0000 0000 0000 00" 
                value={item.iban}
                onChange={(e) => handleIbanChange(index, "iban", e.target.value)}
                className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
              />
            </div>
          </div>
          
          {index === ibanList.length - 1 && (
            <Button 
              variant="outline" 
              size="sm" 
              className="whitespace-nowrap h-7 text-xs w-full mt-1" 
              onClick={addNewIban}
            >
              <Plus className="h-3 w-3 mr-1" /> Yeni Banka Hesabı
            </Button>
          )}
        </div>
      ))}
      
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
};


import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';

export type VehicleType = 'car' | 'truck' | 'van' | 'bus' | 'other';
export type VehicleStatus = 'active' | 'maintenance' | 'repair' | 'out-of-service' | 'retired';
export type FuelType = 'gasoline' | 'diesel' | 'electric' | 'hybrid' | 'lpg' | 'cng';

export interface Vehicle {
  id: string;
  name: string;
  plate: string;
  type: VehicleType;
  brand: string;
  model: string;
  modelYear: number;
  vin: string;
  status: VehicleStatus;
  assignedTo: string | null;
  department: string;
  fuelType: FuelType;
  purchaseDate: string;
  initialOdometer: number;
  currentOdometer: number;
  insuranceExpiry: string;
  inspectionExpiry: string;
  notes: string;
  createdAt: string;
}

export interface FuelRecord {
  id: string;
  vehicleId: string;
  date: string;
  odometer: number;
  fuelAmount: number;
  fuelPrice: number;
  totalCost: number;
  fullTank: boolean;
  station: string;
  driver: string;
  notes: string;
  createdAt: string;
}

export interface UsageRecord {
  id: string;
  vehicleId: string;
  driverId: string;
  startDate: string;
  endDate: string;
  purpose: string;
  startOdometer: number;
  endOdometer: number;
  distance: number;
  notes: string;
  createdAt: string;
}

export interface Driver {
  id: string;
  name: string;
  licenseNumber: string;
  licenseExpiry: string;
  contactNumber: string;
  email: string;
  department: string;
  totalDistance: number;
  totalTrips: number;
  status: 'active' | 'inactive';
  performanceRating: number;
  notes: string;
  createdAt: string;
}

interface VehicleStore {
  vehicles: Vehicle[];
  fuelRecords: FuelRecord[];
  usageRecords: UsageRecord[];
  drivers: Driver[];
  addVehicle: (vehicle: Omit<Vehicle, 'id' | 'createdAt'>) => Vehicle;
  updateVehicle: (id: string, updates: Partial<Vehicle>) => void;
  deleteVehicle: (id: string) => void;
  addFuelRecord: (record: Omit<FuelRecord, 'id' | 'createdAt'>) => FuelRecord;
  updateFuelRecord: (id: string, updates: Partial<FuelRecord>) => void;
  deleteFuelRecord: (id: string) => void;
  addUsageRecord: (record: Omit<UsageRecord, 'id' | 'createdAt'>) => UsageRecord;
  updateUsageRecord: (id: string, updates: Partial<UsageRecord>) => void;
  deleteUsageRecord: (id: string) => void;
  addDriver: (driver: Omit<Driver, 'id' | 'createdAt'>) => Driver;
  updateDriver: (id: string, updates: Partial<Driver>) => void;
  deleteDriver: (id: string) => void;
  generateDemoData: () => void;
}

export const useVehicleStore = create<VehicleStore>((set) => ({
  vehicles: [],
  fuelRecords: [],
  usageRecords: [],
  drivers: [],
  
  addVehicle: (vehicle) => {
    const newVehicle = {
      ...vehicle,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      vehicles: [...state.vehicles, newVehicle],
    }));
    return newVehicle;
  },
  
  updateVehicle: (id, updates) => {
    set((state) => ({
      vehicles: state.vehicles.map((vehicle) =>
        vehicle.id === id ? { ...vehicle, ...updates } : vehicle
      ),
    }));
  },
  
  deleteVehicle: (id) => {
    set((state) => ({
      vehicles: state.vehicles.filter((vehicle) => vehicle.id !== id),
    }));
  },
  
  addFuelRecord: (record) => {
    const newRecord = {
      ...record,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      fuelRecords: [...state.fuelRecords, newRecord],
    }));
    return newRecord;
  },
  
  updateFuelRecord: (id, updates) => {
    set((state) => ({
      fuelRecords: state.fuelRecords.map((record) =>
        record.id === id ? { ...record, ...updates } : record
      ),
    }));
  },
  
  deleteFuelRecord: (id) => {
    set((state) => ({
      fuelRecords: state.fuelRecords.filter((record) => record.id !== id),
    }));
  },
  
  addUsageRecord: (record) => {
    const newRecord = {
      ...record,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      usageRecords: [...state.usageRecords, newRecord],
    }));
    return newRecord;
  },
  
  updateUsageRecord: (id, updates) => {
    set((state) => ({
      usageRecords: state.usageRecords.map((record) =>
        record.id === id ? { ...record, ...updates } : record
      ),
    }));
  },
  
  deleteUsageRecord: (id) => {
    set((state) => ({
      usageRecords: state.usageRecords.filter((record) => record.id !== id),
    }));
  },
  
  addDriver: (driver) => {
    const newDriver = {
      ...driver,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      drivers: [...state.drivers, newDriver],
    }));
    return newDriver;
  },
  
  updateDriver: (id, updates) => {
    set((state) => ({
      drivers: state.drivers.map((driver) =>
        driver.id === id ? { ...driver, ...updates } : driver
      ),
    }));
  },
  
  deleteDriver: (id) => {
    set((state) => ({
      drivers: state.drivers.filter((driver) => driver.id !== id),
    }));
  },
  
  generateDemoData: () => {
    const vehicles: Vehicle[] = [
      {
        id: 'vehicle-1',
        name: 'Şirket Aracı 1',
        plate: '34ABC123',
        type: 'car',
        brand: 'Volkswagen',
        model: 'Passat',
        modelYear: 2021,
        vin: 'WVWZZZ3CZLE035461',
        status: 'active',
        assignedTo: 'driver-1',
        department: 'Yönetim',
        fuelType: 'diesel',
        purchaseDate: '2021-05-15T00:00:00Z',
        initialOdometer: 10,
        currentOdometer: 25430,
        insuranceExpiry: '2024-05-14T00:00:00Z',
        inspectionExpiry: '2024-06-10T00:00:00Z',
        notes: '',
        createdAt: '2021-05-15T10:30:00Z',
      },
      {
        id: 'vehicle-2',
        name: 'Nakliye Kamyonu',
        plate: '34XYZ456',
        type: 'truck',
        brand: 'Mercedes',
        model: 'Actros',
        modelYear: 2020,
        vin: 'WDB9634031L963742',
        status: 'active',
        assignedTo: 'driver-2',
        department: 'Lojistik',
        fuelType: 'diesel',
        purchaseDate: '2020-08-20T00:00:00Z',
        initialOdometer: 50,
        currentOdometer: 78200,
        insuranceExpiry: '2024-08-19T00:00:00Z',
        inspectionExpiry: '2024-09-15T00:00:00Z',
        notes: 'Son bakım: 75.000 KM',
        createdAt: '2020-08-20T14:15:00Z',
      },
      {
        id: 'vehicle-3',
        name: 'Personel Servisi',
        plate: '34DEF789',
        type: 'van',
        brand: 'Ford',
        model: 'Transit',
        modelYear: 2022,
        vin: 'WF0XXXTTGXNS45982',
        status: 'maintenance',
        assignedTo: 'driver-3',
        department: 'İnsan Kaynakları',
        fuelType: 'diesel',
        purchaseDate: '2022-02-10T00:00:00Z',
        initialOdometer: 15,
        currentOdometer: 18650,
        insuranceExpiry: '2025-02-09T00:00:00Z',
        inspectionExpiry: '2025-03-05T00:00:00Z',
        notes: 'Bakımda (18.11.2023 - 20.11.2023)',
        createdAt: '2022-02-10T09:45:00Z',
      },
    ];
    
    const drivers: Driver[] = [
      {
        id: 'driver-1',
        name: 'Ali Yılmaz',
        licenseNumber: 'B-123456',
        licenseExpiry: '2027-05-20T00:00:00Z',
        contactNumber: '0532 123 4567',
        email: '<EMAIL>',
        department: 'Yönetim',
        totalDistance: 25420,
        totalTrips: 187,
        status: 'active',
        performanceRating: 4.8,
        notes: 'Şirket Müdürü',
        createdAt: '2021-05-15T11:00:00Z',
      },
      {
        id: 'driver-2',
        name: 'Mehmet Kaya',
        licenseNumber: 'CE-789012',
        licenseExpiry: '2025-11-15T00:00:00Z',
        contactNumber: '0533 456 7890',
        email: '<EMAIL>',
        department: 'Lojistik',
        totalDistance: 78150,
        totalTrips: 89,
        status: 'active',
        performanceRating: 4.6,
        notes: 'Uzun yol sürücüsü',
        createdAt: '2020-08-20T15:00:00Z',
      },
      {
        id: 'driver-3',
        name: 'Ayşe Demir',
        licenseNumber: 'B-345678',
        licenseExpiry: '2026-07-10T00:00:00Z',
        contactNumber: '0545 789 0123',
        email: '<EMAIL>',
        department: 'İnsan Kaynakları',
        totalDistance: 18635,
        totalTrips: 423,
        status: 'active',
        performanceRating: 4.9,
        notes: 'Servis şoförü',
        createdAt: '2022-02-10T10:30:00Z',
      },
    ];
    
    const fuelRecords: FuelRecord[] = [
      {
        id: uuidv4(),
        vehicleId: 'vehicle-1',
        date: '2023-11-12T12:30:00Z',
        odometer: 25200,
        fuelAmount: 47.5,
        fuelPrice: 39.85,
        totalCost: 1892.88,
        fullTank: true,
        station: 'Petrol Ofisi',
        driver: 'Ali Yılmaz',
        notes: '',
        createdAt: '2023-11-12T12:35:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-2',
        date: '2023-11-15T18:15:00Z',
        odometer: 78000,
        fuelAmount: 155.8,
        fuelPrice: 39.12,
        totalCost: 6094.90,
        fullTank: true,
        station: 'Shell',
        driver: 'Mehmet Kaya',
        notes: 'Uzun yol dönüşü',
        createdAt: '2023-11-15T18:20:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-3',
        date: '2023-11-10T08:45:00Z',
        odometer: 18500,
        fuelAmount: 38.2,
        fuelPrice: 39.65,
        totalCost: 1514.63,
        fullTank: false,
        station: 'BP',
        driver: 'Ayşe Demir',
        notes: '',
        createdAt: '2023-11-10T08:50:00Z',
      },
    ];
    
    const usageRecords: UsageRecord[] = [
      {
        id: uuidv4(),
        vehicleId: 'vehicle-1',
        driverId: 'driver-1',
        startDate: '2023-11-14T09:00:00Z',
        endDate: '2023-11-14T17:30:00Z',
        purpose: 'Müşteri toplantısı',
        startOdometer: 25300,
        endOdometer: 25430,
        distance: 130,
        notes: 'İstanbul - Kocaeli arası',
        createdAt: '2023-11-14T09:05:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-2',
        driverId: 'driver-2',
        startDate: '2023-11-13T06:00:00Z',
        endDate: '2023-11-15T16:00:00Z',
        purpose: 'Mal teslimatı',
        startOdometer: 77850,
        endOdometer: 78200,
        distance: 350,
        notes: 'İstanbul - Ankara - İstanbul',
        createdAt: '2023-11-13T06:05:00Z',
      },
      {
        id: uuidv4(),
        vehicleId: 'vehicle-3',
        driverId: 'driver-3',
        startDate: '2023-11-10T07:30:00Z',
        endDate: '2023-11-10T18:00:00Z',
        purpose: 'Personel servisi',
        startOdometer: 18500,
        endOdometer: 18650,
        distance: 150,
        notes: 'Günlük servis rotası',
        createdAt: '2023-11-10T07:35:00Z',
      },
    ];
    
    set({ vehicles, drivers, fuelRecords, usageRecords });
  },
}));

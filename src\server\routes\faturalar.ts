
import express from 'express';
import { query } from '../../utils/db';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Tüm faturaları getir
router.get('/', async (req, res) => {
  try {
    const faturalar = await query(`
      SELECT f.*, c.unvan as cari_unvan 
      FROM faturalar f
      JOIN cari_hesaplar c ON f.cari_id = c.id
      ORDER BY f.olusturma_tarihi DESC
    `);
    res.json(faturalar);
  } catch (error) {
    console.error('Faturaları getirme hatası:', error);
    res.status(500).json({ error: 'Faturalar getirilemedi' });
  }
});

// ID'ye göre fatura getir
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const fatura = await query(`
      SELECT f.*, c.unvan as cari_unvan 
      FROM faturalar f
      JOIN cari_hesaplar c ON f.cari_id = c.id
      WHERE f.id = ?
    `, [id]);
    
    if (Array.isArray(fatura) && fatura.length === 0) {
      return res.status(404).json({ error: 'Fatura bulunamadı' });
    }
    
    // Fatura kalemlerini getir
    const faturaKalemleri = await query(`
      SELECT fk.*, u.isim as urun_isim, u.kod as urun_kodu
      FROM fatura_kalemleri fk
      LEFT JOIN urunler u ON fk.urun_id = u.id
      WHERE fk.fatura_id = ?
      ORDER BY fk.sira_no
    `, [id]);
    
    res.json({
      ...fatura[0],
      kalemler: faturaKalemleri
    });
  } catch (error) {
    console.error('Fatura getirme hatası:', error);
    res.status(500).json({ error: 'Fatura getirilemedi' });
  }
});

// Yeni fatura ekle
router.post('/', async (req, res) => {
  try {
    const { 
      numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto, 
      toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, 
      e_fatura, kalemler 
    } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!numara || !cari_id || !para_birimi || ara_toplam === undefined || 
        toplam_vergi === undefined || toplam === undefined || !durum || !vade_tarihi) {
      return res.status(400).json({ 
        error: 'Numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam, durum ve vade_tarihi alanları zorunludur' 
      });
    }
    
    // Kalemler dizisini kontrol et
    if (!Array.isArray(kalemler) || kalemler.length === 0) {
      return res.status(400).json({ error: 'En az bir fatura kalemi eklenmelidir' });
    }
    
    // Transaction başlat
    const connection = await (await import('../../utils/db')).pool.getConnection();
    await connection.beginTransaction();
    
    try {
      // Fatura ID oluştur
      const faturaId = uuidv4();
      
      // Fatura ekle
      await connection.execute(
        `INSERT INTO faturalar 
        (id, numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto, 
         toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, e_fatura) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [faturaId, numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto || 0, 
         toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, e_fatura || false]
      );
      
      // Fatura kalemlerini ekle
      for (let i = 0; i < kalemler.length; i++) {
        const kalem = kalemler[i];
        const kalemId = uuidv4();
        
        await connection.execute(
          `INSERT INTO fatura_kalemleri 
          (id, fatura_id, urun_id, aciklama, miktar, birim_fiyat, kdv_oran, kdv_tutar, 
           iskonto_oran, iskonto_tutar, satir_toplam, sira_no, birim) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [kalemId, faturaId, kalem.urun_id, kalem.aciklama, kalem.miktar, kalem.birim_fiyat, 
           kalem.kdv_oran, kalem.kdv_tutar, kalem.iskonto_oran || 0, kalem.iskonto_tutar || 0, 
           kalem.satir_toplam, i + 1, kalem.birim]
        );
      }
      
      // Transaction onayla
      await connection.commit();
      
      res.status(201).json({ 
        message: 'Fatura başarıyla oluşturuldu', 
        id: faturaId 
      });
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Fatura oluşturma hatası:', error);
    res.status(500).json({ error: 'Fatura oluşturulamadı' });
  }
});

// Fatura güncelle
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto, 
      toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, 
      e_fatura, kalemler 
    } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!numara || !cari_id || !para_birimi || ara_toplam === undefined || 
        toplam_vergi === undefined || toplam === undefined || !durum || !vade_tarihi) {
      return res.status(400).json({ 
        error: 'Numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam, durum ve vade_tarihi alanları zorunludur' 
      });
    }
    
    // Kalemler dizisini kontrol et
    if (!Array.isArray(kalemler) || kalemler.length === 0) {
      return res.status(400).json({ error: 'En az bir fatura kalemi eklenmelidir' });
    }
    
    // Transaction başlat
    const connection = await (await import('../../utils/db')).pool.getConnection();
    await connection.beginTransaction();
    
    try {
      // Faturayı güncelle
      await connection.execute(
        `UPDATE faturalar 
        SET numara = ?, cari_id = ?, para_birimi = ?, ara_toplam = ?, toplam_vergi = ?, 
        toplam_iskonto = ?, toplam = ?, durum = ?, odeme_yontemi = ?, taksit_sayisi = ?, 
        vade_tarihi = ?, notlar = ?, e_fatura = ? 
        WHERE id = ?`,
        [numara, cari_id, para_birimi, ara_toplam, toplam_vergi, toplam_iskonto || 0, 
         toplam, durum, odeme_yontemi, taksit_sayisi, vade_tarihi, notlar, e_fatura || false, id]
      );
      
      // Mevcut kalemleri sil
      await connection.execute('DELETE FROM fatura_kalemleri WHERE fatura_id = ?', [id]);
      
      // Yeni kalemleri ekle
      for (let i = 0; i < kalemler.length; i++) {
        const kalem = kalemler[i];
        const kalemId = uuidv4();
        
        await connection.execute(
          `INSERT INTO fatura_kalemleri 
          (id, fatura_id, urun_id, aciklama, miktar, birim_fiyat, kdv_oran, kdv_tutar, 
           iskonto_oran, iskonto_tutar, satir_toplam, sira_no, birim) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [kalemId, id, kalem.urun_id, kalem.aciklama, kalem.miktar, kalem.birim_fiyat, 
           kalem.kdv_oran, kalem.kdv_tutar, kalem.iskonto_oran || 0, kalem.iskonto_tutar || 0, 
           kalem.satir_toplam, i + 1, kalem.birim]
        );
      }
      
      // Transaction onayla
      await connection.commit();
      
      res.json({ message: 'Fatura başarıyla güncellendi' });
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Fatura güncelleme hatası:', error);
    res.status(500).json({ error: 'Fatura güncellenemedi' });
  }
});

// Fatura sil
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Transaction başlat
    const connection = await (await import('../../utils/db')).pool.getConnection();
    await connection.beginTransaction();
    
    try {
      // Önce fatura kalemlerini sil
      await connection.execute('DELETE FROM fatura_kalemleri WHERE fatura_id = ?', [id]);
      
      // Sonra faturayı sil
      await connection.execute('DELETE FROM faturalar WHERE id = ?', [id]);
      
      // Transaction onayla
      await connection.commit();
      
      res.json({ message: 'Fatura başarıyla silindi' });
    } catch (error) {
      // Hata durumunda transaction'ı geri al
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Fatura silme hatası:', error);
    res.status(500).json({ error: 'Fatura silinemedi' });
  }
});

export default router;

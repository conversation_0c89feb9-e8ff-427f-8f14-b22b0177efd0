
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { YearNavigation } from "./cashflow/YearNavigation";
import { BalanceHeader } from "./cashflow/BalanceHeader";
import { CashFlowTable } from "./cashflow/CashFlowTable";
import { initialBalance } from "./utils/cashFlowUtils";
import { type CheckedState } from "@radix-ui/react-checkbox";

export const CashFlowSummaryTable = () => {
  const [expandedSections, setExpandedSections] = useState({
    income: true,
    expenses: true
  });
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [showCurrentMonth, setShowCurrentMonth] = useState(true);

  // Function to toggle expansion of sections
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Function to handle checkbox change
  const handleCheckboxChange = (checked: CheckedState) => {
    if (checked === "indeterminate") return;
    setShowCurrentMonth(checked as boolean);
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-gray-800">
            {currentYear} Özet Nakit Akışı
          </CardTitle>
          <YearNavigation 
            currentYear={currentYear} 
            onYearChange={setCurrentYear} 
          />
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <BalanceHeader 
          initialBalance={initialBalance}
          showCurrentMonth={showCurrentMonth}
          onCheckedChange={handleCheckboxChange}
        />
        
        <CashFlowTable 
          expandedSections={expandedSections}
          toggleSection={toggleSection}
        />
      </CardContent>
    </Card>
  );
};

import React from 'react';
import { format } from 'date-fns';
import { SiloData } from '../../models/SiloData';
import { DataCalculations } from '../../utils/DataCalculations';

interface DataTableProps {
  filtrelenmisVeriler: SiloData[];
  getUrunAdi: (stokKodu: string) => string;
  selectedDiameter?: string | null;
}

const DataTable = ({ filtrelenmisVeriler, getUrunAdi, selectedDiameter }: DataTableProps) => {
  const diameterText = selectedDiameter ? `Ø ${selectedDiameter}` : "Tümü";

  // Tablo kategorilerini belirle
  const prepareTableHeaders = () => {
    return (
      <div className="bg-blue-700 text-white grid grid-cols-5 rounded-t-lg text-sm">
        <div className="p-2 font-medium">KAYIT SIRA NO</div>
        <div className="p-2 font-medium">TARİH, SAAT</div>
        <div className="p-2 font-medium">BORU ADI</div>
        <div className="p-2 font-medium">BORU AĞIRLIK</div>
        <div className="p-2 font-medium">ÜRETİM ADEDİ</div>
      </div>
    );
  };

  // Tablo satırlarını hazırla
  const prepareTableRows = () => {
    if (filtrelenmisVeriler.length === 0) {
      return (
        <div className="bg-white p-4 text-center text-gray-500">
          Bu filtrelere uygun kayıt bulunamadı.
        </div>
      );
    }

    return filtrelenmisVeriler.map((item, index) => {
      const urunAdi = getUrunAdi(item.stokKodu);
      const category = DataCalculations.getProductCategory(urunAdi);
      let rowClass = "grid grid-cols-5 border-b hover:bg-gray-50";

      return (
        <div key={item.id || item.ID} className={rowClass}>
          <div className="p-2 text-sm">{item.ID || 'N/A'}</div>
          <div className="p-2 flex items-center">
            <div className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center mr-1.5">
              <svg width="12" height="12" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
            </div>
            <div>
              <div className="text-xs">{item.saat || format(item.tarih, 'HH:mm')}</div>
              <div className="text-xs text-gray-500">{format(item.tarih, 'dd.MM.yyyy')}</div>
            </div>
          </div>
          <div className="p-2 text-sm overflow-hidden whitespace-nowrap text-ellipsis">
            {urunAdi}
          </div>
          <div className="p-2 text-sm">{parseFloat(item.boruAg).toFixed(1)}</div>
          <div className="p-2 text-sm">{filtrelenmisVeriler.length - index}</div>
        </div>
      );
    });
  };

  return (
    <div className="overflow-x-auto">
      <div className="flex items-center gap-1 mb-2">
        <div className="h-3 w-1 bg-blue-600 rounded-full"></div>
        <span className="text-xs font-medium text-blue-600">Veri Listesi: {diameterText}</span>
      </div>

      {prepareTableHeaders()}

      <div className="bg-white rounded-b-lg max-h-[calc(100vh-330px)] overflow-y-auto">
        {prepareTableRows()}
      </div>
    </div>
  );
};

export default DataTable;

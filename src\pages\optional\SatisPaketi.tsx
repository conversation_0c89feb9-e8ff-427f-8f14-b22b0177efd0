
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ShoppingCart, BarChart3, Users, FileText } from "lucide-react";

const SatisPaketi = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Satış Paketi</h2>
          <p className="text-muted-foreground">
            Perakende satış merkezi, toptan satış paneli ve e-ticaret kontrol merkezi yönetimi
          </p>
        </div>
        
        <Tabs defaultValue="retail" className="space-y-4">
          <TabsList>
            <TabsTrigger value="retail">Perakende Satış</TabsTrigger>
            <TabsTrigger value="wholesale"><PERSON><PERSON></TabsTrigger>
            <TabsTrigger value="ecommerce">E-Ticaret</TabsTrigger>
          </TabsList>
          
          <TabsContent value="retail" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Günlük Satışlar</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-pink-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₺12,543.00</div>
                  <p className="text-xs text-muted-foreground">+7% önceki güne göre</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Müşteri Trafiği</CardTitle>
                  <Users className="h-4 w-4 text-pink-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">342</div>
                  <p className="text-xs text-muted-foreground">+12% önceki güne göre</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Sepet Değeri (Ort.)</CardTitle>
                  <FileText className="h-4 w-4 text-pink-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₺138.20</div>
                  <p className="text-xs text-muted-foreground">-3% önceki güne göre</p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Perakende Satış Merkezi</CardTitle>
                <CardDescription>
                  Mağaza satışlarını, envanter düzeylerini ve müşteri trafiğini bu panelden yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <BarChart3 className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Satış grafiği burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="wholesale" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Toptan Satış Paneli</CardTitle>
                <CardDescription>
                  Kurumsal müşterileriniz ve distribütörleriniz için toptan satış yönetimi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">Toptan satış verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="ecommerce" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>E-Ticaret Kontrol Merkezi</CardTitle>
                <CardDescription>
                  Online satış kanallarınızı tek bir platformdan yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">E-ticaret dashboard'u burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default SatisPaketi;

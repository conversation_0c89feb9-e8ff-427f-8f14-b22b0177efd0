
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { HeartPulse, FileText, Calendar, Database } from "lucide-react";

const SaglikSektoru = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Sağlık Sektörü</h2>
          <p className="text-muted-foreground">
            Elektronik sağlık kaydı entegrasyonu, hasta muhasebesi ve randevu yönetimi
          </p>
        </div>
        
        <Tabs defaultValue="records" className="space-y-4">
          <TabsList>
            <TabsTrigger value="records"><PERSON><PERSON><PERSON><PERSON><PERSON></TabsTrigger>
            <TabsTrigger value="billing"><PERSON><PERSON></TabsTrigger>
            <TabsTrigger value="appointments"><PERSON><PERSON><PERSON></TabsTrigger>
          </TabsList>
          
          <TabsContent value="records" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Elektronik Sağlık Kaydı Entegrasyonu</CardTitle>
                <CardDescription>
                  Hasta verilerini yönetin ve entegre edin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Database className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Elektronik sağlık kaydı entegrasyonu burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="billing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Hasta Muhasebesi ve Faturalama</CardTitle>
                <CardDescription>
                  Hasta faturaları ve ödemelerini yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <FileText className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Hasta muhasebesi ve faturalama araçları burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="appointments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Randevu Yönetimi</CardTitle>
                <CardDescription>
                  Hasta, doktor ve kaynak randevularını yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <Calendar className="h-12 w-12 text-muted-foreground" />
                  <p className="ml-2 text-muted-foreground">Randevu yönetimi takvimi burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default SaglikSektoru;


import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ShoppingCart, Truck, Package, FileText, Clock, CheckCircle } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export const OrderManagement = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <ShoppingCart className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">A<PERSON><PERSON><PERSON></p>
              <h3 className="text-2xl font-semibold">18</h3>
              <p className="text-sm text-warning"><PERSON><PERSON><PERSON> bekliyor</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Truck className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Hazırlanan</p>
              <h3 className="text-2xl font-semibold">7</h3>
              <p className="text-sm text-success">Sevkiyata hazır</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Package className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-muted-foreground">Teslim Edilen</p>
              <h3 className="text-2xl font-semibold">124</h3>
              <p className="text-sm text-muted-foreground">Bu ay</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <FileText className="h-8 w-8 text-orange-500" />
            <div>
              <p className="text-sm text-muted-foreground">Faturalandırılan</p>
              <h3 className="text-2xl font-semibold">112</h3>
              <p className="text-sm text-muted-foreground">Bu ay</p>
            </div>
          </div>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Sipariş Yönetimi</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="active">
            <TabsList className="mb-4">
              <TabsTrigger value="active">Aktif Siparişler</TabsTrigger>
              <TabsTrigger value="completed">Tamamlanan</TabsTrigger>
              <TabsTrigger value="draft">Taslaklar</TabsTrigger>
            </TabsList>
            
            <TabsContent value="active">
              <div className="flex justify-end mb-4">
                <Button className="flex items-center gap-2">
                  <ShoppingCart className="h-4 w-4" />
                  Yeni Sipariş Oluştur
                </Button>
              </div>
              <div className="relative overflow-x-auto rounded-md border">
                <table className="w-full text-sm">
                  <thead className="bg-muted text-muted-foreground">
                    <tr>
                      <th className="px-4 py-3 text-left">Sipariş No</th>
                      <th className="px-4 py-3 text-left">Müşteri</th>
                      <th className="px-4 py-3 text-left">Tarih</th>
                      <th className="px-4 py-3 text-left">Tutar</th>
                      <th className="px-4 py-3 text-left">Durum</th>
                      <th className="px-4 py-3 text-right">İşlemler</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    <tr className="hover:bg-muted/50">
                      <td className="px-4 py-3 font-medium">SIP-2024-042</td>
                      <td className="px-4 py-3">Medica A.Ş.</td>
                      <td className="px-4 py-3 text-muted-foreground">15 Haziran 2024</td>
                      <td className="px-4 py-3 font-medium">₺15,750</td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                          <Clock className="mr-1 h-3 w-3" />
                          Hazırlanıyor
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <Button variant="ghost" size="sm">Detay</Button>
                        <Button variant="ghost" size="sm">Faturala</Button>
                      </td>
                    </tr>
                    <tr className="hover:bg-muted/50">
                      <td className="px-4 py-3 font-medium">SIP-2024-041</td>
                      <td className="px-4 py-3">Teknosa Ltd.</td>
                      <td className="px-4 py-3 text-muted-foreground">14 Haziran 2024</td>
                      <td className="px-4 py-3 font-medium">₺28,430</td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Sevkiyata Hazır
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <Button variant="ghost" size="sm">Detay</Button>
                        <Button variant="ghost" size="sm">Faturala</Button>
                      </td>
                    </tr>
                    <tr className="hover:bg-muted/50">
                      <td className="px-4 py-3 font-medium">SIP-2024-040</td>
                      <td className="px-4 py-3">Anadolu Bilişim</td>
                      <td className="px-4 py-3 text-muted-foreground">12 Haziran 2024</td>
                      <td className="px-4 py-3 font-medium">₺9,870</td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                          <Truck className="mr-1 h-3 w-3" />
                          Sevk Edildi
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <Button variant="ghost" size="sm">Detay</Button>
                        <Button variant="ghost" size="sm">Faturala</Button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div className="flex justify-end mt-4">
                <Button variant="outline" size="sm">Tüm Siparişleri Gör</Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

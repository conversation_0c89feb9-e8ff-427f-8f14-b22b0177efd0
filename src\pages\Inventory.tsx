
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import MainLayout from "@/components/layout/MainLayout";
import { Package2, AlertCircle, Plus, FileDown, FileUp } from "lucide-react";
import InventoryTable from "@/components/inventory/InventoryTable";
import CategoryList from "@/components/inventory/CategoryList";
import SupplierList from "@/components/inventory/SupplierList";
import InventoryStats from "@/components/inventory/InventoryStats";
import { useInventoryStore } from "@/stores/inventoryStore";
import { useToast } from "@/hooks/use-toast";
import { useEffect } from "react";

const Inventory = () => {
  const { products, lowStockProducts } = useInventoryStore();
  const { toast } = useToast();
  
  // Show alerts for low stock products
  useEffect(() => {
    if (lowStockProducts.length > 0) {
      toast({
        title: "Stok Uyarısı",
        description: `${lowStockProducts.length} ürün minimum stok seviyesinin altında!`,
        variant: "destructive",
      });
    }
  }, [lowStockProducts.length, toast]);

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Package2 className="h-8 w-8 text-primary" />
              Stok Yönetimi
            </h2>
            <p className="text-muted-foreground">
              Ürünleri, kategorileri ve tedarikçileri burada yönetin.
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" className="gap-1">
              <FileDown className="h-4 w-4" />
              Dışa Aktar
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <FileUp className="h-4 w-4" />
              İçe Aktar
            </Button>
            <Button size="sm" className="gap-1">
              <Plus className="h-4 w-4" />
              Yeni Ürün
            </Button>
          </div>
        </div>

        {lowStockProducts.length > 0 && (
          <Card className="p-4 bg-destructive/10 border-destructive/20">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium">Stok Uyarısı</h3>
                <p className="text-sm text-muted-foreground">
                  {lowStockProducts.length} ürün minimum stok seviyesinin altına düştü. Lütfen stok ekleyin.
                </p>
              </div>
            </div>
          </Card>
        )}

        <InventoryStats />

        <Tabs defaultValue="products">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="products">Ürünler</TabsTrigger>
            <TabsTrigger value="categories">Kategoriler</TabsTrigger>
            <TabsTrigger value="suppliers">Tedarikçiler</TabsTrigger>
          </TabsList>
          <TabsContent value="products" className="mt-4">
            <InventoryTable />
          </TabsContent>
          <TabsContent value="categories" className="mt-4">
            <CategoryList />
          </TabsContent>
          <TabsContent value="suppliers" className="mt-4">
            <SupplierList />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Inventory;


import { Button } from "@/components/ui/button";
import { PlusCircle, Download, Send } from "lucide-react";
import { useState, useEffect } from "react";
import { CreateReportDialog } from "./CreateReportDialog";
import { ScheduleReportDialog } from "./ScheduleReportDialog";
import { useReportsStore } from "@/stores/reportsStore";
import { useToast } from "@/hooks/use-toast";

export const ReportsHeader = () => {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isScheduleOpen, setIsScheduleOpen] = useState(false);
  const { savedReports, generateDemoData } = useReportsStore();
  const { toast } = useToast();

  useEffect(() => {
    if (savedReports.length === 0) {
      generateDemoData();
    }
  }, [savedReports.length, generateDemoData]);

  const handleExportAll = () => {
    // In a real application, this would generate a file for download
    toast({
      title: "Raporlar dışa aktarıldı",
      description: "Tüm raporlar başarıyla dışa aktarıldı.",
    });
  };

  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Raporlama & Analiz</h2>
        <p className="text-muted-foreground">
          Özel raporlar oluşturun, analiz edin ve paylaşın
        </p>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          onClick={handleExportAll}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Tümünü İndir
        </Button>
        <Button onClick={() => setIsScheduleOpen(true)}>
          <Send className="mr-2 h-4 w-4" />
          Rapor Zamanla
        </Button>
        <Button onClick={() => setIsCreateOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Rapor
        </Button>
      </div>
      
      <CreateReportDialog 
        open={isCreateOpen} 
        onOpenChange={setIsCreateOpen}
      />
      
      <ScheduleReportDialog
        open={isScheduleOpen}
        onOpenChange={setIsScheduleOpen}
      />
    </div>
  );
};

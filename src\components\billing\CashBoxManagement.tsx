
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

// Demo data
const cashRegistersData = [
  { id: "1", name: "<PERSON> Kasa", location: "Merkez Ofis", responsiblePerson: "Ahmet Yılmaz", openingBalance: 5000.00, currentBalance: 7850.50, currency: "TRY", lastUpdate: "2024-07-10" },
  { id: "2", name: "<PERSON><PERSON> Kasası", location: "İstanbul Şube", responsiblePerson: "Ayşe Demir", openingBalance: 3000.00, currentBalance: 4250.75, currency: "TRY", lastUpdate: "2024-07-09" },
  { id: "3", name: "<PERSON><PERSON><PERSON><PERSON>sası", location: "Me<PERSON><PERSON> Ofis", responsiblePerson: "<PERSON><PERSON><PERSON>", openingBalance: 1000.00, currentBalance: 1250.00, currency: "USD", lastUpdate: "2024-07-10" },
  { id: "4", name: "<PERSON><PERSON><PERSON><PERSON> Kasası", location: "Fuar Alanı", responsiblePerson: "Zeynep Arslan", openingBalance: 2000.00, currentBalance: 1580.25, currency: "TRY", lastUpdate: "2024-07-08" }
];

// Column definitions for cash registers
const cashRegistersColumns = [
  {
    header: "Kasa Adı",
    accessorKey: "name"
  },
  {
    header: "Konum",
    accessorKey: "location"
  },
  {
    header: "Sorumlu",
    accessorKey: "responsiblePerson"
  },
  {
    header: "Açılış Bakiyesi",
    accessorKey: "openingBalance",
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span>{currency} {row.original.openingBalance.toLocaleString()}</span>;
    }
  },
  {
    header: "Mevcut Bakiye",
    accessorKey: "currentBalance",
    cell: ({ row }) => {
      const currency = row.original.currency === "TRY" ? "₺" : row.original.currency === "USD" ? "$" : row.original.currency === "EUR" ? "€" : "";
      return <span className="font-medium">{currency} {row.original.currentBalance.toLocaleString()}</span>;
    }
  },
  {
    header: "Para Birimi",
    accessorKey: "currency"
  },
  {
    header: "Son Güncelleme",
    accessorKey: "lastUpdate"
  }
];

// Cash transactions data
const cashTransactionsData = [
  { id: "1", cashBoxName: "Ana Kasa", date: "2024-07-10", description: "Nakit Satış", amount: 1250.00, type: "income" },
  { id: "2", cashBoxName: "Ana Kasa", date: "2024-07-09", description: "Kırtasiye Malzemeleri", amount: 350.50, type: "expense" },
  { id: "3", cashBoxName: "Şube Kasası", date: "2024-07-09", description: "Nakit Satış", amount: 850.00, type: "income" },
  { id: "4", cashBoxName: "Ana Kasa", date: "2024-07-08", description: "İkram Masrafları", amount: 275.00, type: "expense" },
  { id: "5", cashBoxName: "Döviz Kasası", date: "2024-07-08", description: "Müşteri Ödemesi", amount: 500.00, type: "income" },
  { id: "6", cashBoxName: "Etkinlik Kasası", date: "2024-07-07", description: "Stand Giderleri", amount: 420.00, type: "expense" }
];

// Column definitions for cash transactions
const cashTransactionsColumns = [
  {
    header: "Kasa",
    accessorKey: "cashBoxName"
  },
  {
    header: "Tarih",
    accessorKey: "date"
  },
  {
    header: "Açıklama",
    accessorKey: "description"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => {
      const type = row.original.type;
      const className = type === "income" ? "text-green-600 font-medium" : "text-red-600 font-medium";
      return <span className={className}>₺ {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "İşlem Tipi",
    accessorKey: "type",
    cell: ({ row }) => {
      return row.original.type === "income" ? "Gelir" : "Gider";
    }
  }
];

export const CashBoxManagement = () => {
  const [activeTab, setActiveTab] = useState<"cashboxes" | "transactions">("cashboxes");

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={activeTab === "cashboxes" ? "default" : "outline"}
            onClick={() => setActiveTab("cashboxes")}
          >
            Kasa Listesi
          </Button>
          <Button 
            variant={activeTab === "transactions" ? "default" : "outline"}
            onClick={() => setActiveTab("transactions")}
          >
            Kasa Hareketleri
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Kasa Oluştur
        </Button>
      </div>

      {activeTab === "cashboxes" ? (
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-lg">Kasa Listesi</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable columns={cashRegistersColumns} data={cashRegistersData} />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-lg">Kasa Hareketleri</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable columns={cashTransactionsColumns} data={cashTransactionsData} />
          </CardContent>
        </Card>
      )}
    </div>
  );
};


import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { EnhancedDataTable } from "./EnhancedDataTable";
import { getPurchaseInvoiceColumns, purchaseInvoicesData } from "./purchase/PurchaseInvoiceColumns";
import { CreatePaymentOrderDialog } from "./payment/CreatePaymentOrderDialog";
import { CreatePaymentInstructionDialog } from "./payment/CreatePaymentInstructionDialog";
import { StatusFilterButtons } from "./purchase/StatusFilterButtons";
import { ActionButtons } from "./purchase/ActionButtons";

export const PurchaseInvoices = () => {
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [newInvoiceDialogOpen, setNewInvoiceDialogOpen] = useState(false);
  const [paymentOrderDialogOpen, setPaymentOrderDialogOpen] = useState(false);
  const [paymentInstructionDialogOpen, setPaymentInstructionDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [paymentOrderMode, setPaymentOrderMode] = useState<'single' | 'multiple' | 'free'>('single');
  
  // Filter data based on selected status
  const filteredData = statusFilter 
    ? purchaseInvoicesData.filter(invoice => invoice.paymentStatus === statusFilter)
    : purchaseInvoicesData;
  
  // Handle creating payment order for a specific invoice
  const handleCreatePaymentOrder = (invoice: any) => {
    setSelectedInvoice(invoice);
    setPaymentOrderMode('single');
    setPaymentOrderDialogOpen(true);
  };
  
  // Handle creating payment instruction for a specific invoice
  const handleCreatePaymentInstruction = (invoice: any) => {
    setSelectedInvoice(invoice);
    setPaymentInstructionDialogOpen(true);
  };
  
  // Handle bulk payment order
  const handleBulkPaymentOrder = () => {
    setSelectedInvoice(null);
    setPaymentOrderMode('multiple');
    setPaymentOrderDialogOpen(true);
  };
  
  // Handle creating free payment order
  const handleFreePaymentOrder = () => {
    setSelectedInvoice(null);
    setPaymentOrderMode('free');
    setPaymentOrderDialogOpen(true);
  };
  
  // Get columns with action handlers
  const columns = getPurchaseInvoiceColumns(
    handleCreatePaymentOrder,
    handleCreatePaymentInstruction
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <StatusFilterButtons 
          statusFilter={statusFilter} 
          setStatusFilter={setStatusFilter} 
        />
        
        <ActionButtons 
          onNewInvoice={() => setNewInvoiceDialogOpen(true)}
          onBulkPaymentOrder={handleBulkPaymentOrder}
          onPaymentInstruction={() => setPaymentInstructionDialogOpen(true)}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Alış Faturaları</CardTitle>
        </CardHeader>
        <CardContent>
          <EnhancedDataTable 
            columns={columns} 
            data={filteredData} 
            title="Alış Faturaları" 
          />
        </CardContent>
      </Card>
      
      {/* Payment Order Dialog */}
      <CreatePaymentOrderDialog 
        open={paymentOrderDialogOpen}
        onOpenChange={setPaymentOrderDialogOpen}
        invoice={selectedInvoice}
        mode={paymentOrderMode}
        onFreePaymentOrder={handleFreePaymentOrder}
      />
      
      {/* Payment Instruction Dialog */}
      <CreatePaymentInstructionDialog
        open={paymentInstructionDialogOpen}
        onOpenChange={setPaymentInstructionDialogOpen}
      />
    </div>
  );
};

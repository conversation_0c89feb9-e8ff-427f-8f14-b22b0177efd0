
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { User, Settings, Building, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

export const SidebarUserProfile = () => {
  const navigate = useNavigate();
  const { user, company, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="px-4 py-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-blue-50 dark:bg-blue-900/30 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-800/40 transition-colors">
            <Avatar>
              <AvatarImage src="" />
              <AvatarFallback className="bg-blue-100 text-blue-600 dark:bg-blue-800 dark:text-blue-200">
                {user?.avatar || "User"}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{user?.name || "Kullanıcı"}</div>
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                {user?.role || "Rol"}
                {company && (
                  <Badge variant="outline" className="ml-1 text-xs">
                    {company.name}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>Hesabım</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => navigate("/ayarlar")} className="cursor-pointer">
            <User className="mr-2 h-4 w-4" />
            <span>Profil</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => navigate("/ayarlar?tab=preferences")} className="cursor-pointer">
            <Settings className="mr-2 h-4 w-4" />
            <span>Ayarlar</span>
          </DropdownMenuItem>
          {company && (
            <DropdownMenuItem className="cursor-pointer">
              <Building className="mr-2 h-4 w-4" />
              <span>Kurum: {company.code}</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-red-500 hover:text-red-500">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Çıkış Yap</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};


import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { 
  PaymentOrderForm 
} from "./PaymentOrderForm";
import { 
  paymentOrderSchema,
  PaymentOrderFormValues 
} from "./PaymentOrderFormTypes";
import { PendingInvoicesTable } from "./PendingInvoicesTable";

// Demo data for pending invoices
const pendingInvoices = [
  { id: "2", invoiceNumber: "A2024-002", supplier: "XYZ Dağıtım A.Ş.", dueDate: "2024-08-08", totalAmount: 10325.59, currency: "TRY" },
  { id: "3", invoiceNumber: "A2024-003", supplier: "Teknoloji Partner Ltd.", dueDate: "2024-08-10", totalAmount: 26904.00, currency: "TRY" },
  { id: "4", invoiceNumber: "A2024-004", supplier: "Global Satış A.Ş.", dueDate: "2024-08-12", totalAmount: 6136.00, currency: "USD" },
];

type CreatePaymentOrderDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: any | null;
  mode: 'single' | 'multiple' | 'free';
  onFreePaymentOrder: () => void;
};

export const CreatePaymentOrderDialog = ({
  open,
  onOpenChange,
  invoice,
  mode,
  onFreePaymentOrder
}: CreatePaymentOrderDialogProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  
  // Default values for the form
  const defaultValues: Partial<PaymentOrderFormValues> = {
    date: new Date(),
    dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    priority: "medium",
    sourceType: "nakit",
    details: invoice ? `${invoice.invoiceNumber} numaralı fatura ödemesi` : "",
    amount: invoice ? invoice.totalAmount.toString() : "",
    requestingUser: "",
  };

  // Handle form submission
  const onSubmit = async (data: PaymentOrderFormValues) => {
    setLoading(true);
    
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log("Payment order data submitted:", data);
      
      toast({
        title: "Ödeme Emri Oluşturuldu",
        description: "Ödeme emriniz başarıyla oluşturuldu ve onay için gönderildi.",
      });
      
      // Reset form and close dialog
      handleCloseDialog();
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ödeme emri oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleInvoiceSelection = (id: string) => {
    setSelectedInvoices(prev => 
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };

  const handleCloseDialog = () => {
    setSelectedInvoices([]);
    onOpenChange(false);
  };

  // Get dialog title based on mode
  const getDialogTitle = () => {
    switch(mode) {
      case 'single':
        return `${invoice?.invoiceNumber || ''} için Ödeme Emri`;
      case 'multiple':
        return "Bekleyen Faturalar İçin Ödeme Emri";
      case 'free':
        return "Serbest Ödeme Emri";
      default:
        return "Ödeme Emri Oluştur";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-screen overflow-hidden p-4">
        <DialogHeader className="pb-1">
          <DialogTitle className="flex items-center gap-1 text-base">
            <FileText className="h-4 w-4" />
            {getDialogTitle()}
          </DialogTitle>
        </DialogHeader>

        {mode === 'multiple' && (
          <PendingInvoicesTable
            invoices={pendingInvoices}
            selectedInvoices={selectedInvoices}
            onToggleSelection={toggleInvoiceSelection}
            onFreePaymentOrder={onFreePaymentOrder}
          />
        )}

        <Card className="border shadow-sm">
          <CardContent className="p-4">
            <PaymentOrderForm 
              invoice={invoice}
              defaultValues={defaultValues}
              onSubmit={onSubmit}
              onCancel={handleCloseDialog}
              loading={loading}
            />
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
};


import { BasicInfoFields } from "./sections/BasicInfoFields";
import { IbanManager } from "./sections/IbanManager";

interface CariBasicInfoSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
}

export const CariBasicInfoSection = ({ formData, updateFormData, errors }: CariBasicInfoSectionProps) => {
  return (
    <div className="space-y-4 bg-white p-3 rounded-lg border shadow-sm">
      <h4 className="text-md font-semibold text-primary border-b pb-1"><PERSON><PERSON> Bilgiler</h4>
      
      <div className="space-y-3">
        <BasicInfoFields 
          formData={formData}
          updateFormData={updateFormData}
          errors={errors}
        />

        <IbanManager 
          initialIban={formData.iban || ""}
          updateIban={(value) => updateFormData("iban", value)}
          error={errors.iban}
        />
      </div>
    </div>
  );
};


import React from "react";
import { useVehicleStore } from "@/stores/vehicleStore";
import { VehiclesHeader } from "./VehiclesHeader";
import { VehiclesList } from "./VehiclesList";
import { VehiclesStats } from "./VehiclesStats";

export const VehiclesDashboard = () => {
  const generateDemoData = useVehicleStore((state) => state.generateDemoData);

  // Generate demo data when component mounts
  React.useEffect(() => {
    generateDemoData();
  }, [generateDemoData]);

  return (
    <div className="space-y-6">
      <VehiclesHeader />
      <VehiclesStats />
      <VehiclesList />
    </div>
  );
};

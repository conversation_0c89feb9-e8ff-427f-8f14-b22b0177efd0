import { Route, Routes } from "react-router-dom";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import Raporlar from "@/pages/Raporlar";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

// Finance Routes
import { FinanceRoutes } from "./FinanceRoutes";

// Optional Module Routes
import { OptionalModuleRoutes } from "./OptionalModuleRoutes";

// Other Main Pages
import Admin from "@/pages/Admin";
import Musteriler from "@/pages/Musteriler";
import Inventory from "@/pages/Inventory";
import Orders from "@/pages/Orders";
import Uretim from "@/pages/Uretim";
import Bakim from "@/pages/Bakim";
import Araclar from "@/pages/Araclar";
import EkipYonetimi from "@/pages/EkipYonetimi";
import Stok from "@/pages/Stok";
import Ayarlar from "@/pages/Ayarlar";

const MainRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/raporlar" element={<Raporlar />} />
      
      {/* Finance Routes */}
      <Route path="/finans/*" element={<FinanceRoutes />} />
      
      {/* Optional Module Routes */}
      <Route path="/modul/*" element={<OptionalModuleRoutes />} />
      
      {/* Other Main Pages */}
      <Route 
        path="/admin" 
        element={
          <ProtectedRoute requiredRole="admin">
            <Admin />
          </ProtectedRoute>
        } 
      />
      <Route path="/musteriler" element={<Musteriler />} />
      <Route path="/envanter" element={<Inventory />} />
      <Route path="/siparisler" element={<Orders />} />
      <Route path="/uretim" element={<Uretim />} />
      <Route path="/bakim" element={<Bakim />} />
      <Route path="/araclar" element={<Araclar />} />
      <Route path="/ekip" element={<EkipYonetimi />} />
      <Route path="/stok" element={<Stok />} />
      <Route path="/ayarlar" element={<Ayarlar />} />
      
      {/* Not Found Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default MainRoutes;


import React from "react";
import { Clock } from "lucide-react";

export const ApprovalSection = () => {
  return (
    <div className="grid grid-cols-3 gap-3 bg-muted/30 p-2.5 rounded-md border border-border">
      <div>
        <label className="text-[10px] font-medium text-muted-foreground">Onaylama Durumu</label>
        <div className="bg-background px-2.5 py-1.5 rounded border border-border text-[11px] mt-1">
          Beklemede
        </div>
      </div>
      
      <div>
        <label className="text-[10px] font-medium text-muted-foreground">On<PERSON>layan</label>
        <div className="bg-background px-2.5 py-1.5 rounded border border-border text-muted-foreground text-[11px] mt-1">
          -
        </div>
      </div>
      
      <div>
        <label className="text-[10px] font-medium text-muted-foreground"><PERSON><PERSON><PERSON><PERSON></label>
        <div className="bg-background px-2.5 py-1.5 rounded border border-border text-muted-foreground text-[11px] mt-1">
          -
        </div>
      </div>
    </div>
  );
};

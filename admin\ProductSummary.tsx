import React from 'react';
import { Card } from '@/components/ui/card';
import { ProductTypeSummary, CategorySummary } from '../../utils/DataCalculations';

interface ProductSummaryProps {
  productTypeSummary: ProductTypeSummary[];
  selectedDiameter?: string | null;
}

const ProductSummary = ({ productTypeSummary, selectedDiameter }: ProductSummaryProps) => {
  // Kategorilere göre veri hazırlama
  const prepareCategories = (): CategorySummary[] => {
    // Varsayılan boş kategoriler
    const categories: CategorySummary[] = [
      { kategori: 'Delikli', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 },
      { kategori: 'Deliksiz', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 },
      { kategori: 'Keçeli', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 }
    ];

    // Her ürün için kategori hesapla
    productTypeSummary.forEach(item => {
      const category = item.kategori || 'Delikli';
      const categoryIndex = categories.findIndex(cat => cat.kategori === category);

      if (categoryIndex !== -1) {
        categories[categoryIndex].adet += item.adet;
        categories[categoryIndex].toplamUzunluk += item.uzunluk || 0;
        categories[categoryIndex].ortalamaAgirlik += item.toplamAgirlik;
      }
    });

    // Ortalama ağırlığı hesapla
    categories.forEach(category => {
      if (category.adet > 0 && category.toplamUzunluk > 0) {
        category.ortalamaAgirlik = category.ortalamaAgirlik / category.toplamUzunluk;
      }
    });

    return categories;
  };

  const categories = prepareCategories();
  const diameterText = selectedDiameter ? `Ø ${selectedDiameter}` : "Tümü";

  return (
    <div className="h-full">
      <div className="flex items-center gap-1 mb-2">
        <div className="h-3 w-1 bg-blue-600 rounded-full"></div>
        <span className="text-xs font-medium text-blue-600">Üretim Özeti: {diameterText}</span>
      </div>

      <div className="grid grid-cols-3 gap-3 h-[calc(100%-24px)]">
        {categories.map((category, index) => {
          // Renkleri kategoriye göre belirle
          let bgColor = "bg-blue-50";
          let textColor = "text-blue-700";
          let badgeColor = "bg-blue-500";

          if (category.kategori === 'Delikli') {
            bgColor = "bg-blue-50";
            textColor = "text-blue-700";
            badgeColor = "bg-blue-500";
          } else if (category.kategori === 'Deliksiz') {
            bgColor = "bg-green-50";
            textColor = "text-green-700";
            badgeColor = "bg-green-500";
          } else if (category.kategori === 'Keçeli') {
            bgColor = "bg-amber-50";
            textColor = "text-amber-700";
            badgeColor = "bg-amber-500";
          }

          return (
            <Card key={index} className={`p-3 ${bgColor} border-none shadow-sm h-full flex flex-col justify-between`}>
              <div className="flex justify-between items-center mb-1">
                <h3 className={`text-sm font-bold ${textColor}`}>
                  {category.kategori.toUpperCase()}
                </h3>
                <span className={`${badgeColor} text-white rounded-full px-2 py-0.5 text-xs`}>
                  {category.adet} adet
                </span>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center">
                  <div className="w-5 h-5 flex items-center justify-center mr-1">
                    <svg viewBox="0 0 24 24" className={`w-4 h-4 ${textColor}`} stroke="currentColor" fill="none" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Uzunluk</div>
                    <div className="text-xs font-semibold text-gray-800">
                      {category.toplamUzunluk.toFixed(1)} m
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="w-5 h-5 flex items-center justify-center mr-1">
                    <svg viewBox="0 0 24 24" className={`w-4 h-4 ${textColor}`} stroke="currentColor" fill="none" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Ağırlık/m</div>
                    <div className="text-xs font-semibold text-gray-800">
                      {category.ortalamaAgirlik.toFixed(4)} kg
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ProductSummary;

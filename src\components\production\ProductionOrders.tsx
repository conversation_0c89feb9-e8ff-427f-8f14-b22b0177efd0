
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, FileText, CheckCircle2 } from "lucide-react";

export const ProductionOrders = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium">Üretim Emirleri</h3>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Ye<PERSON>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Aktif Üretim Emirleri</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative overflow-x-auto rounded-md border">
            <table className="w-full text-sm">
              <thead className="bg-muted text-muted-foreground">
                <tr>
                  <th className="px-4 py-3 text-left">Emir No</th>
                  <th className="px-4 py-3 text-left">Plan No</th>
                  <th className="px-4 py-3 text-left">Ürün</th>
                  <th className="px-4 py-3 text-left">Miktar</th>
                  <th className="px-4 py-3 text-left">İlerleme</th>
                  <th className="px-4 py-3 text-left">Teslim Tarihi</th>
                  <th className="px-4 py-3 text-right">İşlemler</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">PO-2024-056</td>
                  <td className="px-4 py-3">PRD-2024-027</td>
                  <td className="px-4 py-3">Metal Ayaklı Sehpa</td>
                  <td className="px-4 py-3">100 adet</td>
                  <td className="px-4 py-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{width: '45%'}}></div>
                    </div>
                    <span className="text-xs text-gray-500">%45</span>
                  </td>
                  <td className="px-4 py-3">22.06.2024</td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">
                      <FileText className="h-4 w-4 mr-1" />
                      Detay
                    </Button>
                    <Button variant="ghost" size="sm">
                      <CheckCircle2 className="h-4 w-4 mr-1" />
                      İlerleme
                    </Button>
                  </td>
                </tr>
                <tr className="hover:bg-muted/50">
                  <td className="px-4 py-3 font-medium">PO-2024-055</td>
                  <td className="px-4 py-3">PRD-2024-026</td>
                  <td className="px-4 py-3">Dekoratif Vazo Set</td>
                  <td className="px-4 py-3">500 adet</td>
                  <td className="px-4 py-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{width: '0%'}}></div>
                    </div>
                    <span className="text-xs text-gray-500">%0</span>
                  </td>
                  <td className="px-4 py-3">20.06.2024</td>
                  <td className="px-4 py-3 text-right">
                    <Button variant="ghost" size="sm">
                      <FileText className="h-4 w-4 mr-1" />
                      Detay
                    </Button>
                    <Button variant="ghost" size="sm">
                      <CheckCircle2 className="h-4 w-4 mr-1" />
                      İlerleme
                    </Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

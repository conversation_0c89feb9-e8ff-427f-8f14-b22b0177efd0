
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Wallet, TrendingUp, TrendingDown, CreditCard, RefreshCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export const BankAccountManagement = () => {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Wallet className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm text-muted-foreground">Toplam Bakiye</p>
              <h3 className="text-2xl font-semibold">₺124,587.53</h3>
              <p className="text-sm text-success">Tüm Hesaplar</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <TrendingUp className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm text-muted-foreground">Toplam Gelir</p>
              <h3 className="text-2xl font-semibold">₺45,678.90</h3>
              <p className="text-sm text-success">Bu Ay</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <TrendingDown className="h-8 w-8 text-red-500" />
            <div>
              <p className="text-sm text-muted-foreground">Toplam Gider</p>
              <h3 className="text-2xl font-semibold">₺32,456.78</h3>
              <p className="text-sm text-destructive">Bu Ay</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Wallet className="h-8 w-8 text-purple-500" />
            <div>
              <p className="text-sm text-muted-foreground">Kasa Bakiyesi</p>
              <h3 className="text-2xl font-semibold">₺5,432.10</h3>
              <p className="text-sm text-muted-foreground">Nakit</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Banka Hesapları</CardTitle>
            <CardDescription>Bağlı banka hesaplarınızı yönetin</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Yeni Hesap Ekle
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <RefreshCcw className="h-4 w-4" />
                  Bakiyeleri Güncelle
                </Button>
              </div>
              
              <div className="space-y-3 mt-4">
                <div className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 p-2 rounded-md">
                      <Wallet className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">İş Bankası</p>
                      <p className="text-sm text-muted-foreground">TR45 0006 4000 0011 2345 6789 12</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">₺67,892.34</p>
                    <Badge variant="outline" className="text-xs">TRY</Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center gap-3">
                    <div className="bg-red-100 p-2 rounded-md">
                      <Wallet className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <p className="font-medium">Ziraat Bankası</p>
                      <p className="text-sm text-muted-foreground">TR33 0001 0002 3456 7890 1234 56</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">₺42,458.95</p>
                    <Badge variant="outline" className="text-xs">TRY</Badge>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center gap-3">
                    <div className="bg-purple-100 p-2 rounded-md">
                      <Wallet className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium">Garanti BBVA</p>
                      <p className="text-sm text-muted-foreground">TR66 0006 2000 3456 7890 1234 56</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">$1,578.45</p>
                    <Badge variant="outline" className="text-xs">USD</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Kasa Yönetimi</CardTitle>
            <CardDescription>Nakit akışı ve kasa işlemleri</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Kasa Girişi
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <TrendingDown className="h-4 w-4" />
                  Kasa Çıkışı
                </Button>
              </div>
              
              <div className="mt-4 space-y-4">
                <h4 className="text-sm font-medium">Son İşlemler</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">Kira Ödemesi</p>
                      <p className="text-xs text-muted-foreground">26 Mar 2024</p>
                    </div>
                    <div className="flex items-center">
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                      <span className="text-destructive font-medium">₺4,500.00</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">Nakit Satış</p>
                      <p className="text-xs text-muted-foreground">25 Mar 2024</p>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-600 font-medium">₺1,250.00</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b">
                    <div>
                      <p className="font-medium">Ofis Giderleri</p>
                      <p className="text-xs text-muted-foreground">24 Mar 2024</p>
                    </div>
                    <div className="flex items-center">
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                      <span className="text-destructive font-medium">₺850.00</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center py-2">
                    <div>
                      <p className="font-medium">Perakende Satış</p>
                      <p className="text-xs text-muted-foreground">23 Mar 2024</p>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-green-600 font-medium">₺2,780.00</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

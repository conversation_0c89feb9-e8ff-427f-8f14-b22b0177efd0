
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Calendar, Download, FileText, Filter, ChevronRight, ArrowUpDown, Eye, Info } from "lucide-react";

// Demo veriler
const cashFlowData = [
  { date: "01/07/2024", income: 18500, expense: 12800, balance: 5700 },
  { date: "02/07/2024", income: 9200, expense: 7500, balance: 1700 },
  { date: "03/07/2024", income: 12500, expense: 6800, balance: 5700 },
  { date: "04/07/2024", income: 8800, expense: 9500, balance: -700 },
  { date: "05/07/2024", income: 15200, expense: 8200, balance: 7000 },
  { date: "06/07/2024", income: 6500, expense: 5400, balance: 1100 },
  { date: "07/07/2024", income: 0, expense: 3200, balance: -3200 }
];

const accountBalances = [
  { id: "1", name: "Ana İşletme Hesabı", bank: "A Bankası", accountNo: "TR12 0001 2345 6789", balance: 125800, lastUpdate: "2024-07-07" },
  { id: "2", name: "Maaş Ödemeleri", bank: "B Bankası", accountNo: "TR45 0002 3456 7890", balance: 218500, lastUpdate: "2024-07-07" },
  { id: "3", name: "Euro Hesabı", bank: "C Bankası", accountNo: "TR78 0003 4567 8901", balance: 74300, lastUpdate: "2024-07-06" },
  { id: "4", name: "Döviz Hesabı (USD)", bank: "D Bankası", accountNo: "TR90 0004 5678 9012", balance: 98200, lastUpdate: "2024-07-07" }
];

const upcomingTransactions = [
  { id: "1", type: "income", description: "Müşteri Ödemesi - ABC Şirketi", amount: 45000, date: "2024-07-15", status: "Planlanmış", paymentMethod: "Havale", account: "Ana İşletme Hesabı", reference: "INV-2024-056" },
  { id: "2", type: "expense", description: "Personel Maaş Ödemeleri", amount: 120000, date: "2024-07-25", status: "Planlanmış", paymentMethod: "Banka", account: "Maaş Ödemeleri", reference: "PAYROLL-JUL24" },
  { id: "3", type: "expense", description: "Kira Ödemesi", amount: 15000, date: "2024-07-15", status: "Planlanmış", paymentMethod: "Otomatik Ödeme", account: "Ana İşletme Hesabı", reference: "RENT-JUL24" },
  { id: "4", type: "income", description: "Müşteri Ödemesi - XYZ Ltd", amount: 28500, date: "2024-07-18", status: "Planlanmış", paymentMethod: "EFT", account: "Ana İşletme Hesabı", reference: "INV-2024-072" },
  { id: "5", type: "expense", description: "Tedarikçi Ödemesi", amount: 32500, date: "2024-07-20", status: "Planlanmış", paymentMethod: "Çek", account: "Ana İşletme Hesabı", reference: "PO-2024-038" }
];

// Tablo sütunları
const accountsColumns = [
  {
    header: "Hesap Adı",
    accessorKey: "name"
  },
  {
    header: "Banka",
    accessorKey: "bank"
  },
  {
    header: "Hesap Numarası",
    accessorKey: "accountNo"
  },
  {
    header: "Bakiye",
    accessorKey: "balance",
    cell: ({ row }) => <span className="font-medium text-blue-600">₺{row.original.balance.toLocaleString()}</span>
  },
  {
    header: "Son Güncelleme",
    accessorKey: "lastUpdate"
  }
];

const transactionsColumns = [
  {
    header: "Tür",
    accessorKey: "type",
    cell: ({ row }) => {
      const type = row.original.type;
      return (
        <Badge className={type === "income" ? "bg-green-500" : "bg-red-500"}>
          {type === "income" ? "Gelir" : "Gider"}
        </Badge>
      );
    }
  },
  {
    header: "Açıklama",
    accessorKey: "description"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => <span className={row.original.type === "income" ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
      ₺{row.original.amount.toLocaleString()}
    </span>
  },
  {
    header: "Tarih",
    accessorKey: "date"
  },
  {
    header: "Hesap",
    accessorKey: "account",
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => (
      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
        {row.original.status}
      </Badge>
    )
  },
  {
    header: "İşlem",
    id: "actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
          <Eye className="h-3.5 w-3.5 mr-1" />
          Detay
        </Button>
        <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
          <Info className="h-3.5 w-3.5 mr-1" />
          İşlem
        </Button>
      </div>
    )
  }
];

export const CashFlowManagement = () => {
  // Hesaplamalar
  const totalCashBalance = accountBalances.reduce((sum, account) => sum + account.balance, 0);
  const upcomingIncome = upcomingTransactions
    .filter(t => t.type === "income")
    .reduce((sum, t) => sum + t.amount, 0);
  const upcomingExpenses = upcomingTransactions
    .filter(t => t.type === "expense")
    .reduce((sum, t) => sum + t.amount, 0);
  
  // Calculate projected balance
  const projectedBalance = totalCashBalance + upcomingIncome - upcomingExpenses;
  
  const [activeTab, setActiveTab] = useState<"accounts" | "calendar">("accounts");

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Nakit Akışı Yönetimi</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="text-xs h-8 gap-1">
            <Filter className="h-3.5 w-3.5" />
            Filtrele
          </Button>
          <Button variant="outline" size="sm" className="text-xs h-8 gap-1">
            <Download className="h-3.5 w-3.5" />
            Dışa Aktar
          </Button>
          <Button variant="outline" size="sm" className="text-xs h-8 gap-1">
            <Calendar className="h-3.5 w-3.5" />
            Tarih Seç
          </Button>
        </div>
      </div>

      {/* Özet Kartları */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <Card className="p-3 border-l-4 border-l-blue-500">
          <div className="flex flex-col">
            <div className="text-sm font-medium mb-1 text-muted-foreground">Toplam Nakit Pozisyonu</div>
            <div className="text-lg font-bold text-blue-600">₺{totalCashBalance.toLocaleString()}</div>
            <div className="flex justify-end">
              <Button variant="ghost" size="sm" className="h-6 gap-0.5 text-[10px] px-1 -mr-1 -mb-1">
                <FileText className="h-3 w-3" /> 
                Detay
              </Button>
            </div>
          </div>
        </Card>
        <Card className="p-3 border-l-4 border-l-green-500">
          <div className="flex flex-col">
            <div className="text-sm font-medium mb-1 text-muted-foreground">Yaklaşan Tahsilatlar</div>
            <div className="text-lg font-bold text-green-600">₺{upcomingIncome.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Önümüzdeki 15 gün</div>
          </div>
        </Card>
        <Card className="p-3 border-l-4 border-l-red-500">
          <div className="flex flex-col">
            <div className="text-sm font-medium mb-1 text-muted-foreground">Yaklaşan Ödemeler</div>
            <div className="text-lg font-bold text-red-600">₺{upcomingExpenses.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Önümüzdeki 15 gün</div>
          </div>
        </Card>
        <Card className="p-3 border-l-4 border-l-purple-500">
          <div className="flex flex-col">
            <div className="text-sm font-medium mb-1 text-muted-foreground">Projeksiyonlu Bakiye</div>
            <div className="text-lg font-bold text-purple-600">₺{projectedBalance.toLocaleString()}</div>
            <div className="text-xs text-muted-foreground">Tahmin: 15 gün sonra</div>
          </div>
        </Card>
      </div>

      {/* Tabs for Accounts and Calendar */}
      <div className="flex space-x-4">
        <Button 
          variant={activeTab === "accounts" ? "default" : "outline"}
          onClick={() => setActiveTab("accounts")}
          size="sm"
          className="h-9"
        >
          Banka ve Kasa Durumu
        </Button>
        <Button 
          variant={activeTab === "calendar" ? "default" : "outline"}
          onClick={() => setActiveTab("calendar")}
          size="sm" 
          className="h-9"
        >
          Ödeme ve Tahsilat Takvimi
        </Button>
      </div>

      {activeTab === "accounts" ? (
        <Card>
          <CardHeader className="py-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Banka ve Kasa Durumu</CardTitle>
            <Button variant="outline" size="sm" className="text-xs h-8 gap-1">
              <ArrowUpDown className="h-3.5 w-3.5" />
              Sırala
            </Button>
          </CardHeader>
          <CardContent>
            <DataTable columns={accountsColumns} data={accountBalances} />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader className="py-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg">Ödeme ve Tahsilat Takvimi</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="text-xs h-8">
                Tümünü Göster
              </Button>
              <Button size="sm" className="text-xs h-8 gap-1">
                <ChevronRight className="h-3.5 w-3.5" />
                Yeni İşlem
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <DataTable columns={transactionsColumns} data={upcomingTransactions} />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

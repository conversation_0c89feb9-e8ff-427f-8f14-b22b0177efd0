
import { useReportsStore } from "@/stores/reportsStore";
import { useEffect, useState } from "react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

export const SalesByTimeChart = () => {
  const { salesData, generateDemoData } = useReportsStore();
  const [chartData, setChartData] = useState<any[]>([]);
  
  useEffect(() => {
    if (salesData.length === 0) {
      generateDemoData();
    } else {
      // Format data for chart
      setChartData(salesData);
    }
  }, [salesData, generateDemoData]);

  if (chartData.length === 0) {
    return <div className="flex justify-center items-center h-60">Yükleniyor...</div>;
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" tick={{ fontSize: 12 }} tickFormatter={(value) => value.slice(5)} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip 
            formatter={(value: number) => [`₺${value.toLocaleString('tr-TR')}`, 'Satış']}
            labelFormatter={(label) => `Tarih: ${label}`}
          />
          <Line 
            type="monotone" 
            dataKey="sales" 
            stroke="#8884d8" 
            activeDot={{ r: 8 }} 
            name="Satış"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

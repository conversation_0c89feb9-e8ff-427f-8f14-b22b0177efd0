
import { addDays, format, subDays, subMonths } from "date-fns";

// Types for financial data
export interface FinancialMetric {
  id: string;
  name: string;
  value: number;
  change: number;
  type: "currency" | "percentage" | "count";
  trend: "up" | "down" | "neutral";
}

export interface ChartDataPoint {
  date: string;
  value: number;
  category?: string;
}

export interface TransactionData {
  id: string;
  date: string;
  description: string;
  amount: number;
  category: string;
  type: "income" | "expense";
}

export interface FinancialSummary {
  totalIncome: number;
  totalExpense: number;
  netBalance: number;
  profitMargin: number;
  metrics: FinancialMetric[];
}

// Mock data generation functions
export const generateFinancialMetrics = (): FinancialMetric[] => {
  return [
    {
      id: "1",
      name: "Toplam Satış",
      value: 245000,
      change: 8.5,
      type: "currency",
      trend: "up",
    },
    {
      id: "2",
      name: "Toplam Gider",
      value: 156000,
      change: 4.2,
      type: "currency",
      trend: "up",
    },
    {
      id: "3",
      name: "<PERSON> Kâr",
      value: 89000,
      change: 12.3,
      type: "currency",
      trend: "up",
    },
    {
      id: "4",
      name: "<PERSON><PERSON><PERSON>",
      value: 36.3,
      change: 3.8,
      type: "percentage",
      trend: "up",
    },
  ];
};

export const generateMonthlySales = (months = 12): ChartDataPoint[] => {
  const today = new Date();
  const data: ChartDataPoint[] = [];

  for (let i = months - 1; i >= 0; i--) {
    const date = subMonths(today, i);
    const value = Math.floor(Math.random() * 50000) + 50000;

    data.push({
      date: format(date, "MMM yyyy"),
      value,
    });
  }

  return data;
};

export const generateCategorySales = (): ChartDataPoint[] => {
  const categories = ["Elektronik", "Mobilya", "Giyim", "Gıda", "Diğer"];
  
  return categories.map(category => ({
    date: category,
    value: Math.floor(Math.random() * 50000) + 10000,
    category
  }));
};

export const generateTransactions = (count = 20): TransactionData[] => {
  const today = new Date();
  const transactions: TransactionData[] = [];
  const categories = ["Satış", "Hizmet", "Kira", "Personel", "Hammadde", "Vergiler", "Operasyonel"];

  for (let i = 0; i < count; i++) {
    const isIncome = Math.random() > 0.6;
    const date = subDays(today, Math.floor(Math.random() * 30));
    
    transactions.push({
      id: `tr-${i}`,
      date: format(date, "dd.MM.yyyy"),
      description: isIncome 
        ? `${categories[Math.floor(Math.random() * 3)]} Geliri #${Math.floor(Math.random() * 10000)}`
        : `${categories[Math.floor(Math.random() * 4) + 3]} Gideri`,
      amount: isIncome 
        ? Math.floor(Math.random() * 15000) + 5000
        : Math.floor(Math.random() * 10000) + 2000,
      category: isIncome 
        ? categories[Math.floor(Math.random() * 3)]
        : categories[Math.floor(Math.random() * 4) + 3],
      type: isIncome ? "income" : "expense"
    });
  }

  // Sort by date
  transactions.sort((a, b) => {
    const dateA = new Date(a.date.split('.').reverse().join('-'));
    const dateB = new Date(b.date.split('.').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });

  return transactions;
};

export const generateFinancialSummary = (): FinancialSummary => {
  const totalIncome = Math.floor(Math.random() * 200000) + 100000;
  const totalExpense = Math.floor(Math.random() * 100000) + 50000;
  const netBalance = totalIncome - totalExpense;
  const profitMargin = (netBalance / totalIncome) * 100;

  return {
    totalIncome,
    totalExpense,
    netBalance,
    profitMargin,
    metrics: generateFinancialMetrics()
  };
};

// Helper functions
export const formatCurrency = (value: number): string => {
  return `₺${value.toLocaleString('tr-TR')}`;
};

export const formatPercentage = (value: number): string => {
  return `%${value.toFixed(1)}`;
};

export const formatTrend = (value: number): "up" | "down" | "neutral" => {
  if (value > 0) return "up";
  if (value < 0) return "down";
  return "neutral";
};

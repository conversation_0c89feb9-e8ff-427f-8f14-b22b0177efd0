
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Truck, Package, Map, TrendingUp } from "lucide-react";

const TedarikZinciri = () => {
  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON></h2>
          <p className="text-muted-foreground">
            <PERSON> madde tedarik takibi, lojistik yönetimi ve sevkiyat planlama
          </p>
        </div>
        
        <Tabs defaultValue="material" className="space-y-4">
          <TabsList>
            <TabsTrigger value="material">Ham Madde Tedarik</TabsTrigger>
            <TabsTrigger value="logistics">Lojistik Yönetimi</TabsTrigger>
            <TabsTrigger value="shipping">Sevkiyat Planlama</TabsTrigger>
          </TabsList>
          
          <TabsContent value="material" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Aktif Tedarikçiler</CardTitle>
                  <Package className="h-4 w-4 text-teal-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">42</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Açık Sipariş</CardTitle>
                  <TrendingUp className="h-4 w-4 text-teal-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">16</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Kritik Stok</CardTitle>
                  <Package className="h-4 w-4 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">Acil tedarik gerekli</p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Ham Madde Tedarik Takibi</CardTitle>
                <CardDescription>
                  Tedarik süreçlerinizi takip edin ve optimize edin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">Tedarik verileri burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="logistics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Lojistik Yönetimi</CardTitle>
                <CardDescription>
                  Nakliye ve taşıma süreçlerinizi yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center mb-4">
                  <Map className="h-24 w-24 text-muted-foreground" />
                </div>
                <div className="h-[200px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">Lojistik haritası burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="shipping" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sevkiyat Planlama</CardTitle>
                <CardDescription>
                  Sevkiyat süreçlerinizi planlayin ve optimize edin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">Sevkiyat planlama aracı burada görüntülenecek</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default TedarikZinciri;

server {
    listen 80;
    server_name bormeg.fun www.bormeg.fun;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name bormeg.fun www.bormeg.fun;

    ssl_certificate /etc/letsencrypt/live/bormeg.fun/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/bormeg.fun/privkey.pem;

    root /var/www/html;
    index index.html index.htm;

    # Web API proxy yönlendirme
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Diğer statik içerikler
    location / {
         try_files $uri $uri/ /index.html;
    }
}


import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { DataTable } from "./DataTable";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Demo data
const chequesData = [
  { id: "1", chequeNo: "123456", bank: "İş Bankası", branch: "Merkez", drawerName: "ABC Ltd. Şti.", dueDate: "2024-08-15", amount: 15000.00, status: "pending", type: "receivable", entryDate: "2024-06-15", description: "Müşteri ödemesi" },
  { id: "2", chequeNo: "234567", bank: "Garanti Bankası", branch: "Beşiktaş", drawerName: "DEF A.Ş.", dueDate: "2024-08-20", amount: 22500.00, status: "endorsed", type: "receivable", entryDate: "2024-06-18", description: "Müşteri ödemesi" },
  { id: "3", chequeNo: "345678", bank: "Ziraat Bankası", branch: "Kadıköy", drawerName: "GHI Ltd.", dueDate: "2024-08-25", amount: 18750.00, status: "deposited", type: "receivable", entryDate: "2024-06-20", description: "Müşteri ödemesi" },
  { id: "4", chequeNo: "456789", bank: "Yapı Kredi", branch: "Şişli", drawerName: "XYZ Dağıtım A.Ş.", dueDate: "2024-09-10", amount: 35000.00, status: "pending", type: "payable", entryDate: "2024-07-01", description: "Tedarikçi ödemesi" },
  { id: "5", chequeNo: "567890", bank: "Akbank", branch: "Levent", drawerName: "LMN Holding", dueDate: "2024-09-15", amount: 42500.00, status: "delivered", type: "payable", entryDate: "2024-07-05", description: "Tedarikçi ödemesi" }
];

// Column definitions
const chequesColumns = [
  {
    header: "Çek No",
    accessorKey: "chequeNo"
  },
  {
    header: "Banka",
    accessorKey: "bank"
  },
  {
    header: "Şube",
    accessorKey: "branch"
  },
  {
    header: "Keşideci",
    accessorKey: "drawerName"
  },
  {
    header: "Vade",
    accessorKey: "dueDate"
  },
  {
    header: "Tutar",
    accessorKey: "amount",
    cell: ({ row }) => {
      return <span className="font-medium">₺ {row.original.amount.toLocaleString()}</span>;
    }
  },
  {
    header: "Tür",
    accessorKey: "type",
    cell: ({ row }) => {
      const type = row.original.type;
      const badgeVariant = type === "receivable" ? "success" : "warning";
      return <Badge variant={badgeVariant}>{type === "receivable" ? "Alınan Çek" : "Verilen Çek"}</Badge>;
    }
  },
  {
    header: "Durum",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;
      let badgeVariant: "default" | "secondary" | "success" | "warning" = "default";
      let statusLabel = "";
      
      switch(status) {
        case "pending":
          badgeVariant = "default";
          statusLabel = "Beklemede";
          break;
        case "endorsed":
          badgeVariant = "secondary";
          statusLabel = "Ciro Edildi";
          break;
        case "deposited":
          badgeVariant = "success";
          statusLabel = "Bankaya Verildi";
          break;
        case "delivered":
          badgeVariant = "warning";
          statusLabel = "Teslim Edildi";
          break;
        default:
          statusLabel = status;
      }
      
      return <Badge variant={badgeVariant}>{statusLabel}</Badge>;
    }
  },
  {
    header: "Kayıt Tarihi",
    accessorKey: "entryDate"
  }
];

export const ChequeManagement = () => {
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  
  // Filter data based on selected type
  const filteredData = typeFilter 
    ? chequesData.filter(cheque => cheque.type === typeFilter)
    : chequesData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <Button 
            variant={typeFilter === null ? "default" : "outline"}
            onClick={() => setTypeFilter(null)}
          >
            Tüm Çekler
          </Button>
          <Button 
            variant={typeFilter === "receivable" ? "default" : "outline"}
            onClick={() => setTypeFilter("receivable")}
          >
            Alınan Çekler
          </Button>
          <Button 
            variant={typeFilter === "payable" ? "default" : "outline"}
            onClick={() => setTypeFilter("payable")}
          >
            Verilen Çekler
          </Button>
        </div>
        
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Yeni Çek Ekle
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Çek Yönetimi</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={chequesColumns} data={filteredData} />
        </CardContent>
      </Card>
    </div>
  );
};

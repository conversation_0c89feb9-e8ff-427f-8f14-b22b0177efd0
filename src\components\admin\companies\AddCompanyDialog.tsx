
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AddCompanyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  newCompany: {
    name: string;
    code: string;
    type: string;
  };
  onNewCompanyChange: (newCompany: { name: string; code: string; type: string }) => void;
  onAddCompany: () => void;
}

export const AddCompanyDialog = ({
  open,
  onOpenChange,
  newCompany,
  onNewCompanyChange,
  onAddCompany
}: AddCompanyDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle><PERSON><PERSON></DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Firma Adı</Label>
            <Input
              id="name"
              value={newCompany.name}
              onChange={(e) => onNewCompanyChange({...newCompany, name: e.target.value})}
              placeholder="Firma adını girin"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="code">Kurum Kodu</Label>
            <Input
              id="code"
              value={newCompany.code}
              onChange={(e) => onNewCompanyChange({...newCompany, code: e.target.value})}
              placeholder="Benzersiz kurum kodu girin"
            />
            <p className="text-xs text-muted-foreground">
              Bu kod, firma kullanıcılarının giriş yapması için gereklidir
            </p>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="type">Firma Tipi</Label>
            <select
              id="type"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={newCompany.type}
              onChange={(e) => onNewCompanyChange({...newCompany, type: e.target.value})}
            >
              <option value="KOBİ">KOBİ</option>
              <option value="Kurumsal">Kurumsal</option>
              <option value="Üretim">Üretim</option>
              <option value="Perakende">Perakende</option>
              <option value="Hizmet">Hizmet</option>
            </select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>İptal</Button>
          <Button onClick={onAddCompany}>Firma Ekle</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

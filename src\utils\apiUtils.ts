
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

// Retry ayarları
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // milisaniye

// Benzersiz işlem ID'si oluşturma
export const generateTransactionId = (): string => {
  return uuidv4();
};

// API isteği için wrapper fonksiyonu
export const safeApiCall = async <T>(
  apiCall: () => Promise<T>,
  options: {
    loadingMessage?: string;
    successMessage?: string;
    errorMessage?: string;
    retryEnabled?: boolean;
    transactionId?: string;
  } = {}
): Promise<{ data: T | null; error: any; loading: boolean; transactionId: string }> => {
  
  const transactionId = options.transactionId || generateTransactionId();
  
  const {
    loadingMessage = 'İşlem gerçekleştiriliyor...',
    successMessage,
    errorMessage = 'İşlem sırasında bir hata oluştu',
    retryEnabled = true
  } = options;

  if (loadingMessage) {
    toast.loading(loadingMessage, { id: transactionId });
  }

  let retries = 0;
  let data: T | null = null;
  let error: any = null;

  const executeApiCall = async (): Promise<void> => {
    try {
      // API çağrısını gerçekleştir
      data = await apiCall();
      
      // Başarılı olursa toast mesajı göster (eğer belirtilmişse)
      if (successMessage) {
        toast.success(successMessage, { id: transactionId });
      } else {
        toast.dismiss(transactionId);
      }
    } catch (err) {
      error = err;
      console.error(`İşlem hatası (ID: ${transactionId}):`, err);

      // Axios hatası ise daha detaylı bilgi al
      if (axios.isAxiosError(err)) {
        const axiosError = err as AxiosError;
        const statusCode = axiosError.response?.status;
        const responseData = axiosError.response?.data as any;
        
        // Veritabanı kısıtlama hataları için özel mesajlar
        if (statusCode === 409 || (responseData?.error && responseData.error.includes('duplicate'))) {
          toast.error('Bu işlem daha önce kaydedilmiş.', { id: transactionId });
        } else if (statusCode === 500) {
          toast.error('Sunucu hatası oluştu. Lütfen tekrar deneyin.', { id: transactionId });
        } else if (statusCode === 400) {
          toast.error(`Doğrulama hatası: ${responseData?.error || 'Geçersiz veri'}`, { id: transactionId });
        } else {
          toast.error(errorMessage, { id: transactionId });
        }
      } else {
        toast.error(errorMessage, { id: transactionId });
      }

      // Yeniden deneme mekanizması (eğer aktifse)
      if (retryEnabled && retries < MAX_RETRIES) {
        retries++;
        toast.loading(`İşlem yeniden deneniyor (${retries}/${MAX_RETRIES})...`, { id: transactionId });
        
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        await executeApiCall();
      }
    }
  };

  await executeApiCall();

  return { data, error, loading: false, transactionId };
};

// API çağrıları için genişletilmiş axios instance
export const apiClient = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// İstek yapılırken otomatik olarak transaction ID ekleyen interceptor
apiClient.interceptors.request.use((config) => {
  // Eğer transaction ID parametresi yoksa ekle
  if (!config.headers['X-Transaction-ID']) {
    config.headers['X-Transaction-ID'] = generateTransactionId();
  }
  return config;
});

// Güvenli HTTP metotları için yardımcı fonksiyonlar
export const safeGet = async <T>(
  url: string, 
  options?: { 
    config?: AxiosRequestConfig, 
    messages?: { loading?: string; success?: string; error?: string }, 
    retry?: boolean,
    transactionId?: string
  }
) => {
  return safeApiCall<T>(
    async () => {
      const response = await apiClient.get(url, options?.config);
      return response.data;
    },
    {
      loadingMessage: options?.messages?.loading,
      successMessage: options?.messages?.success,
      errorMessage: options?.messages?.error,
      retryEnabled: options?.retry,
      transactionId: options?.transactionId
    }
  );
};

export const safePost = async <T>(
  url: string,
  data: any,
  options?: { 
    config?: AxiosRequestConfig, 
    messages?: { loading?: string; success?: string; error?: string },
    retry?: boolean,
    transactionId?: string
  }
) => {
  return safeApiCall<T>(
    async () => {
      // Backend'e transactionId'yi gönder
      const config = {
        ...(options?.config || {}),
        headers: {
          ...(options?.config?.headers || {}),
          'X-Transaction-ID': options?.transactionId || generateTransactionId()
        }
      };
      
      const response = await apiClient.post(url, data, config);
      return response.data;
    },
    {
      loadingMessage: options?.messages?.loading || 'Kaydediliyor...',
      successMessage: options?.messages?.success || 'Başarıyla kaydedildi',
      errorMessage: options?.messages?.error || 'Kayıt sırasında bir hata oluştu',
      retryEnabled: options?.retry,
      transactionId: options?.transactionId
    }
  );
};

export const safePut = async <T>(
  url: string,
  data: any,
  options?: { 
    config?: AxiosRequestConfig, 
    messages?: { loading?: string; success?: string; error?: string },
    retry?: boolean,
    transactionId?: string
  }
) => {
  return safeApiCall<T>(
    async () => {
      // Backend'e transactionId'yi gönder
      const config = {
        ...(options?.config || {}),
        headers: {
          ...(options?.config?.headers || {}),
          'X-Transaction-ID': options?.transactionId || generateTransactionId()
        }
      };
      
      const response = await apiClient.put(url, data, config);
      return response.data;
    },
    {
      loadingMessage: options?.messages?.loading || 'Güncelleniyor...',
      successMessage: options?.messages?.success || 'Başarıyla güncellendi',
      errorMessage: options?.messages?.error || 'Güncelleme sırasında bir hata oluştu',
      retryEnabled: options?.retry,
      transactionId: options?.transactionId
    }
  );
};

export const safeDelete = async <T>(
  url: string,
  options?: { 
    config?: AxiosRequestConfig, 
    messages?: { loading?: string; success?: string; error?: string },
    retry?: boolean,
    transactionId?: string
  }
) => {
  return safeApiCall<T>(
    async () => {
      // Backend'e transactionId'yi gönder
      const config = {
        ...(options?.config || {}),
        headers: {
          ...(options?.config?.headers || {}),
          'X-Transaction-ID': options?.transactionId || generateTransactionId()
        }
      };
      
      const response = await apiClient.delete(url, config);
      return response.data;
    },
    {
      loadingMessage: options?.messages?.loading || 'Siliniyor...',
      successMessage: options?.messages?.success || 'Başarıyla silindi',
      errorMessage: options?.messages?.error || 'Silme işlemi sırasında bir hata oluştu',
      retryEnabled: options?.retry,
      transactionId: options?.transactionId
    }
  );
};

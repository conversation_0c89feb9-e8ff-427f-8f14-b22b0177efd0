
import React, { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Tabs, 
  Ta<PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format, subMonths } from "date-fns";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import {
  DownloadCloud,
  Calendar as CalendarIcon,
  Filter,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  FileSpreadsheet,
  FileText,
  BarChart2,
  TrendingUp,
  ArrowR<PERSON>,
} from "lucide-react";

// Sample data for charts
const monthlyData = [
  { name: "<PERSON><PERSON><PERSON>", gelir: 42500, gider: 25600, bakiye: 16900 },
  { name: "<PERSON><PERSON><PERSON>", gelir: 45200, gider: 28900, bakiye: 16300 },
  { name: "Mart", gelir: 48800, gider: 31200, bakiye: 17600 },
  { name: "Nisan", gelir: 53400, gider: 34500, bakiye: 18900 },
  { name: "Mayıs", gelir: 58900, gider: 42100, bakiye: 16800 },
  { name: "Haziran", gelir: 62300, gider: 39800, bakiye: 22500 },
];

const quarterlyData = [
  { name: "1. Çeyrek", gelir: 136500, gider: 85700, bakiye: 50800 },
  { name: "2. Çeyrek", gelir: 174600, gider: 116400, bakiye: 58200 },
];

const expenseCategories = [
  { name: "Personel", value: 35 },
  { name: "Hammadde", value: 25 },
  { name: "Operasyonel", value: 20 },
  { name: "Pazarlama", value: 12 },
  { name: "Diğer", value: 8 },
];

const incomeCategories = [
  { name: "Ürün Satışı", value: 65 },
  { name: "Hizmet Geliri", value: 25 },
  { name: "Kira Geliri", value: 5 },
  { name: "Diğer", value: 5 },
];

const transactions = [
  { id: 1, date: "10.06.2023", description: "Ürün Satışı #2587", amount: 12450, type: "gelir" },
  { id: 2, date: "08.06.2023", description: "Hammadde Alımı", amount: 8750, type: "gider" },
  { id: 3, date: "05.06.2023", description: "Personel Maaş Ödemesi", amount: 22500, type: "gider" },
  { id: 4, date: "01.06.2023", description: "Hizmet Geliri #1845", amount: 8500, type: "gelir" },
  { id: 5, date: "28.05.2023", description: "Kira Ödemesi", amount: 12000, type: "gider" },
  { id: 6, date: "25.05.2023", description: "Ürün Satışı #2574", amount: 9850, type: "gelir" },
  { id: 7, date: "20.05.2023", description: "Vergi Ödemesi", amount: 15600, type: "gider" },
  { id: 8, date: "15.05.2023", description: "Hizmet Geliri #1832", amount: 11200, type: "gelir" },
];

const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"];

export const FinancialDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [startDate, setStartDate] = useState<Date | undefined>(subMonths(new Date(), 6));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Finans Gösterge Paneli</h2>
          <p className="text-muted-foreground">
            Finansal durumunuzu anlık olarak takip edin ve analiz edin
          </p>
        </div>
        <div className="flex gap-2 flex-wrap">
          <Popover open={filterOpen} onOpenChange={setFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <CalendarIcon className="h-4 w-4" />
                {startDate && endDate ? (
                  <>
                    {format(startDate, "dd.MM.yyyy")} - {format(endDate, "dd.MM.yyyy")}
                  </>
                ) : (
                  "Tarih Seçin"
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="range"
                selected={{
                  from: startDate,
                  to: endDate,
                }}
                onSelect={(range) => {
                  setStartDate(range?.from);
                  setEndDate(range?.to);
                }}
                initialFocus
                className={cn("p-3 pointer-events-auto")}
              />
              <div className="p-3 border-t border-border flex justify-end gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setFilterOpen(false)}
                >
                  İptal
                </Button>
                <Button 
                  size="sm" 
                  onClick={() => setFilterOpen(false)}
                >
                  Uygula
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>

          <Button variant="outline" className="gap-2">
            <DownloadCloud className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Bakiye</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺245,762</div>
            <p className="text-xs text-muted-foreground">
              Tüm hesapların toplam bakiyesi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Gelir</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺62,300</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-green-600 font-medium">+5.4%</span>
              <span className="text-xs text-muted-foreground ml-1">
                geçen aya göre
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Gider</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺39,800</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-red-600 font-medium">-5.5%</span>
              <span className="text-xs text-muted-foreground ml-1">
                geçen aya göre
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Kâr Marjı</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">%36.1</div>
            <div className="flex items-center pt-1">
              <span className="text-xs text-green-600 font-medium">+2.1%</span>
              <span className="text-xs text-muted-foreground ml-1">
                geçen aya göre
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full md:w-auto grid-cols-3">
          <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
          <TabsTrigger value="income">Gelirler</TabsTrigger>
          <TabsTrigger value="expenses">Giderler</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Aylık Gelir-Gider Karşılaştırması</CardTitle>
              <CardDescription>Son 6 ayın karşılaştırmalı finansal verileri</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => `₺${value.toLocaleString()}`}
                    />
                    <Legend />
                    <Bar dataKey="gelir" name="Gelir" fill="#4f46e5" />
                    <Bar dataKey="gider" name="Gider" fill="#ef4444" />
                    <Bar dataKey="bakiye" name="Net Bakiye" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>İşlem Geçmişi</CardTitle>
                <CardDescription>Son finansal işlemler</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        Tümü
                      </Button>
                      <Button variant="ghost" size="sm">
                        Gelirler
                      </Button>
                      <Button variant="ghost" size="sm">
                        Giderler
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <FileSpreadsheet className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <FileText className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="rounded-md border">
                    <div className="relative max-h-[300px] w-full overflow-auto">
                      <table className="w-full caption-bottom text-sm">
                        <thead className="[&_tr]:border-b">
                          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                              Tarih
                            </th>
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                              Açıklama
                            </th>
                            <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
                              Tutar
                            </th>
                          </tr>
                        </thead>
                        <tbody className="[&_tr:last-child]:border-0">
                          {transactions.map((transaction) => (
                            <tr
                              key={transaction.id}
                              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                            >
                              <td className="p-4 align-middle">
                                {transaction.date}
                              </td>
                              <td className="p-4 align-middle">
                                {transaction.description}
                              </td>
                              <td
                                className={`p-4 align-middle text-right font-medium ${
                                  transaction.type === "gelir"
                                    ? "text-green-600"
                                    : "text-red-600"
                                }`}
                              >
                                {transaction.type === "gelir" ? "+" : "-"}₺
                                {transaction.amount.toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div className="flex justify-center">
                    <Button variant="outline" className="w-full gap-2">
                      Daha Fazla Göster
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Finansal Özet</CardTitle>
                <CardDescription>Kategorik dağılımlar ve analizler</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="gider" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="gider">Gider Dağılımı</TabsTrigger>
                    <TabsTrigger value="gelir">Gelir Dağılımı</TabsTrigger>
                  </TabsList>
                  <TabsContent value="gider" className="space-y-4">
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={expenseCategories}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: %${(percent * 100).toFixed(0)}`
                            }
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {expenseCategories.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={COLORS[index % COLORS.length]}
                              />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value) => [`%${value}`, "Oran"]}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-2">
                      {expenseCategories.map((category, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ background: COLORS[index % COLORS.length] }}
                          ></div>
                          <div className="flex justify-between w-full">
                            <span className="text-sm">{category.name}</span>
                            <span className="text-sm font-medium">
                              %{category.value}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="gelir" className="space-y-4">
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={incomeCategories}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: %${(percent * 100).toFixed(0)}`
                            }
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {incomeCategories.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={COLORS[index % COLORS.length]}
                              />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value) => [`%${value}`, "Oran"]}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-2">
                      {incomeCategories.map((category, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ background: COLORS[index % COLORS.length] }}
                          ></div>
                          <div className="flex justify-between w-full">
                            <span className="text-sm">{category.name}</span>
                            <span className="text-sm font-medium">
                              %{category.value}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="income" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gelir Trendi</CardTitle>
              <CardDescription>Aylara göre gelir değişimi</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [`₺${value.toLocaleString()}`, "Gelir"]}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="gelir"
                      name="Gelir"
                      stroke="#4f46e5"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Gelir Kaynakları</CardTitle>
                <CardDescription>Ana gelir kaynaklarının dağılımı</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={incomeCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) =>
                          `${name}: %${(percent * 100).toFixed(0)}`
                        }
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {incomeCategories.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`%${value}`, "Oran"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Çeyreklik Karşılaştırma</CardTitle>
                <CardDescription>Çeyreklere göre gelir değişimi</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={quarterlyData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) => [`₺${value.toLocaleString()}`]}
                      />
                      <Legend />
                      <Bar dataKey="gelir" name="Gelir" fill="#4f46e5" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Gider Trendi</CardTitle>
              <CardDescription>Aylara göre gider değişimi</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [`₺${value.toLocaleString()}`, "Gider"]}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="gider"
                      name="Gider"
                      stroke="#ef4444"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Gider Kategorileri</CardTitle>
                <CardDescription>Ana gider kategorilerinin dağılımı</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={expenseCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) =>
                          `${name}: %${(percent * 100).toFixed(0)}`
                        }
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {expenseCategories.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`%${value}`, "Oran"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Çeyreklik Karşılaştırma</CardTitle>
                <CardDescription>Çeyreklere göre gider değişimi</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={quarterlyData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) => [`₺${value.toLocaleString()}`]}
                      />
                      <Legend />
                      <Bar dataKey="gider" name="Gider" fill="#ef4444" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};


import { AlertCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface TaxIdentificationFieldProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export const TaxIdentificationField = ({ value, onChange, error }: TaxIdentificationFieldProps) => {
  return (
    <div className="space-y-1">
      <Label htmlFor="vknTckn" className="font-medium text-sm flex items-center gap-1">
        VKN / TCKN <span className="text-red-500">*</span>
      </Label>
      <Input 
        id="vknTckn" 
        placeholder="Vergi Kimlik No veya TC Kimlik No" 
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        className="border-primary/20 focus-visible:ring-primary h-8 text-sm"
      />
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
};

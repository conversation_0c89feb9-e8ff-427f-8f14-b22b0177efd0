
import { FileText, Receipt, CreditCard, Wallet, BarChart3, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";

interface FinanceSubMenuItemProps {
  icon: string;
  label: string;
  path: string;
  color: string;
}

export const FinanceSubMenuItem = ({ icon, label, path, color }: FinanceSubMenuItemProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const isActive = location.pathname + location.search === path;

  const renderIcon = () => {
    switch (icon) {
      case "FileText":
        return <FileText className={`h-4 w-4 ${color}`} />;
      case "Receipt":
        return <Receipt className={`h-4 w-4 ${color}`} />;
      case "CreditCard":
        return <CreditCard className={`h-4 w-4 ${color}`} />;
      case "Wallet":
        return <Wallet className={`h-4 w-4 ${color}`} />;
      case "BarChart3":
        return <BarChart3 className={`h-4 w-4 ${color}`} />;
      case "TrendingUp":
        return <TrendingUp className={`h-4 w-4 ${color}`} />;
      default:
        return null;
    }
  };

  return (
    <Button
      variant="ghost"
      className={`w-full justify-start gap-2 ${isActive ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-200' : ''}`}
      onClick={() => navigate(path)}
    >
      {renderIcon()}
      {label}
    </Button>
  );
};


import { ReactNode, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: "admin" | "company_admin" | "user" | null;
  requiredModule?: string | null;
}

const ProtectedRoute = ({ 
  children, 
  requiredRole = null,
  requiredModule = null
}: ProtectedRouteProps) => {
  const { isAuthenticated, user, company } = useAuth();
  const location = useLocation();
  const { toast } = useToast();

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Role-based access check
  if (requiredRole && user) {
    if (requiredRole === "admin" && user.role !== "Admin") {
      toast({
        title: "<PERSON><PERSON><PERSON>",
        description: "Bu sayfaya erişmek için admin yetkisine sahip olmanız gerekmektedir.",
        variant: "destructive",
      });
      return <Navigate to="/" replace />;
    }
    
    if (requiredRole === "company_admin" && user.role !== "Müdür") {
      toast({
        title: "Yetkisiz Erişim",
        description: "Bu sayfaya erişmek için şirket yöneticisi olmanız gerekmektedir.",
        variant: "destructive",
      });
      return <Navigate to="/" replace />;
    }
  }

  // Module-based access check
  if (requiredModule && user && company && user.role !== "Admin") {
    const hasModuleAccess = 
      company.modules.includes(requiredModule) || 
      company.optionalModules.includes(requiredModule);
    
    const userHasPermission = 
      user.permissions.includes(requiredModule) || 
      user.permissions.includes("all");

    if (!hasModuleAccess || !userHasPermission) {
      toast({
        title: "Yetkisiz Erişim",
        description: `Bu modüle erişim yetkiniz bulunmamaktadır: ${requiredModule}`,
        variant: "destructive",
      });
      return <Navigate to="/" replace />;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
};

export default ProtectedRoute;

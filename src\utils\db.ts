
import mysql from 'mysql2/promise';
import 'dotenv/config';

// Veritabanı bağlantı bilgileri
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'finans_db',
  port: Number(process.env.DB_PORT) || 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Bağlantı havuzu oluştur
const pool = mysql.createPool(dbConfig);

// Bağlantıyı test et
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('Veritabanı bağlantısı başarılı!');
    connection.release();
    return true;
  } catch (error) {
    console.error('Veritabanı bağlantı hatası:', error);
    return false;
  }
};

// Sorgu çalıştırmak için yardımcı fonksiyon
const query = async (sql: string, params?: any[]) => {
  try {
    const [results] = await pool.execute(sql, params);
    return results;
  } catch (error) {
    console.error('Sorgu hatası:', error);
    throw error;
  }
};

export { pool, query, testConnection };

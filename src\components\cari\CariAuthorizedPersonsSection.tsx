
import { Button } from "@/components/ui/button";
import { User } from "lucide-react";

export const CariAuthorizedPersonsSection = () => {
  const addAuthorizedPerson = () => {
    console.log("Yetkili kişi ekleniyor");
    // Burada yetkililer listesine yeni bir kişi eklenecek
  };

  return (
    <div className="space-y-4">
      <h4 className="text-md font-medium">Yetki<PERSON> Kişi Bilgileri</h4>
      
      <div className="border rounded-md p-4 bg-muted/5">
        <div className="grid grid-cols-4 gap-4 mb-2 font-medium text-sm border-b pb-2">
          <div>Yetki<PERSON>inin Adı</div>
          <div>E-posta</div>
          <div>Telefon</div>
          <div>Notlar</div>
        </div>
        
        <div className="text-center py-8 text-muted-foreground text-sm">
          Hen<PERSON>z yetkili kişi eklen<PERSON>
        </div>
        
        <Button variant="outline" size="sm" className="mt-2" onClick={addAuthorizedPerson}>
          <User className="h-4 w-4 mr-1" /> Yetkili Ekle
        </Button>
      </div>
    </div>
  );
};

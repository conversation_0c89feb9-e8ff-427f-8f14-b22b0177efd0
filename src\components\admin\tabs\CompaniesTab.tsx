
import { Company } from "../types";
import { CompanyListHeader } from "../companies/CompanyListHeader";
import { CompanyList } from "../companies/CompanyList";
import { AddCompanyDialog } from "../companies/AddCompanyDialog";
import { EditCompanyDialog } from "../dialogs/EditCompanyDialog";
import { useCompanies } from "../companies/useCompanies";

interface CompaniesTabProps {
  companies: Company[];
  setCompanies: (companies: Company[]) => void;
}

export const CompaniesTab = ({ companies, setCompanies }: CompaniesTabProps) => {
  const {
    showAddCompanyDialog,
    setShowAddCompanyDialog,
    showEditCompanyDialog,
    setShowEditCompanyDialog,
    selectedCompany,
    newCompany,
    setNewCompany,
    toggleCompanyStatus,
    handleAddCompany,
    handleEditCompany,
    handleSaveCompany,
    handleResetPassword
  } = useCompanies(companies, setCompanies);

  return (
    <>
      <CompanyListHeader onAddCompany={() => setShowAddCompanyDialog(true)} />
      
      <CompanyList 
        companies={companies}
        onToggleStatus={toggleCompanyStatus}
        onEdit={handleEditCompany}
      />

      {/* Add Company Dialog */}
      <AddCompanyDialog
        open={showAddCompanyDialog}
        onOpenChange={setShowAddCompanyDialog}
        newCompany={newCompany}
        onNewCompanyChange={setNewCompany}
        onAddCompany={handleAddCompany}
      />

      {/* Edit Company Dialog */}
      {selectedCompany && (
        <EditCompanyDialog
          company={selectedCompany}
          open={showEditCompanyDialog}
          onOpenChange={setShowEditCompanyDialog}
          onSave={handleSaveCompany}
          onResetPassword={handleResetPassword}
        />
      )}
    </>
  );
};


import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Company } from "../types";

interface EditCompanyDialogProps {
  company: Company | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (updatedCompany: Company) => void;
  onResetPassword?: (companyId: number) => void;
}

export function EditCompanyDialog({
  company,
  open,
  onOpenChange,
  onSave,
  onResetPassword
}: EditCompanyDialogProps) {
  const { toast } = useToast();
  const [editedCompany, setEditedCompany] = useState<Company | null>(company);

  // Reset form when dialog opens with new company
  if (company && company.id !== editedCompany?.id) {
    setEditedCompany(company);
  }

  if (!editedCompany) return null;

  const handleSave = () => {
    if (!editedCompany.name || !editedCompany.code) {
      toast({
        title: "Eksik Bilgi",
        description: "Firma adı ve kurum kodu alanları gereklidir",
        variant: "destructive",
      });
      return;
    }

    onSave(editedCompany);
    toast({
      title: "Firma Güncellendi",
      description: "Firma bilgileri başarıyla güncellendi",
    });
  };

  const handleResetPassword = () => {
    if (onResetPassword) {
      onResetPassword(editedCompany.id);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Firma Düzenle - {editedCompany.name}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="general">Genel Bilgiler</TabsTrigger>
            <TabsTrigger value="contact">İletişim Bilgileri</TabsTrigger>
            <TabsTrigger value="tax">Vergi Bilgileri</TabsTrigger>
            <TabsTrigger value="subscription">Abonelik Bilgileri</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Firma Adı</Label>
                <Input
                  id="name"
                  value={editedCompany.name}
                  onChange={(e) => setEditedCompany({...editedCompany, name: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Kurum Kodu</Label>
                <Input
                  id="code"
                  value={editedCompany.code}
                  onChange={(e) => setEditedCompany({...editedCompany, code: e.target.value})}
                />
                <p className="text-xs text-muted-foreground">Firma kullanıcılarının giriş yapması için gerekli kod</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Firma Tipi</Label>
                <select
                  id="type"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={editedCompany.type}
                  onChange={(e) => setEditedCompany({...editedCompany, type: e.target.value})}
                >
                  <option value="KOBİ">KOBİ</option>
                  <option value="Kurumsal">Kurumsal</option>
                  <option value="Üretim">Üretim</option>
                  <option value="Perakende">Perakende</option>
                  <option value="Hizmet">Hizmet</option>
                </select>
              </div>
              <div className="space-y-2 flex items-end">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={handleResetPassword}
                >
                  Şifre Sıfırlama Gönder
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="contact" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">E-posta Adresi</Label>
                <Input
                  id="email"
                  type="email"
                  value={editedCompany.contactInfo?.email || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    contactInfo: {
                      ...editedCompany.contactInfo || {},
                      email: e.target.value
                    }
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Telefon</Label>
                <Input
                  id="phone"
                  value={editedCompany.contactInfo?.phone || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    contactInfo: {
                      ...editedCompany.contactInfo || {},
                      phone: e.target.value
                    }
                  })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Adres</Label>
              <Input
                id="address"
                value={editedCompany.contactInfo?.address || ""}
                onChange={(e) => setEditedCompany({
                  ...editedCompany, 
                  contactInfo: {
                    ...editedCompany.contactInfo || {},
                    address: e.target.value
                  }
                })}
              />
            </div>
          </TabsContent>

          <TabsContent value="tax" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="taxOffice">Vergi Dairesi</Label>
                <Input
                  id="taxOffice"
                  value={editedCompany.taxInfo?.taxOffice || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    taxInfo: {
                      ...editedCompany.taxInfo || {},
                      taxOffice: e.target.value
                    }
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="taxNumber">Vergi Numarası</Label>
                <Input
                  id="taxNumber"
                  value={editedCompany.taxInfo?.taxNumber || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    taxInfo: {
                      ...editedCompany.taxInfo || {},
                      taxNumber: e.target.value
                    }
                  })}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="subscription" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="plan">Abonelik Planı</Label>
                <select
                  id="plan"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={editedCompany.subscription?.plan || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    subscription: {
                      ...editedCompany.subscription || {},
                      plan: e.target.value
                    }
                  })}
                >
                  <option value="">Plan Seçiniz</option>
                  <option value="Başlangıç">Başlangıç</option>
                  <option value="Profesyonel">Profesyonel</option>
                  <option value="Kurumsal">Kurumsal</option>
                  <option value="Enterprise">Enterprise</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Abonelik Durumu</Label>
                <select
                  id="status"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={editedCompany.subscription?.status || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    subscription: {
                      ...editedCompany.subscription || {},
                      status: e.target.value
                    }
                  })}
                >
                  <option value="">Durum Seçiniz</option>
                  <option value="Aktif">Aktif</option>
                  <option value="Deneme">Deneme</option>
                  <option value="Fatura Bekleniyor">Fatura Bekleniyor</option>
                  <option value="Süresi Dolmuş">Süresi Dolmuş</option>
                  <option value="İptal Edildi">İptal Edildi</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nextBillingDate">Sonraki Fatura Tarihi</Label>
                <Input
                  id="nextBillingDate"
                  type="date"
                  value={editedCompany.subscription?.nextBillingDate || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    subscription: {
                      ...editedCompany.subscription || {},
                      nextBillingDate: e.target.value
                    }
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="amount">Aylık Tutar (₺)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={editedCompany.subscription?.amount || ""}
                  onChange={(e) => setEditedCompany({
                    ...editedCompany, 
                    subscription: {
                      ...editedCompany.subscription || {},
                      amount: parseFloat(e.target.value) || 0
                    }
                  })}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>İptal</Button>
          <Button onClick={handleSave}>Kaydet</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

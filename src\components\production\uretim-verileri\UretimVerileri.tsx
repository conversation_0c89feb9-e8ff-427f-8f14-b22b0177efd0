import React, { useState, useEffect, useRef, useCallback } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ApiService } from '@/services/apiService';
import { SiloData } from '@/models/SiloData';
import { UrunModel } from '@/models/UrunModel';
import { DataCalculations } from '@/utils/DataCalculations';
import { toast } from "@/hooks/use-toast";
import { useProductionLine, type ProductionLine } from "@/contexts/ProductionLineContext";
import TarihFiltreleri from './TarihFiltreleri';
import BoruCapiFiltreleri from './BoruCapiFiltreleri';
import UretimOzeti from './UretimOzeti';
import UretimVerileriTablosu from './UretimVerileriTablosu';
import AyarlarTab from './AyarlarTab';
import UrunDonusum from './UrunDonusum';
import { Button } from "@/components/ui/button";
import { RefreshCw, AlertTriangle } from "lucide-react";

// Props tipini tanımla
interface UretimVerileriProps {
    currentLine: ProductionLine;
}

const UretimVerileri: React.FC<UretimVerileriProps> = ({ currentLine }) => {
    const [siloDataList, setSiloDataList] = useState<SiloData[]>([]);
    const [filtrelenmisVeriler, setFiltrelenmisVeriler] = useState<SiloData[]>([]);
    const [urunler, setUrunler] = useState<UrunModel[]>([]);
    const [urunAdlari, setUrunAdlari] = useState<Record<string, string>>({});
    const [loadingData, setLoadingData] = useState(false);
    const [isConnected, setIsConnected] = useState(true);
    const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
    const [activeTab, setActiveTab] = useState("uretim-verileri");
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const previousDataRef = useRef<string>(''); // Veri karşılaştırması için

    // Filtre değişkenleri
    const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
    const [endDate, setEndDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
    const [selectedDiameters, setSelectedDiameters] = useState<Record<string, boolean>>({
        "80": false,
        "100": false,
        "125": false,
        "160": false,
        "200": false
    });
    const [disabledDiameters, setDisabledDiameters] = useState<string[]>([]);
    const [dateFilterType, setDateFilterType] = useState<'tekGun' | 'aralik'>('tekGun');
    const [selectedBoruTipi, setSelectedBoruTipi] = useState('');
    const [selectedAltUrun, setSelectedAltUrun] = useState('');

    // Context'ten alınan mevcut hat değeri (tema renkleri için kullanıyoruz)
    const { getLineThemeColor, getLineAccentColor } = useProductionLine();

    useEffect(() => {
        // Önce eski timer'ı temizle
        if (timerRef.current) {
            console.log("Mevcut timer temizleniyor (hat değişimi sebebiyle)");
            clearInterval(timerRef.current);
            timerRef.current = null;
        }

        // Initial data fetch
        console.log(`${currentLine} hattı için ilk veri çekme işlemi başlatılıyor`);
        fetchData();

        // Set up continuous data fetching (every 15 seconds)
        timerRef.current = setInterval(() => {
            console.log(`${currentLine} hattı için otomatik veri çekme işlemi çalıştırıldı`);
            fetchData(true); // Silent update
        }, 15000); // 15 seconds

        return () => {
            // Clear interval when component unmounts or dependencies change
            if (timerRef.current) {
                console.log("Timer temizleniyor");
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [currentLine]); // Hat prop'u değiştiğinde yeniden veri çek

    // Filtre değişikliklerinde otomatik olarak filtreleri uygula
    useEffect(() => {
        if (siloDataList.length > 0) {
            applyFilters();
        }
    }, [selectedDate, endDate, dateFilterType, selectedDiameters]);

    useEffect(() => {
        updateDisabledDiameters();
    }, [siloDataList, dateFilterType, selectedDate, endDate]);

    const fetchData = async (isSilentUpdate = false) => {
        if (isSilentUpdate) {
            // Sessiz yenileme modunda yükleme göstergesini etkinleştirme
            setLoadingData(false);
        } else {
            setLoadingData(true);
        }

        try {
            console.log("Veri çekme başladı - API URL:", `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}`);
            console.log("Aktif üretim hattı (prop):", currentLine); // Aktif hattı kontrol et

            // Aktif üretim hattına göre uygun API metodunu seç
            let fetchSiloDataFunction;
            let hatName = "";

            switch (currentLine) {
                case "line1":
                    console.log("Hat 1 fonksiyonu seçildi - ApiService.fetchSiloData");
                    fetchSiloDataFunction = ApiService.fetchSiloData;
                    hatName = "Hat 1";
                    break;
                case "line2":
                    console.log("Hat 2 fonksiyonu seçildi - ApiService.fetchSiloData2");
                    fetchSiloDataFunction = ApiService.fetchSiloData2;
                    hatName = "Hat 2";
                    break;
                case "line3":
                    console.log("Hat 3 fonksiyonu seçildi - ApiService.fetchSiloData3");
                    fetchSiloDataFunction = ApiService.fetchSiloData3;
                    hatName = "Hat 3";
                    break;
                case "line4":
                    console.log("Hat 4 fonksiyonu seçildi - ApiService.fetchSiloData4");
                    fetchSiloDataFunction = ApiService.fetchSiloData4;
                    hatName = "Hat 4";
                    break;
                case "line5":
                    console.log("Hat 5 fonksiyonu seçildi - ApiService.fetchSiloData5");
                    fetchSiloDataFunction = ApiService.fetchSiloData5;
                    hatName = "Hat 5";
                    break;
                default:
                    console.log("Varsayılan Hat 1 fonksiyonu seçildi (varsayılan) - ApiService.fetchSiloData");
                    fetchSiloDataFunction = ApiService.fetchSiloData;
                    hatName = "Hat 1 (varsayılan)";
            }

            // Fetch all data in parallel
            console.log(`${hatName} için fetchSiloDataFunction() çağrılıyor...`);
            const [siloData, urunlerData] = await Promise.all([
                fetchSiloDataFunction(), // Aktif hatta göre veri çek
                ApiService.fetchUrunler(),
            ]);

            console.log(`${hatName} (${currentLine}) için veri çekme başarılı:`, siloData.length, "üretim verisi,", urunlerData.length, "ürün verisi alındı");

            // Yeni veri kontrolü (sadece siloData'yı kontrol et)
            const newDataHash = JSON.stringify(siloData.map(item => item.id));
            const hasNewData = newDataHash !== previousDataRef.current;

            // Yeni veri yoksa ve sessiz yenileme modundaysa, yenilemeyi at
            if (!hasNewData && isSilentUpdate) {
                console.log("Yeni veri yok, güncelleme yapılmadı");
                return;
            }

            // Yeni veri varsa veya zorla yenileme istendiyse devam et
            previousDataRef.current = newDataHash;
            setLastUpdated(new Date());

            // Sort by date (newest first)
            siloData.sort((a, b) => b.tarih.getTime() - a.tarih.getTime());

            // Sadece en son 200 veriyi al
            const limitedSiloData = siloData.slice(0, 200);

            // Düzeltme: Saat bilgisini değiştirmeyin, veritabanındaki gibi gösterin
            const correctedData = limitedSiloData.map(item => {
                // Veritabanındaki tarih ve saat bilgisini olduğu gibi kullanma
                const tarihStr = item.tarih instanceof Date ? item.tarih.toISOString() : item.tarih;
                const correctedDate = new Date(tarihStr);

                // Çift Ø sorunu kontrolü ve düzeltmesi
                let fixedBoruAd = item.boruAd;
                if (fixedBoruAd && typeof fixedBoruAd === 'string') {
                    const duplicatePattern = /Ø\s*(\d+)\s*Ø\s*\1/;
                    if (duplicatePattern.test(fixedBoruAd)) {
                        fixedBoruAd = fixedBoruAd.replace(duplicatePattern, 'Ø $1');
                    }
                }

                return {
                    ...item,
                    tarih: correctedDate,
                    boruAd: fixedBoruAd,
                    saat: item.saat
                };
            });

            setSiloDataList(correctedData);
            setUrunler(urunlerData);

            // Create a mapping of stock codes to product names
            const urunAdlariMap: Record<string, string> = {};
            urunlerData.forEach(urun => {
                urunAdlariMap[urun.stokKodu] = urun.stokAdi;
            });
            setUrunAdlari(urunAdlariMap);

            // En son üretim yapılan güne ait verileri göster
            const latestDateData = DataCalculations.getLatestDateData(correctedData);
            setFiltrelenmisVeriler(latestDateData);

            setIsConnected(true);

            if (!isSilentUpdate) {
                toast({
                    title: "Veriler güncellendi",
                    description: `${correctedData.length} üretim verisi başarıyla yüklendi.`,
                });
            }
        } catch (error) {
            console.error('Veri yükleme hatası:', error);
            setIsConnected(false);

            toast({
                variant: "destructive",
                title: "Veri çekme hatası",
                description: "Sunucudan veriler alınırken bir hata oluştu. Lütfen bağlantınızı kontrol edin.",
            });
        } finally {
            if (!isSilentUpdate) {
                setLoadingData(false);
            }
        }
    };

    const getUrunAdi = (stokKodu: string): string => {
        return urunAdlari[stokKodu] || 'Bilinmeyen';
    };

    const getUrunCap = (stokKodu: string): string => {
        // Önce stok kodundan çapı bulmaya çalış
        let cap = DataCalculations.getUrunCap(stokKodu);

        // Eğer bulunamadıysa, stok adından çapı bulmaya çalış
        if (!cap) {
            const urunAdi = getUrunAdi(stokKodu);
            cap = DataCalculations.getUrunCapFromStokAdi ?
                DataCalculations.getUrunCapFromStokAdi(urunAdi) : '';
        }

        return cap;
    };

    // Format boru adı - Çift Ø sembolü düzeltmesi
    const formatBoruAdi = (item: SiloData): string => {
        if (item.boruAd && typeof item.boruAd === 'string') {
            // Tekrarlanan çap gösterimi sorununu düzelt
            const duplicatePattern = /Ø\s*(\d+)\s*Ø\s*\1/;
            if (duplicatePattern.test(item.boruAd)) {
                return item.boruAd.replace(duplicatePattern, 'Ø $1');
            }
            return item.boruAd;
        }

        const cap = getUrunCap(item.stokKodu);
        const urunAdi = getUrunAdi(item.stokKodu);

        // Ürün adı içinde zaten çap bilgisi varsa başa tekrar ekleme
        if (urunAdi.includes(cap)) {
            return `Ø ${cap} ${urunAdi.replace(/^\d+\s*/, '')}`;
        }
        return `Ø ${cap} ${urunAdi}`;
    };

    const updateDisabledDiameters = useCallback(() => {
        // Sadece tarih filtrelemesi sonucu oluşan verilere göre çapları belirleyelim
        let dateFiltered = [...siloDataList];

        if (dateFilterType === 'tekGun' && selectedDate) {
            const filterDate = new Date(selectedDate);
            dateFiltered = DataCalculations.filterDataByDateRange(
                dateFiltered,
                filterDate,
                filterDate
            );
        } else if (dateFilterType === 'aralik' && selectedDate && endDate) {
            const startDate = new Date(selectedDate);
            const finishDate = new Date(endDate);

            dateFiltered = DataCalculations.filterDataByDateRange(
                dateFiltered,
                startDate,
                finishDate
            );
        }

        // Tarih filtresine göre mevcut çapları belirle
        const availableDiameters = new Set<string>();
        dateFiltered.forEach(item => {
            const cap = getUrunCap(item.stokKodu);
            if (cap) {
                availableDiameters.add(cap);
            }
        });

        // Tarih filtresine göre mevcut olmayan çapları devre dışı bırak
        const disabled = Object.keys(selectedDiameters).filter(diam => !availableDiameters.has(diam));
        setDisabledDiameters(disabled);
    }, [siloDataList, dateFilterType, selectedDate, endDate, selectedDiameters]);

    const applyFilters = useCallback(() => {
        if (siloDataList.length === 0) return;

        let filtered = [...siloDataList];

        // Apply date filter
        if (dateFilterType === 'tekGun' && selectedDate) {
            const filterDate = new Date(selectedDate);
            filtered = DataCalculations.filterDataByDateRange(
                filtered,
                filterDate,
                filterDate
            );
        } else if (dateFilterType === 'aralik' && selectedDate && endDate) {
            const startDate = new Date(selectedDate);
            const finishDate = new Date(endDate);

            filtered = DataCalculations.filterDataByDateRange(
                filtered,
                startDate,
                finishDate
            );
        }

        // Apply diameter filter if any selected
        const selectedDiametersArray = Object.entries(selectedDiameters)
            .filter(([_, isSelected]) => isSelected)
            .map(([diameter]) => diameter);

        if (selectedDiametersArray.length > 0) {
            filtered = DataCalculations.filterDataByDiameter(filtered, selectedDiametersArray, getUrunCap);
        }

        setFiltrelenmisVeriler(filtered);
    }, [siloDataList, dateFilterType, selectedDate, endDate, selectedDiameters]);

    const resetFilters = () => {
        setSelectedDiameters({
            "80": false,
            "100": false,
            "125": false,
            "160": false,
            "200": false
        });
        setSelectedDate(format(new Date(), 'yyyy-MM-dd'));
        setEndDate(format(new Date(), 'yyyy-MM-dd'));
        setDateFilterType('tekGun');

        // Reset to latest day's data
        if (siloDataList.length > 0) {
            const latestDateData = DataCalculations.getLatestDateData(siloDataList);
            setFiltrelenmisVeriler(latestDateData);
        }
    };

    const handleDateFilterTypeChange = (type: 'tekGun' | 'aralik') => {
        setDateFilterType(type);
    };

    const handleDateChange = (date: string) => {
        setSelectedDate(date);
    };

    const handleEndDateChange = (date: string) => {
        setEndDate(date);
    };

    const handleDiameterChange = (diameter: string, checked: boolean) => {
        setSelectedDiameters(prev => ({
            ...prev,
            [diameter]: checked
        }));
    };

    // Manual refresh function for any future uses
    const handleManualRefresh = () => {
        fetchData(false);
    };

    // Grup istatistiklerini hesapla
    const groupedData = DataCalculations.groupDataByProductType(filtrelenmisVeriler);
    const groupStats = DataCalculations.calculateGroupStats(groupedData);

    const handleBoruTipiChange = (tip: string) => {
        setSelectedBoruTipi(tip);
    };

    const handleAltUrunChange = (altUrun: string) => {
        setSelectedAltUrun(altUrun);
    };

    const handleSaveStokKodu = async () => {
        if (!selectedAltUrun) {
            toast({
                title: "Hata",
                description: "Lütfen bir stok kodu seçiniz",
                variant: "destructive"
            });
            return;
        }

        try {
            // Stok kodunu kaydet
            await ApiService.saveStokKodu(selectedAltUrun);

            toast({
                title: "Başarılı",
                description: "Stok kodu başarıyla kaydedildi",
            });

            // Verileri yenile
            fetchData(false);

            // Seçimleri sıfırla
            setSelectedBoruTipi("");
            setSelectedAltUrun("");
        } catch (error) {
            console.error("Stok kodu kaydedilirken hata oluştu:", error);
            toast({
                title: "Hata",
                description: "Stok kodu kaydedilemedi",
                variant: "destructive"
            });
        }
    };

    const handleUpdateSiloData = async (id: number, newStokKodu: string) => {
        try {
            // Aktif hatta göre uygun güncelleme metodunu seçme
            let updateFunction;

            switch (currentLine) {
                case "line1":
                    updateFunction = ApiService.updateSiloData;
                    break;
                case "line2":
                    updateFunction = ApiService.updateSiloData2;
                    break;
                case "line3":
                    updateFunction = ApiService.updateSiloData3;
                    break;
                case "line4":
                    updateFunction = ApiService.updateSiloData4;
                    break;
                case "line5":
                    updateFunction = ApiService.updateSiloData5;
                    break;
                default:
                    updateFunction = ApiService.updateSiloData;
            }

            await updateFunction(id, newStokKodu);

            toast({
                title: "Başarılı",
                description: `Hat ${currentLine.replace('line', '')} - Kayıt başarıyla güncellendi`,
            });

            // Verileri yenile
            fetchData(false);
        } catch (error) {
            console.error("Kayıt güncellenirken hata oluştu:", error);
            toast({
                title: "Hata",
                description: "Kayıt güncellenemedi",
                variant: "destructive"
            });
        }
    };

    const handleDeleteSiloData = async (id: number) => {
        try {
            // Aktif hatta göre uygun silme metodunu seçme
            let deleteFunction;

            switch (currentLine) {
                case "line1":
                    deleteFunction = ApiService.deleteSiloData;
                    break;
                case "line2":
                    deleteFunction = ApiService.deleteSiloData2;
                    break;
                case "line3":
                    deleteFunction = ApiService.deleteSiloData3;
                    break;
                case "line4":
                    deleteFunction = ApiService.deleteSiloData4;
                    break;
                case "line5":
                    deleteFunction = ApiService.deleteSiloData5;
                    break;
                default:
                    deleteFunction = ApiService.deleteSiloData;
            }

            await deleteFunction(id);

            toast({
                title: "Başarılı",
                description: `Hat ${currentLine.replace('line', '')} - Kayıt başarıyla silindi`,
            });

            // Verileri yenile
            fetchData(false);
        } catch (error) {
            console.error("Kayıt silinirken hata oluştu:", error);
            toast({
                title: "Hata",
                description: "Kayıt silinemedi",
                variant: "destructive"
            });
        }
    };

    // KEÇELİ DÖNÜŞÜM tespit sistemi
    const detectKeceliDonusum = () => {
        // KEÇELİ DÖNÜŞÜM stok kodları ve karşılık gelen DELİKLİ BORU kodları
        const donusumMapping = {
            "MRZ-ST10663": { donusum: "Ø 80 KEÇELİ DÖNÜŞÜM", delikli: "Ø 80 DELİKLİ BORU", delikliKod: "MRZ-ST00661" },
            "MRZ-ST12549": { donusum: "Ø 100 KEÇELİ DÖNÜŞÜM", delikli: "Ø 100 DELİKLİ BORU", delikliKod: "MGS0055" },
            "MRZ-ST10666": { donusum: "Ø 125 KEÇELİ DÖNÜŞÜM", delikli: "Ø 125 DELİKLİ BORU", delikliKod: "MRZ-ST00665" },
            "MRZ-ST12550": { donusum: "Ø 160 KEÇELİ DÖNÜŞÜM", delikli: "Ø 160 DELİKLİ BORU", delikliKod: "MGS0053" },
            "MRZ-ST12551": { donusum: "Ø 200 KEÇELİ DÖNÜŞÜM", delikli: "Ø 200 DELİKLİ BORU", delikliKod: "MGS0056" }
        };

        // Filtrelenmiş verilerde KEÇELİ DÖNÜŞÜM sayılarını hesapla
        const donusumCounts: Record<string, number> = {};

        filtrelenmisVeriler.forEach(item => {
            if (donusumMapping[item.stokKodu]) {
                donusumCounts[item.stokKodu] = (donusumCounts[item.stokKodu] || 0) + 1;
            }
        });

        // Mesajları oluştur
        const messages: string[] = [];
        Object.entries(donusumCounts).forEach(([stokKodu, count]) => {
            const mapping = donusumMapping[stokKodu];
            if (mapping && count > 0) {
                messages.push(
                    `${count} adet ${mapping.donusum} üretimi için ${count} adet ${mapping.delikli} 'pasif' yapılmalıdır.`
                );
            }
        });

        return messages;
    };

    const donusumMessages = detectKeceliDonusum();

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-end">
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <span>Son güncelleme: {format(lastUpdated, 'HH:mm:ss')}</span>
                    <div
                        className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}
                        title={isConnected ? 'Bağlantı var' : 'Bağlantı yok'}>
                    </div>
                </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="flex justify-between items-center mb-4">
                    <TabsList style={{ backgroundColor: getLineThemeColor() }}>
                        <TabsTrigger
                            value="uretim-verileri"
                            style={{
                                "--active-bg": getLineAccentColor(),
                            } as React.CSSProperties}
                            className="font-medium data-[state=active]:bg-[var(--active-bg)] data-[state=active]:text-gray-800"
                        >
                            Üretim Verileri
                        </TabsTrigger>
                        <TabsTrigger
                            value="ayarlar"
                            style={{
                                "--active-bg": getLineAccentColor(),
                            } as React.CSSProperties}
                            className="font-medium data-[state=active]:bg-[var(--active-bg)] data-[state=active]:text-gray-800"
                        >
                            Ayarlar
                        </TabsTrigger>
                    </TabsList>

                    <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleManualRefresh}
                            disabled={loadingData}
                        >
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Yenile
                        </Button>
                    </div>
                </div>

                <TabsContent value="uretim-verileri" className="w-full max-w-full mx-0 p-0">
                    <div className="flex flex-col md:flex-row gap-4">
                        <div className="w-full md:w-72 space-y-3 flex-shrink-0">
                            <TarihFiltreleri
                                selectedDate={selectedDate}
                                endDate={endDate}
                                dateFilterType={dateFilterType}
                                onDateFilterTypeChange={setDateFilterType}
                                onDateChange={setSelectedDate}
                                onEndDateChange={setEndDate}
                            />

                            <BoruCapiFiltreleri
                                selectedDiameters={selectedDiameters}
                                disabledDiameters={disabledDiameters}
                                onDiameterChange={handleDiameterChange}
                                onResetFilters={resetFilters}
                            />
                        </div>

                        <div className="flex-grow">
                            <UretimOzeti
                                categories={(() => {
                                    // Admin tarafındaki gibi özet hazırlanıyor
                                    const productTypeSummary = DataCalculations.calculateProductTypeSummary(
                                        filtrelenmisVeriler,
                                        getUrunAdi,
                                        urunler
                                    );
                                    // Varsayılan boş kategoriler
                                    const categories = [
                                        { kategori: 'Delikli', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 },
                                        { kategori: 'Deliksiz', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 },
                                        { kategori: 'Keçeli', toplamUzunluk: 0, ortalamaAgirlik: 0, adet: 0 }
                                    ];
                                    // Her ürün için kategori hesapla
                                    productTypeSummary.forEach(item => {
                                        const category = item.kategori || 'Delikli';
                                        const categoryIndex = categories.findIndex(cat => cat.kategori === category);
                                        if (categoryIndex !== -1) {
                                            categories[categoryIndex].adet += item.adet;
                                            categories[categoryIndex].toplamUzunluk += item.uzunluk || 0;
                                            categories[categoryIndex].ortalamaAgirlik += item.toplamAgirlik;
                                        }
                                    });
                                    // Ortalama ağırlığı hesapla
                                    categories.forEach(category => {
                                        if (category.adet > 0 && category.toplamUzunluk > 0) {
                                            category.ortalamaAgirlik = category.ortalamaAgirlik / category.toplamUzunluk;
                                        }
                                    });
                                    return categories;
                                })()}
                            />
                        </div>
                    </div>

                    <div>
                        {/* KEÇELİ DÖNÜŞÜM Uyarı Mesajları */}
                        {donusumMessages.length > 0 && (
                            <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                                <div className="flex items-start space-x-3">
                                    <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                                    <div className="flex-1">
                                        <h4 className="text-sm font-semibold text-amber-800 mb-2">
                                            KEÇELİ DÖNÜŞÜM Uyarısı
                                        </h4>
                                        <div className="space-y-1">
                                            {donusumMessages.map((message, index) => (
                                                <p key={index} className="text-sm text-amber-700">
                                                    • {message}
                                                </p>
                                            ))}
                                        </div>
                                        <p className="text-xs text-amber-600 mt-2 italic">
                                            Lütfen yukarıdaki dönüşümler için ilgili KEÇELİ BORU kayıtlarını manuel olarak 'pasif' duruma getiriniz.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="mb-2">
                            <h3 className="text-lg font-bold text-blue-700">Son Üretim Verileri</h3>
                            <p className="text-sm text-gray-600">En son üretim yapılan gündeki tüm veriler aşağıda listelenmiştir.</p>
                        </div>

                        <UretimVerileriTablosu
                            veriler={filtrelenmisVeriler}
                            getUrunAdi={getUrunAdi}
                            getUrunCap={getUrunCap}
                            formatBoruAdi={formatBoruAdi}
                            loading={loadingData}
                        />
                    </div>
                </TabsContent>

                <TabsContent value="ayarlar" className="w-full max-w-full mx-0 p-0">
                    <AyarlarTab
                        refreshInterval={15}
                        onRefreshIntervalChange={() => { }}
                        autoRefresh={true}
                        onAutoRefreshChange={() => { }}
                        onRefresh={handleManualRefresh}
                        siloDataList={siloDataList}
                        urunler={urunler}
                        getUrunAdi={getUrunAdi}
                        getUrunCap={getUrunCap}
                        currentLine={currentLine}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default UretimVerileri;

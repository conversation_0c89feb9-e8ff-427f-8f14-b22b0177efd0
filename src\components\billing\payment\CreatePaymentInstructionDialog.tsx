
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

type CreatePaymentInstructionDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export const CreatePaymentInstructionDialog = ({
  open,
  onOpenChange,
}: CreatePaymentInstructionDialogProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [supplierCode, setSupplierCode] = useState("");
  const [orderNumber, setOrderNumber] = useState("");
  const [paymentDetails, setPaymentDetails] = useState<any>(null);
  const [amount, setAmount] = useState("");
  
  // Step 1: Search supplier
  const handleSearchSupplier = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (supplierCode) {
        // Mock supplier data
        setPaymentDetails({
          supplierName: "BİLİR AKADEMİ ORTAK SAĞLIK GÜVENLİK BİRİMİ SAN. VE TİC. LTD ŞTİ",
          supplierCode: supplierCode
        });
        setStep(2);
      }
      setLoading(false);
    }, 800);
  };
  
  // Step 2: Search payment order
  const handleSearchOrder = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (orderNumber) {
        // Mock payment order data
        setPaymentDetails(prev => ({
          ...prev,
          orderNumber,
          description: "BAA20250000000340 NOLU FATURA ÖDEMESİ",
          subject: "BORMEG PLASTİK KEÇELİ BORU",
          amount: "7,900.00",
          paymentDate: "11-04-2025",
          bank: "VAKIFBANK PLASTİK - PLASTİK - TL#16",
          source: "Talep",
          requestInfo: {
            requestNumber: "21258",
            subject: "ŞUBATİŞ SAĞLIĞI VE GÜVENLİĞİ BEDELİ",
            requestedBy: "ssever",
            user: "ssever",
            branch: "MEÇ MERKEZ"
          }
        }));
        setAmount("7900");
        setStep(3);
      }
      setLoading(false);
    }, 800);
  };
  
  // Step 3: Submit payment instruction
  const handleSubmit = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Ödeme Talimatı Oluşturuldu",
        description: "Ödeme talimatınız başarıyla oluşturuldu ve kaydedildi.",
      });
      
      // Reset and close dialog
      setStep(1);
      setSupplierCode("");
      setOrderNumber("");
      setPaymentDetails(null);
      setAmount("");
      onOpenChange(false);
      setLoading(false);
    }, 1000);
  };
  
  const handleClose = () => {
    setStep(1);
    setSupplierCode("");
    setOrderNumber("");
    setPaymentDetails(null);
    setAmount("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto p-4">
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-2 text-base">
            <FileText className="h-4 w-4" />
            Ödeme Emri Talimatı Oluştur
          </DialogTitle>
        </DialogHeader>

        <Card className="border shadow-sm">
          <CardContent className="p-4">
            {step === 1 && (
              <div className="space-y-4">
                <div className="grid gap-3">
                  <Label htmlFor="supplierCode">Cari Kod:</Label>
                  <div className="flex gap-2">
                    <Input 
                      id="supplierCode"
                      value={supplierCode}
                      onChange={(e) => setSupplierCode(e.target.value)}
                      placeholder="Cari kod giriniz"
                      className="bg-yellow-50"
                    />
                    <Button 
                      onClick={handleSearchSupplier} 
                      disabled={!supplierCode || loading}
                      className="whitespace-nowrap"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      Sorgula
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            {step === 2 && (
              <div className="space-y-4">
                <div className="bg-blue-50 p-3 rounded-md mb-4">
                  <p className="text-sm font-medium">Cari Bilgileri:</p>
                  <p className="text-sm">{paymentDetails?.supplierName}</p>
                  <p className="text-xs text-muted-foreground">Kod: {paymentDetails?.supplierCode}</p>
                </div>
                
                <div className="grid gap-3">
                  <Label htmlFor="orderNumber">Ödeme Emirleri:</Label>
                  <div className="flex gap-2">
                    <Input 
                      id="orderNumber"
                      value={orderNumber}
                      onChange={(e) => setOrderNumber(e.target.value)}
                      placeholder="Ödeme emri numarası giriniz"
                      className="bg-yellow-50"
                    />
                    <Button 
                      onClick={handleSearchOrder} 
                      disabled={!orderNumber || loading}
                      className="whitespace-nowrap"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      Sorgula
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            {step === 3 && (
              <div className="space-y-4">
                <div className="bg-green-50 p-3 rounded-md mb-4">
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Açıklama:</p>
                      <p className="text-sm">{paymentDetails?.description}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Şube:</p>
                      <p className="text-sm">{paymentDetails?.subject}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Tutar:</p>
                      <p className="text-sm font-medium">{paymentDetails?.amount} TL</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Ödeme Tarihi:</p>
                      <p className="text-sm">{paymentDetails?.paymentDate}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Ödeme Bankası:</p>
                      <p className="text-sm">{paymentDetails?.bank}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Ödeme Kaynağı:</p>
                      <p className="text-sm">{paymentDetails?.source}</p>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-3">
                  <h3 className="text-sm font-medium mb-3">Talep Bilgileri</h3>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 bg-muted/30 p-3 rounded-md">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Talep No:</p>
                      <p className="text-sm">* {paymentDetails?.requestInfo.requestNumber}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Konu:</p>
                      <p className="text-sm">{paymentDetails?.requestInfo.subject}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Talep Eden:</p>
                      <p className="text-sm">{paymentDetails?.requestInfo.requestedBy}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Kullanıcı:</p>
                      <p className="text-sm">{paymentDetails?.requestInfo.user}</p>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-muted-foreground">Şube:</p>
                      <p className="text-sm">{paymentDetails?.requestInfo.branch}</p>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-3">
                  <h3 className="text-sm font-medium mb-3">Ödeme Detayları</h3>
                  <div className="grid gap-4">
                    <div>
                      <Label htmlFor="amount">Ödeme Tutarı:</Label>
                      <Input 
                        id="amount"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        className="bg-yellow-50 mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="company">Ödemenin Çıkacağı Şirket:</Label>
                      <Select defaultValue="default">
                        <SelectTrigger id="company" className="mt-1">
                          <SelectValue placeholder="Şirket seçiniz" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">BORMEG PLASTİK BORU MAK. İNŞ. SAN. VE TİC. A.Ş</SelectItem>
                          <SelectItem value="company2">XYZ Şirketi</SelectItem>
                          <SelectItem value="company3">ABC Şirketi</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex justify-end gap-2 mt-4 pt-3 border-t">
              <Button 
                variant="outline" 
                onClick={handleClose}
                size="sm"
                className="h-9"
              >
                İptal
              </Button>
              
              {step < 3 ? (
                <Button
                  disabled={
                    (step === 1 && !supplierCode) || 
                    (step === 2 && !orderNumber) ||
                    loading
                  }
                  onClick={step === 1 ? handleSearchSupplier : handleSearchOrder}
                  size="sm"
                  className="h-9"
                >
                  {loading ? "İşlem yapılıyor..." : "Devam Et"}
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmit}
                  disabled={loading}
                  size="sm"
                  className="h-9"
                >
                  {loading ? "Kaydediliyor..." : "Onayla ve Kaydet"}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
};


import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";

export interface TabItem {
  id: string;
  label: string;
  component: React.ReactNode;
  icon?: React.ReactNode;
}

interface TabsNavigationProps {
  items: TabItem[];
  defaultValue?: string;
  className?: string;
}

export const TabsNavigation = ({
  items,
  defaultValue,
  className
}: TabsNavigationProps) => {
  return (
    <Tabs 
      defaultValue={defaultValue || items[0]?.id} 
      className={`space-y-4 ${className}`}
    >
      <TabsList className="flex flex-wrap">
        {items.map((item) => (
          <TabsTrigger key={item.id} value={item.id}>
            {item.icon && <span className="mr-2">{item.icon}</span>}
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {items.map((item) => (
        <TabsContent key={item.id} value={item.id} className="space-y-4">
          {item.component}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export const TabsCard = ({ children }: { children: React.ReactNode }) => {
  return (
    <Card className="p-6">
      {children}
    </Card>
  );
};

import React from 'react';
import { Filter } from "lucide-react";

interface BoruCapiFiltreleriProps {
    selectedDiameters: Record<string, boolean>;
    disabledDiameters: string[];
    onDiameterChange: (diameter: string, checked: boolean) => void;
    onResetFilters: () => void;
}

const BoruCapiFiltreleri: React.FC<BoruCapiFiltreleriProps> = ({
    selectedDiameters,
    disabledDiameters,
    onDiameterChange,
    onResetFilters
}) => {
    console.log("BoruCapiFiltreleri render edildi:", selectedDiameters);

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center justify-between p-2 border-b border-gray-100">
                <div className="flex items-center">
                    <Filter className="h-3.5 w-3.5 text-blue-600 mr-1.5" />
                    <span className="font-medium text-blue-600 text-sm"><PERSON><PERSON></span>
                </div>

                <button
                    className="flex items-center text-xs text-gray-500 hover:text-blue-600 bg-gray-50 px-2 py-0.5 rounded"
                    onClick={() => {
                        console.log("Temizle butonuna tıklandı");
                        onResetFilters();
                    }}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-2.5 w-2.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Temizle
                </button>
            </div>

            <div className="p-2">
                <div className="flex flex-wrap gap-1.5">
                    {Object.keys(selectedDiameters).map((diameter) => {
                        const isSelected = selectedDiameters[diameter];
                        const isDisabled = disabledDiameters.includes(diameter);

                        return (
                            <div
                                key={diameter}
                                className={`flex items-center border px-2 py-1 rounded text-xs ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-blue-50'
                                    } ${isSelected ? 'bg-blue-50 border-blue-300 text-blue-700' : 'bg-white border-gray-200 text-gray-700'
                                    }`}
                                onClick={() => {
                                    if (!isDisabled) {
                                        console.log(`Çap tıklandı: ${diameter}, yeni değer: ${!isSelected}`);
                                        onDiameterChange(diameter, !isSelected);
                                    }
                                }}
                            >
                                {/* Checkbox yerine küçük daire */}
                                <div className={`w-2.5 h-2.5 rounded-full mr-1 flex-shrink-0 ${isSelected ? 'bg-blue-500' : 'border border-gray-300'
                                    }`}></div>
                                <span>Ø {diameter}</span>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default BoruCapiFiltreleri;

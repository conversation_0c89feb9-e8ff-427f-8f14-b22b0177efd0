
import { useState, useRef, useEffect } from "react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronsUpDown, User, Building2, Search } from "lucide-react";
import { CariHesap } from "@/types/cari";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { getCustomerOptions } from "@/services/api";
import { toast } from "sonner";

interface CustomerSelectorProps {
  selectedCustomer: CariHesap | null;
  onCustomerSelect: (customer: CariHesap | null) => void;
}

export const CustomerSelector = ({ selectedCustomer, onCustomerSelect }: CustomerSelectorProps) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Gerçek API'den müşterileri getir
  const { data: customers = [], isLoading, error } = useQuery({
    queryKey: ['customers'],
    queryFn: getCustomerOptions,
    staleTime: 5 * 60 * 1000, // 5 dakika cache
  });

  // Hata durumunda bildirim göster
  useEffect(() => {
    if (error) {
      toast.error("Müşteri listesi yüklenirken bir hata oluştu.");
      console.error("Müşteri yükleme hatası:", error);
    }
  }, [error]);

  // Arama değerine göre müşterileri filtrele
  const filteredCustomers = customers.filter((customer: CariHesap) => {
    const searchLower = searchValue.toLowerCase();
    return (
      customer.unvan.toLowerCase().includes(searchLower) ||
      customer.kod.toLowerCase().includes(searchLower) ||
      (customer.vknTckn && customer.vknTckn.toLowerCase().includes(searchLower)) ||
      (customer.il && customer.il.toLowerCase().includes(searchLower))
    );
  });

  // Popover açıldığında arama kutusuna odaklan
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between h-auto py-3"
        >
          {selectedCustomer ? (
            <div className="flex flex-col items-start">
              <div className="font-medium">{selectedCustomer.unvan}</div>
              <div className="text-xs text-muted-foreground flex items-center gap-1 mt-0.5">
                <Building2 className="h-3 w-3" />
                {selectedCustomer.kod} | {selectedCustomer.vknTckn}
              </div>
            </div>
          ) : (
            <div className="flex items-center text-muted-foreground gap-2">
              <Search className="h-4 w-4" />
              <span>Müşteri seçin veya arayın...</span>
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[450px]" align="start">
        <Command>
          <CommandInput 
            placeholder="Müşteri adı, kod veya vergi no..." 
            ref={inputRef}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            {isLoading ? (
              <div className="py-6 text-center">
                <User className="h-10 w-10 text-muted-foreground mb-2 mx-auto animate-pulse" />
                <p>Müşteriler yükleniyor...</p>
              </div>
            ) : (
              <>
                <CommandEmpty>
                  <div className="py-6 text-center flex flex-col items-center">
                    <User className="h-10 w-10 text-muted-foreground mb-2" />
                    <p>Müşteri bulunamadı</p>
                    <Button 
                      variant="link" 
                      className="mt-2 text-xs"
                      onClick={() => {
                        setOpen(false);
                        // Here you would open a new customer dialog
                      }}
                    >
                      Yeni müşteri eklemek için tıklayın
                    </Button>
                  </div>
                </CommandEmpty>
                <CommandGroup heading="Müşteriler">
                  {filteredCustomers.map((customer: CariHesap) => (
                    <CommandItem
                      key={customer.id}
                      value={customer.id}
                      onSelect={() => {
                        onCustomerSelect(customer);
                        setOpen(false);
                        setSearchValue("");
                      }}
                      className="py-2"
                    >
                      <div className="flex justify-between items-center w-full">
                        <div>
                          <div className="font-medium">{customer.unvan}</div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1 mt-0.5">
                            <span>{customer.kod}</span>
                            {customer.il && (
                              <>
                                <span>•</span>
                                <span>{customer.il}</span>
                              </>
                            )}
                            <span>•</span>
                            <span>VN: {customer.vknTckn}</span>
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "h-4 w-4",
                            selectedCustomer?.id === customer.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};


import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Clock, CalendarCheck, AlertTriangle } from "lucide-react";
import { useMaintenanceStore } from "@/stores/maintenanceStore";
import { addDays, isBefore } from "date-fns";

export const MaintenanceStats = () => {
  const { maintenanceItems, maintenanceSchedules } = useMaintenanceStore();
  
  // Calculate statistics
  const totalMaintenance = maintenanceItems.length;
  
  const completedMaintenance = maintenanceItems.filter(
    item => item.status === 'completed'
  ).length;
  
  const upcomingMaintenance = maintenanceSchedules.filter(schedule => {
    if (!schedule.isActive) return false;
    
    const today = new Date();
    const reminderDate = addDays(new Date(schedule.nextDate), -schedule.reminderDays);
    return isBefore(today, new Date(schedule.nextDate)) && isBefore(reminderDate, today);
  }).length;
  
  const overdueMaintenance = maintenanceSchedules.filter(schedule => {
    if (!schedule.isActive) return false;
    
    return isBefore(new Date(schedule.nextDate), new Date());
  }).length;
  
  const totalCost = maintenanceItems
    .filter(item => item.status === 'completed')
    .reduce((sum, item) => sum + item.cost, 0);
  
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <div className="stats-card">
        <div className="stats-card-icon">
          <Wrench className="h-5 w-5" />
        </div>
        <h3 className="text-lg font-semibold">Toplam Bakım</h3>
        <div className="text-3xl font-bold mt-2">{totalMaintenance}</div>
        <p className="text-sm text-muted-foreground mt-2">
          {completedMaintenance} tamamlanan, {totalMaintenance - completedMaintenance} devam eden
        </p>
      </div>
      
      <div className="stats-card">
        <div className="stats-card-icon">
          <Clock className="h-5 w-5" />
        </div>
        <h3 className="text-lg font-semibold">Yaklaşan Bakımlar</h3>
        <div className="text-3xl font-bold mt-2">{upcomingMaintenance}</div>
        <p className="text-sm text-muted-foreground mt-2">
          Önümüzdeki günlerdeki bakımlar
        </p>
        {upcomingMaintenance > 0 && <div className="badge-pulse mt-3 self-start"></div>}
      </div>
      
      <div className="stats-card">
        <div className="stats-card-icon">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
        </div>
        <h3 className="text-lg font-semibold">Geciken Bakımlar</h3>
        <div className="text-3xl font-bold mt-2">{overdueMaintenance}</div>
        <p className="text-sm text-muted-foreground mt-2">
          Zamanında yapılmayan bakımlar
        </p>
        {overdueMaintenance > 0 && <div className="mt-2 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
          <div className="bg-amber-500 h-full" style={{ width: `100%` }}></div>
        </div>}
      </div>
      
      <div className="stats-card">
        <div className="stats-card-icon">
          <CalendarCheck className="h-5 w-5 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold">Toplam Maliyet</h3>
        <div className="text-3xl font-bold mt-2">{totalCost.toLocaleString('tr-TR')} ₺</div>
        <p className="text-sm text-muted-foreground mt-2">
          Tamamlanan tüm bakımların maliyeti
        </p>
      </div>
    </div>
  );
};


import { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle
} from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, CheckCircle2, Clock, User } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { SafePaymentButton } from "./SafePaymentButton";
import { logger } from "@/utils/logger";
import { generateTransactionId } from "@/utils/apiUtils";

// Form schema validation using zod
const paymentOrderSchema = z.object({
  requestingUser: z.string().min(2, { message: "Kullanıcı adı gereklidir" }),
  date: z.date({ required_error: "Tarih seçilmelidir" }),
  details: z.string().min(10, { message: "En az 10 karakter girmelisiniz" }),
  priority: z.enum(["high", "medium", "low"], { required_error: "Öncelik seçilmelidir" }),
  amount: z.string().min(1, { message: "Tutar gereklidir" })
    .refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, { message: "Geçerli bir tutar giriniz" }),
  paymentMethod: z.string({ required_error: "Ödeme yöntemi seçilmelidir" }),
  company: z.string().min(1, { message: "Firma seçilmelidir" })
});

type PaymentOrderForm = z.infer<typeof paymentOrderSchema>;

export const PaymentOrderForm = () => {
  const [loading, setLoading] = useState(false);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  
  // Default values for the form
  const defaultValues: Partial<PaymentOrderForm> = {
    date: new Date(),
    priority: "medium",
    paymentMethod: "banka_transferi"
  };

  // Initialize the form
  const form = useForm<PaymentOrderForm>({
    resolver: zodResolver(paymentOrderSchema),
    defaultValues,
  });

  // Handle form submission
  const onSubmit = async (data: PaymentOrderForm) => {
    // Yeni bir transaction ID oluştur
    const newTransactionId = generateTransactionId();
    setTransactionId(newTransactionId);
    
    setLoading(true);
    logger.info(`Ödeme emri oluşturma başlatıldı. [TransactionID: ${newTransactionId}]`);
    
    // Simulate API call
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log("Form data submitted:", data);
      logger.info(`Ödeme emri verileri: ${JSON.stringify(data)}. [TransactionID: ${newTransactionId}]`);
      
      toast.success("Ödeme emriniz başarıyla oluşturuldu ve onay için gönderildi.");
      
      // Reset the form
      form.reset(defaultValues);
      logger.info(`Ödeme emri başarıyla oluşturuldu. [TransactionID: ${newTransactionId}]`);
    } catch (error) {
      logger.error(`Ödeme emri oluşturulurken hata: ${error}. [TransactionID: ${newTransactionId}]`);
      toast.error("Ödeme emri oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.");
    } finally {
      setLoading(false);
    }
  };

  // Ödeme verilerini hazırla
  const preparePaymentData = (data: PaymentOrderForm) => {
    return {
      cari_id: data.company,
      tutar: parseFloat(data.amount),
      odeme_tarihi: format(data.date, 'yyyy-MM-dd'),
      odeme_yontemi: data.paymentMethod,
      aciklama: data.details,
      referans_no: `EMR-${Date.now().toString().slice(-6)}` // Basit bir referans no oluştur
    };
  };

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-green-500" />
          Yeni Ödeme Emri
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Requesting User */}
              <FormField
                control={form.control}
                name="requestingUser"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      Ödeme Talep Eden Kullanıcı Adı
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Ad Soyad" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date Field */}
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="flex items-center gap-1">
                      <CalendarIcon className="h-4 w-4" />
                      Tarih
                    </FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: tr })
                            ) : (
                              <span>Tarih Seçin</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                          locale={tr}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Company Field */}
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Firma</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Firma seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="company-1">ABC Teknoloji A.Ş.</SelectItem>
                      <SelectItem value="company-2">DEF İnşaat Ltd. Şti.</SelectItem>
                      <SelectItem value="company-3">GHI Tekstil Sanayi A.Ş.</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Amount Field */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tutar</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input {...field} placeholder="0.00" type="text" />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
                        TL
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Method Field */}
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ödeme Yöntemi</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Ödeme yöntemi seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="banka_transferi">Banka Transferi</SelectItem>
                      <SelectItem value="kredi_karti">Kredi Kartı</SelectItem>
                      <SelectItem value="nakit">Nakit</SelectItem>
                      <SelectItem value="cek">Çek</SelectItem>
                      <SelectItem value="senet">Senet</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Transaction Details */}
            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>İşlem Detayı</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Ödeme detaylarını buraya girin..." 
                      {...field} 
                      className="resize-y min-h-[120px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Priority Field */}
            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Önceliği</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Öncelik seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="high" className="text-red-500 font-medium">Yüksek</SelectItem>
                      <SelectItem value="medium" className="text-amber-500 font-medium">Orta</SelectItem>
                      <SelectItem value="low" className="text-green-500 font-medium">Düşük</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Read-only approval fields */}
            <div className="bg-gray-50 dark:bg-slate-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700 space-y-3">
              <h3 className="text-sm font-medium flex items-center gap-1 text-muted-foreground border-b pb-2 border-gray-200 dark:border-gray-700">
                <Clock className="h-4 w-4" />
                Onay Bilgileri
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Onaylama Durumu</label>
                  <div className="bg-white dark:bg-slate-900 px-3 py-2 rounded border border-gray-200 dark:border-gray-700 text-sm">
                    Beklemede
                  </div>
                </div>
                
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Onaylayan</label>
                  <div className="bg-white dark:bg-slate-900 px-3 py-2 rounded border border-gray-200 dark:border-gray-700 text-sm text-gray-400">
                    -
                  </div>
                </div>
                
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Onaylama Tarih/Saat</label>
                  <div className="bg-white dark:bg-slate-900 px-3 py-2 rounded border border-gray-200 dark:border-gray-700 text-sm text-gray-400">
                    -
                  </div>
                </div>
              </div>
            </div>

            <CardFooter className="flex justify-end gap-2 px-0 pb-0 pt-4">
              <Button variant="outline" type="button">
                İptal
              </Button>
              
              <Button 
                type="submit" 
                disabled={loading} 
                className={loading ? "opacity-50 cursor-not-allowed" : ""}
              >
                {loading ? "Kaydediliyor..." : "Ödeme Emri Oluştur"}
              </Button>
              
              {/* Güvenli Ödeme Butonu */}
              <SafePaymentButton 
                paymentData={form.getValues() ? preparePaymentData(form.getValues() as PaymentOrderForm) : {
                  cari_id: "",
                  tutar: 0,
                  odeme_tarihi: format(new Date(), 'yyyy-MM-dd'),
                  odeme_yontemi: "banka_transferi"
                }}
                onSuccess={(response) => {
                  console.log("Ödeme başarıyla kaydedildi", response);
                  toast.success("Ödeme başarıyla kaydedildi");
                  form.reset(defaultValues);
                }}
                onError={(error) => {
                  console.error("Ödeme kaydedilemedi", error);
                  toast.error("Ödeme kaydedilemedi");
                }}
                variant="default"
              >
                Güvenli Ödeme Oluştur
              </SafePaymentButton>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

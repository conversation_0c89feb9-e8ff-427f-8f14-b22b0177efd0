import React from 'react';
import { CalendarDays } from "lucide-react";

interface TarihFiltreleriProps {
    selectedDate: string;
    endDate: string;
    dateFilterType: 'tekGun' | 'aralik';
    onDateFilterTypeChange: (type: 'tekGun' | 'aralik') => void;
    onDateChange: (date: string) => void;
    onEndDateChange: (date: string) => void;
}

const TarihFiltreleri: React.FC<TarihFiltreleriProps> = ({
    selectedDate,
    endDate,
    dateFilterType,
    onDateFilterTypeChange,
    onDateChange,
    onEndDateChange
}) => {
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center p-2 border-b border-gray-100">
                <CalendarDays className="h-3.5 w-3.5 text-blue-600 mr-1.5" />
                <span className="font-medium text-blue-600 text-sm"><PERSON><PERSON><PERSON></span>
            </div>

            <div className="p-2 space-y-2">
                <div className="flex bg-gray-100 rounded overflow-hidden">
                    <button
                        className={`flex-1 py-1 px-2 text-xs font-medium transition-colors ${dateFilterType === 'tekGun' ? 'bg-blue-600 text-white' : 'text-gray-700'}`}
                        onClick={() => onDateFilterTypeChange('tekGun')}
                    >
                        Tek Gün
                    </button>
                    <button
                        className={`flex-1 py-1 px-2 text-xs font-medium transition-colors ${dateFilterType === 'aralik' ? 'bg-blue-600 text-white' : 'text-gray-700'}`}
                        onClick={() => onDateFilterTypeChange('aralik')}
                    >
                        Aralık
                    </button>
                </div>

                {dateFilterType === 'tekGun' ? (
                    <div className="relative">
                        <input
                            type="date"
                            value={selectedDate}
                            onChange={(e) => onDateChange(e.target.value)}
                            className="border border-gray-200 rounded px-2 py-1 w-full text-xs focus:ring-blue-500 focus:border-blue-500 outline-none"
                        />
                    </div>
                ) : (
                    <div className="flex gap-1.5">
                        <div className="relative flex-1">
                            <input
                                type="date"
                                value={selectedDate}
                                onChange={(e) => onDateChange(e.target.value)}
                                className="border border-gray-200 rounded px-2 py-1 w-full text-xs focus:ring-blue-500 focus:border-blue-500 outline-none"
                            />
                        </div>
                        <div className="relative flex-1">
                            <input
                                type="date"
                                value={endDate}
                                onChange={(e) => onEndDateChange(e.target.value)}
                                className="border border-gray-200 rounded px-2 py-1 w-full text-xs focus:ring-blue-500 focus:border-blue-500 outline-none"
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TarihFiltreleri;

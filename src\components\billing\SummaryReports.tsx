
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>hart,
  <PERSON>Chart,
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Bar,
  Line,
  Pie,
  Cell,
  ResponsiveContainer,
} from "recharts";
import {
  ArrowDownRight,
  ArrowRightLeft,
  ArrowUpRight,
  BadgeCheck,
  BadgeDollarSign,
  BarChart2,
  CalendarDays,
  CircleDollarSign,
  CreditCard,
  Download,
  FileSpreadsheet,
  Filter,
  Pie<PERSON><PERSON> as PieChartIcon,
  TrendingUp,
  Wallet,
} from "lucide-react";
import {
  generateCategorySales,
  generateFinancialSummary,
  generateMonthlySales,
  generateTransactions,
  formatCurrency,
  formatPercentage,
} from "./FinanceDataUtils";

// Mock data
const monthlySales = generateMonthlySales();
const categorySales = generateCategorySales();
const transactions = generateTransactions(10);
const financialSummary = generateFinancialSummary();

// Colors for the charts
const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"];

export const SummaryReports = () => {
  const [reportPeriod, setReportPeriod] = useState("monthly");

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">Finansal Özet Raporları</h2>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <CalendarDays className="h-4 w-4" />
            Son 30 Gün
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            Excel
          </Button>
          <Button size="sm" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {financialSummary.metrics.map((metric) => (
          <Card key={metric.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              {metric.name === "Toplam Satış" && (
                <BadgeDollarSign className="h-4 w-4 text-blue-600" />
              )}
              {metric.name === "Toplam Gider" && (
                <CreditCard className="h-4 w-4 text-red-600" />
              )}
              {metric.name === "Net Kâr" && (
                <Wallet className="h-4 w-4 text-green-600" />
              )}
              {metric.name === "Kâr Marjı" && (
                <TrendingUp className="h-4 w-4 text-purple-600" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metric.type === "currency"
                  ? formatCurrency(metric.value)
                  : formatPercentage(metric.value)}
              </div>
              <div className="flex items-center pt-1">
                {metric.trend === "up" ? (
                  <ArrowUpRight className="h-3.5 w-3.5 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="h-3.5 w-3.5 text-red-600 mr-1" />
                )}
                <span
                  className={`text-xs ${
                    metric.trend === "up" ? "text-green-600" : "text-red-600"
                  } font-medium`}
                >
                  {metric.change > 0 ? "+" : ""}
                  {formatPercentage(metric.change)}
                </span>
                <span className="text-xs text-muted-foreground ml-1">
                  geçen dönem
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={reportPeriod} onValueChange={setReportPeriod} className="space-y-4">
        <TabsList className="grid w-full md:w-auto grid-cols-3">
          <TabsTrigger value="monthly" className="flex items-center gap-2">
            <BarChart2 className="h-4 w-4" />
            <span className="hidden md:inline">Aylık Trend</span>
            <span className="md:hidden">Aylık</span>
          </TabsTrigger>
          <TabsTrigger value="category" className="flex items-center gap-2">
            <PieChartIcon className="h-4 w-4" />
            <span className="hidden md:inline">Kategori Analizi</span>
            <span className="md:hidden">Kategori</span>
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <ArrowRightLeft className="h-4 w-4" />
            <span className="hidden md:inline">Son İşlemler</span>
            <span className="md:hidden">İşlemler</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="monthly">
          <Card>
            <CardHeader>
              <CardTitle>Aylık Satış Trendi</CardTitle>
              <CardDescription>Son 12 ayın satış performansı</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlySales}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [formatCurrency(value as number), "Satış"]}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="value"
                      name="Satış"
                      stroke="#4f46e5"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="category">
          <div className="grid md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Kategori Bazlı Satışlar</CardTitle>
                <CardDescription>Satışların kategori dağılımı</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={categorySales}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) => [formatCurrency(value as number), "Satış"]}
                      />
                      <Legend />
                      <Bar dataKey="value" name="Satış" fill="#4f46e5">
                        {categorySales.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Kategori Dağılımı</CardTitle>
                <CardDescription>Satışların yüzdelik dağılımı</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={categorySales}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: %${(percent * 100).toFixed(0)}`}
                        outerRadius={150}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="date"
                      >
                        {categorySales.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [formatCurrency(value as number), "Satış"]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Son İşlemler</CardTitle>
              <CardDescription>Son 10 finansal işlem</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="h-12 px-4 text-left align-middle font-medium">
                          Tarih
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-medium">
                          Açıklama
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-medium">
                          Kategori
                        </th>
                        <th className="h-12 px-4 text-right align-middle font-medium">
                          Tutar
                        </th>
                        <th className="h-12 px-4 text-center align-middle font-medium">
                          Tip
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction) => (
                        <tr
                          key={transaction.id}
                          className="border-b transition-colors hover:bg-muted/50"
                        >
                          <td className="p-4 align-middle font-medium">
                            {transaction.date}
                          </td>
                          <td className="p-4 align-middle">
                            {transaction.description}
                          </td>
                          <td className="p-4 align-middle">
                            {transaction.category}
                          </td>
                          <td
                            className={`p-4 align-middle text-right font-medium ${
                              transaction.type === "income"
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {transaction.type === "income" ? "+" : "-"}
                            {formatCurrency(transaction.amount)}
                          </td>
                          <td className="p-4 align-middle text-center">
                            <span
                              className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                transaction.type === "income"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {transaction.type === "income" ? "Gelir" : "Gider"}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="flex justify-center mt-4">
                <Button variant="outline">Tüm İşlemleri Görüntüle</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

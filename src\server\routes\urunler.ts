
import express from 'express';
import { query } from '../../utils/db';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Tüm ürünleri getir
router.get('/', async (req, res) => {
  try {
    const urunler = await query('SELECT * FROM urunler ORDER BY olusturma_tarihi DESC');
    res.json(urunler);
  } catch (error) {
    console.error('Ürünleri getirme hatası:', error);
    res.status(500).json({ error: 'Ürünler getirilemedi' });
  }
});

// ID'ye göre ürün getir
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const urun = await query('SELECT * FROM urunler WHERE id = ?', [id]);
    
    if (Array.isArray(urun) && urun.length === 0) {
      return res.status(404).json({ error: '<PERSON><PERSON><PERSON><PERSON> bulunamad<PERSON>' });
    }
    
    res.json(urun[0]);
  } catch (error) {
    console.error('Ürün getirme hatası:', error);
    res.status(500).json({ error: 'Ürün getirilemedi' });
  }
});

// Yeni ürün ekle
router.post('/', async (req, res) => {
  try {
    const { kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!kod || !isim || !birim || fiyat === undefined || kdv_oran === undefined || !tur) {
      return res.status(400).json({ error: 'Kod, isim, birim, fiyat, kdv oranı ve tür alanları zorunludur' });
    }
    
    // ID oluştur
    const id = uuidv4();
    
    await query(
      `INSERT INTO urunler 
      (id, kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur]
    );
    
    res.status(201).json({ 
      message: 'Ürün başarıyla oluşturuldu', 
      id 
    });
  } catch (error) {
    console.error('Ürün oluşturma hatası:', error);
    res.status(500).json({ error: 'Ürün oluşturulamadı' });
  }
});

// Ürün güncelle
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur } = req.body;
    
    // Zorunlu alanları kontrol et
    if (!kod || !isim || !birim || fiyat === undefined || kdv_oran === undefined || !tur) {
      return res.status(400).json({ error: 'Kod, isim, birim, fiyat, kdv oranı ve tür alanları zorunludur' });
    }
    
    await query(
      `UPDATE urunler 
      SET kod = ?, isim = ?, aciklama = ?, birim = ?, fiyat = ?, kdv_oran = ?, stok = ?, tur = ? 
      WHERE id = ?`,
      [kod, isim, aciklama, birim, fiyat, kdv_oran, stok, tur, id]
    );
    
    res.json({ message: 'Ürün başarıyla güncellendi' });
  } catch (error) {
    console.error('Ürün güncelleme hatası:', error);
    res.status(500).json({ error: 'Ürün güncellenemedi' });
  }
});

// Ürün sil
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await query('DELETE FROM urunler WHERE id = ?', [id]);
    res.json({ message: 'Ürün başarıyla silindi' });
  } catch (error) {
    console.error('Ürün silme hatası:', error);
    res.status(500).json({ error: 'Ürün silinemedi' });
  }
});

export default router;

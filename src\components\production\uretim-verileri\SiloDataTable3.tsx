import React from 'react';
import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { SiloData } from '../../../models/SiloData';
import { UrunModel } from '../../../models/UrunModel';
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { format, subHours, addHours } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card } from '@/components/ui/card';

// Statik ürün listesi - Hat 3 için güncellenmiş (KEÇELİ DÖNÜŞÜM eklendi)
const StaticUrunListesi = [
    { stokKodu: "MRZ-ST00662", ad: "Ø 80 DELİKSİZ BORU" },
    { stokKodu: "MRZ-ST00663", ad: "Ø 80 KEÇELİ BORU (DELİKLİ-KEÇELİ)" },
    { stokKodu: "MRZ-ST10663", ad: "Ø 80 KEÇELİ DÖNÜŞÜM" },
    { stokKodu: "MRZ-ST00659", ad: "Ø 100 DELİKSİZ BORU" },
    { stokKodu: "MRZ-ST02549", ad: "Ø 100 KEÇELİ BORU (DELİKLİ-KEÇELİ)" },
    { stokKodu: "MRZ-ST12549", ad: "Ø 100 KEÇELİ DÖNÜŞÜM" },
    { stokKodu: "MRZ-ST00664", ad: "Ø 125 DELİKSİZ BORU" },
    { stokKodu: "MRZ-ST00666", ad: "Ø 125 KEÇELİ BORU (DELİKLİ-KEÇELİ)" },
    { stokKodu: "MRZ-ST10666", ad: "Ø 125 KEÇELİ DÖNÜŞÜM" },
    { stokKodu: "MRZ-ST00658", ad: "Ø 160 DELİKSİZ BORU" },
    { stokKodu: "MRZ-ST02550", ad: "Ø 160 KEÇELİ BORU (DELİKLİ-KEÇELİ)" },
    { stokKodu: "MRZ-ST12550", ad: "Ø 160 KEÇELİ DÖNÜŞÜM" },
    { stokKodu: "MRZ-ST00660", ad: "Ø 200 DELİKSİZ BORU" },
    { stokKodu: "MRZ-ST02551", ad: "Ø 200 KEÇELİ BORU (DELİKLİ-KEÇELİ)" },
    { stokKodu: "MRZ-ST12551", ad: "Ø 200 KEÇELİ DÖNÜŞÜM" }
];

interface SiloDataTable3Props {
    siloDataList: SiloData[];
    urunAdlari: Record<string, string>;
    urunler: UrunModel[];
    handleUpdateSiloData: (id: number, newStokKodu: string) => void;
    handleDeleteSiloData: (id: number) => void;
}

const SiloDataTable3 = ({
    siloDataList,
    urunAdlari,
    urunler,
    handleUpdateSiloData,
    handleDeleteSiloData
}: SiloDataTable3Props) => {
    const [itemToDelete, setItemToDelete] = useState<SiloData | null>(null);
    const [itemToUpdate, setItemToUpdate] = useState<SiloData | null>(null);
    const [secilenUrun, setSecilenUrun] = useState<string>('');

    // Stok koduna göre ürün adını bulma
    const getUrunAdiByStokkodu = useCallback((stokKodu: string): string => {
        const urun = StaticUrunListesi.find(u => u.stokKodu === stokKodu);
        return urun ? urun.ad : '';
    }, []);

    // Düzenleme işlemini başlat
    const handleEditStart = useCallback((item: SiloData) => {
        setItemToUpdate(item);
        setSecilenUrun(item.stokKodu);
    }, []);

    // Ürün değişikliğini ele al
    const handleUrunChange = useCallback((value: string) => {
        setSecilenUrun(value);
    }, []);

    // Güncelleme işlemini tamamla
    const handleUpdateComplete = useCallback(() => {
        if (itemToUpdate && secilenUrun) {
            handleUpdateSiloData(itemToUpdate.id, secilenUrun);
            setItemToUpdate(null);
            setSecilenUrun('');
        }
    }, [itemToUpdate, secilenUrun, handleUpdateSiloData]);

    // İptal
    const handleCancel = useCallback(() => {
        setItemToUpdate(null);
        setSecilenUrun('');
    }, []);

    return (
        <Card className="mt-8 p-6 shadow-lg border border-gray-200 rounded-xl bg-white">
            <div className="flex items-center mb-4">
                <div className="h-5 w-2 bg-purple-600 rounded-full mr-2"></div>
                <h2 className="text-xl font-bold text-purple-700">Hat 3 - Veri Düzenleme</h2>
            </div>

            <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-100">
                <div className="flex items-center text-sm">
                    <svg className="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-700">Üretim Hattı 3 - Kayıtlı verileri düzenleyebilir veya silebilirsiniz.</span>
                </div>
            </div>

            <div className="overflow-x-auto rounded-xl border border-gray-100 shadow">
                <table className="min-w-full bg-white divide-y divide-gray-200">
                    <thead>
                        <tr className="bg-gradient-to-r from-purple-50 to-purple-100">
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">ID</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Tarih</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Stok Kodu</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Stok Adı</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Boru Ağırlık</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Durum</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Dönüşüm Tarihi</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">Dönüşüm ID</th>
                            <th className="py-3 px-4 text-left text-xs font-semibold text-purple-800 uppercase tracking-wider">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {siloDataList.slice(0, 100).map((item, index) => (
                            <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                <td className="py-3 px-4 text-sm font-medium text-gray-900">{item.id}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">
                                    <div className="text-sm font-medium text-gray-800">
                                        {format(new Date(item.tarih), 'HH:mm')}
                                    </div>
                                    <div className="text-xs text-gray-600">
                                        {format(new Date(item.tarih), 'dd.MM.yyyy')}
                                    </div>
                                </td>
                                <td className="py-3 px-4 text-sm text-gray-700 font-mono">{item.stokKodu}</td>
                                <td className="py-3 px-4 text-sm text-gray-700 font-medium">{urunAdlari[item.stokKodu] || getUrunAdiByStokkodu(item.stokKodu) || ''}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{item.boruAg} kg</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{item.durum || ''}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{item.donusum_tarihi ? format(new Date(item.donusum_tarihi), 'dd.MM.yyyy HH:mm') : ''}</td>
                                <td className="py-3 px-4 text-sm text-gray-700">{item.donusum_id ?? ''}</td>
                                <td className="py-3 px-4">
                                    <div className="flex space-x-2">
                                        <button
                                            className="p-1 text-purple-600 hover:bg-purple-50 rounded-full w-8 h-8 flex items-center justify-center transition-colors"
                                            onClick={() => handleEditStart(item)}
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </button>
                                        <button
                                            className="p-1 text-red-600 hover:bg-red-50 rounded-full w-8 h-8 flex items-center justify-center transition-colors"
                                            onClick={() => setItemToDelete(item)}
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                        {siloDataList.length === 0 && (
                            <tr>
                                <td colSpan={6} className="py-8 text-center text-gray-500 italic">
                                    Veri bulunamadı
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Silme Onay Dialog */}
            <AlertDialog open={!!itemToDelete} onOpenChange={(open) => !open && setItemToDelete(null)}>
                <AlertDialogContent className="max-w-md bg-white rounded-3xl p-0 overflow-hidden">
                    <div className="bg-red-500 p-5 text-white">
                        <AlertDialogHeader>
                            <AlertDialogTitle className="text-white flex items-center text-xl">
                                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                Silme Onayı (Hat 3)
                            </AlertDialogTitle>
                        </AlertDialogHeader>
                    </div>

                    <div className="p-6">
                        <AlertDialogDescription className="text-gray-700 text-base mb-4">
                            Hat 3 - Bu kaydı silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
                        </AlertDialogDescription>

                        <div className="bg-gray-50 p-4 rounded-xl mb-4">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div className="text-gray-500">ID:</div>
                                <div className="font-medium">{itemToDelete?.id}</div>

                                <div className="text-gray-500">Ürün:</div>
                                <div className="font-medium">{itemToDelete ? (urunAdlari[itemToDelete.stokKodu] || getUrunAdiByStokkodu(itemToDelete.stokKodu) || 'Bilinmeyen') : 'Bilinmeyen'}</div>

                                <div className="text-gray-500">Tarih:</div>
                                <div className="font-medium">
                                    {itemToDelete ? format(new Date(itemToDelete.tarih), 'dd.MM.yyyy HH:mm') : ''}
                                </div>
                            </div>
                        </div>

                        <AlertDialogFooter className="flex gap-3">
                            <AlertDialogCancel className="flex-1 border-gray-300 text-gray-700 rounded-xl py-3">İptal</AlertDialogCancel>
                            <AlertDialogAction
                                className="flex-1 bg-red-500 text-white hover:bg-red-600 rounded-xl py-3"
                                onClick={() => {
                                    if (itemToDelete) {
                                        handleDeleteSiloData(itemToDelete.id);
                                        setItemToDelete(null);
                                    }
                                }}
                            >
                                Sil
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </div>
                </AlertDialogContent>
            </AlertDialog>

            {/* Ürün Bilgisini Düzenle Dialog */}
            <AlertDialog open={!!itemToUpdate} onOpenChange={(open) => !open && handleCancel()}>
                <AlertDialogContent className="max-w-md bg-white rounded-3xl p-0 overflow-hidden">
                    <div className="bg-purple-600 p-5 text-white">
                        <AlertDialogHeader>
                            <AlertDialogTitle className="text-white flex items-center text-xl">
                                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Hat 3 - Ürün Bilgisini Düzenle
                            </AlertDialogTitle>
                        </AlertDialogHeader>
                    </div>

                    <div className="p-6">
                        <div className="mb-4 px-4 py-3 bg-purple-50 rounded-xl border border-purple-100">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div className="text-gray-500">ID:</div>
                                <div className="font-medium">{itemToUpdate?.id}</div>

                                <div className="text-gray-500">Mevcut Ürün:</div>
                                <div className="font-medium">{itemToUpdate ? (urunAdlari[itemToUpdate.stokKodu] || getUrunAdiByStokkodu(itemToUpdate.stokKodu) || 'Bilinmeyen') : 'Bilinmeyen'}</div>

                                <div className="text-gray-500">Stok Kodu:</div>
                                <div className="font-medium font-mono">{itemToUpdate?.stokKodu || ''}</div>
                            </div>
                        </div>

                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <div className="font-medium text-sm text-gray-700 mb-3">
                                    Yeni Ürün Seçimi
                                </div>
                                <Select value={secilenUrun} onValueChange={handleUrunChange}>
                                    <SelectTrigger className="w-full rounded-xl border border-gray-300 bg-white py-3">
                                        <SelectValue placeholder="Ürün seçin" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {StaticUrunListesi.map(urun => (
                                            <SelectItem key={urun.stokKodu} value={urun.stokKodu}>
                                                {urun.ad}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <AlertDialogFooter className="flex gap-3 mt-4">
                            <AlertDialogCancel
                                onClick={handleCancel}
                                className="flex-1 border-gray-300 text-gray-700 rounded-xl py-3"
                            >
                                İptal
                            </AlertDialogCancel>
                            <AlertDialogAction
                                onClick={handleUpdateComplete}
                                disabled={!secilenUrun}
                                className={`flex-1 rounded-xl py-3 ${!secilenUrun
                                    ? "bg-gray-300 cursor-not-allowed text-gray-600"
                                    : "bg-purple-600 hover:bg-purple-700 text-white"}`}
                            >
                                Kaydet
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </Card>
    );
};

export default SiloDataTable3; 